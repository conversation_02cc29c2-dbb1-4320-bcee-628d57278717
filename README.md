# Chat. Redact

Chat. Redact is a cutting-edge web application designed to provide secure and seamless chat experiences. Powered by **WealthSpace** technology, it enables organizations to harness the full potential of AI without compromising data confidentiality.

## 📦 Installation & Setup

### 1️⃣ Clone the Repository

```sh
git clone [REPO_URL]
cd [DIRECTORY_NAME]
```

### 2️⃣ Install Dependencies

Ensure you have **Node.js** (>=18) installed, then run:

```sh
npm install
```

### 3️⃣ Configure Environment Variables

Copy `.env.example` to `.env`:

```sh
cp .env.example .env
```

Then open `.env` and fill in the required values:

```ini
VITE_APP_URL=
VITE_API_URL=
VITE_CHAT_API_URL=
VITE_WEBSOCKET_URL=
VITE_AUTH0_CLIENT_ID=
VITE_AUTH0_DOMAIN=
VITE_AUTH0_LOGIN_REDIRECT_URI=
VITE_AUTH0_LOGIN_RESPONSE_TYPE=
VITE_AUTH0_REALM=
VITE_AUTH0_SCOPE=
```

---

## 🛠 Development

To start the development server:

```sh
npm run start
```

It will be available at **http://localhost:3000**.

---

## 🔨 Building for Production

To create a production-ready build:

```sh
npm run build
```

For staging:

```sh
npm run build:staging
```

For production:

```sh
npm run build:production
```

To preview the built files:

```sh
npm run preview
```

---

## 🧹 Code Quality

### **Linting & Formatting**

Run **ESLint** to check for issues:

```sh
npm run lint
```

Format code with **Prettier**:

```sh
npm run format
```

---

## 📜 Tech Stack

- **Frontend**: React, React Router, Zustand, React Query(TanStack), Bootstrap Sass, React Bootstrap
- **Auth & Security**: Auth0, Rollbar
- **Networking**: Axios, WebSockets (Socket.io)
- **Forms & Validation**: Formik, Yup
- **Markdown & Syntax**: React Markdown, Marked, Syntax Highlighter
- **Formatting & Linting**: Prettier, ESLint
- **Payments**: Stripe
- **Build & Dev Tools**: Vite, TypeScript
