.insert-section-modal {
  .modal-dialog {
    @media only screen and (min-width: 992px) {
      width: 800px;
      max-width: 800px;
    }
  }

  .modal {
    &-body {
      .dropdown {
        .dropdown-toggle {
          padding: 0px 20px 0px 20px;
          border-radius: 5px;
          background: transparent;
          font-size: 18px;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 10px;
          color: #0d3149;
          border: 1px solid #0d3149;

          &:focus,
          &:active {
            box-shadow: none;
            outline: none;
          }

          &::after {
            transition: transform 0.2s ease-in-out;
          }

          &.show::after {
            transform: rotate(-180deg);
            transition: transform 0.2s ease-in-out;
          }
        }

        .dropdown-menu {
          border-radius: 10px;
          background: #ffffff;
          border: none;
          padding: 10px 0;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

          .dropdown-item {
            font-size: 16px;
            padding: 10px 20px;
            color: #0d3149;
            border-radius: 8px;

            &:hover {
              background: rgba(173, 152, 111, 0.2);
            }
          }
        }
      }
    }
  }
}
