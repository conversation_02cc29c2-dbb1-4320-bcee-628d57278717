import { TempNewChatInterface } from "types";
import { create } from "zustand";

interface PayloadInterface {
  tempNewChat: TempNewChatInterface;
  messageHistory: any;
}

const initialState = {
  tempNewChat: {
    chat_id: "",
    created_at: "",
    text: "",
  },
  messageHistory: [],
  isDocDeleted: false,
  savedFiles: [],
};

const useChatStore = create((set: any) => ({
  ...initialState,
  setTempNewChat: (data: TempNewChatInterface | null) =>
    set((state: PayloadInterface) => ({ ...state, tempNewChat: data })),
  setMessageHistory: (dataOrUpdater: any) =>
    set((state: PayloadInterface) => {
      const newData =
        typeof dataOrUpdater === "function"
          ? dataOrUpdater(state.messageHistory)
          : dataOrUpdater;

      return { ...state, messageHistory: newData };
    }),
  setIsDocDeleted: (data: boolean) =>
    set((state: PayloadInterface) => ({ ...state, isDocDeleted: data })),
  setSavedFiles: (data: any[]) =>
    set((state: PayloadInterface) => ({ ...state, savedFiles: data })),
}));

export const getMessageHistory = () => {
  return useChatStore.getState().messageHistory;
};

export const {
  setTempNewChat,
  setMessageHistory,
  setIsDocDeleted,
  setSavedFiles,
} = useChatStore.getState();

export default useChatStore;
