import React from "react";
import "./styles.scss";

interface DefaultProfileProps {
  text: string | undefined;
  className?: string;
}

const DefaultProfile: React.FC<DefaultProfileProps> = ({ text, className }) => {
  return (
    <p
      className={`default-text-image rounded-circle mb-0 font-light fw-bold d-flex justify-content-center align-items-center ${className}`}
    >
      {text}
    </p>
  );
};

export default DefaultProfile;
