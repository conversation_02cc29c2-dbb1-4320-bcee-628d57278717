import { RiArrowLeftSLine, RiArrowRightSLine } from "@remixicon/react";
import { useCustomCarousel } from "hooks/useCustomCarousel";
import { Button } from "react-bootstrap";
import "./styles.scss";

const CustomCarousel = ({ children, className }: any) => {
  const {
    scrollRef,
    canScrollLeft,
    canScrollRight,
    scrollLeft,
    scrollRight,
    updateScrollState,
  } = useCustomCarousel();

  return (
    <div className="horizontal-scroller">
      <Button
        onClick={scrollLeft}
        className="scroll-button left"
        variant=""
        disabled={!canScrollLeft}
      >
        <RiArrowLeftSLine className="arrow-icon" />
      </Button>
      <div
        className={`scroll-container ${className}`}
        ref={scrollRef}
        onScroll={updateScrollState}
      >
        {children}
      </div>
      <Button
        onClick={scrollRight}
        className="scroll-button right"
        variant=""
        disabled={!canScrollRight}
      >
        <RiArrowRightSLine className="arrow-icon" />
      </Button>
    </div>
  );
};

export default CustomCarousel;
