import { RiCloseLine } from "@remixicon/react";
import { useDeleteSelectDocument } from "api";
import { DOC_TYPE_INFO } from "globals";
import { Button, Image, ProgressBar } from "react-bootstrap";
import { useParams } from "react-router-dom";
import "./styles.scss";

const FilePreview = ({
  selectedFile,
  handleRemoveFile,
  selectedDocID,
  uploadDocChatId,
  uploadProgress,
}: any) => {
  const { id: chat_id } = useParams();

  const { mutateAsync: deleteDocument } = useDeleteSelectDocument();

  const handleClickRemove = async () => {
    if (selectedFile?.type.startsWith("image/")) {
      handleRemoveFile();
      return;
    }
    try {
      const payload = {
        doc_id: selectedDocID,
        chat_id: chat_id ?? uploadDocChatId,
      };
      handleRemoveFile();
      await deleteDocument(payload);
    } catch (err: any) {
      console.log(err);
    }
  };

  return (
    <>
      {selectedFile && (
        <div className="form-file-output-wrapper rounded-3 w-100 mx-3">
          <div className="form-file-output-wrapper-scrolltrack d-flex">
            {selectedFile?.type.startsWith("image/") ? (
              <div className="form-file-output-wrapper-file type-image d-flex justify-content-start align-items-center position-relative p-2">
                <Button
                  variant="Link"
                  className="m-0 p-0 remove-btn rounded-circle position-absolute"
                  onClick={handleClickRemove}
                >
                  <RiCloseLine size={"15px"} />
                </Button>

                <Image
                  src={URL.createObjectURL(selectedFile)}
                  className="object-fit-cover"
                />

                {uploadProgress > 0 && !selectedDocID && (
                  <ProgressBar
                    striped
                    now={uploadProgress}
                    animated={uploadProgress > 98}
                    className="position-absolute progress-bar-bottom"
                  />
                )}
              </div>
            ) : (
              <div className="form-file-output-wrapper-file d-flex justify-content-start align-items-center position-relative p-2">
                {selectedDocID && (
                  <Button
                    variant="Link"
                    className="m-0 p-0 remove-btn rounded-circle position-absolute"
                    onClick={handleClickRemove}
                  >
                    <RiCloseLine size={"15px"} />
                  </Button>
                )}

                <Image
                  src={DOC_TYPE_INFO?.[selectedFile?.type]?.icon}
                  className="object-fit-contain"
                />

                <hr className="my-1 p-0 align-self-stretch opacity-100" />

                {/* <div className="d-flex flex-column gap-1 w-100"> */}
                <p className="mb-0 fw-bold text-start text-truncate">
                  {selectedFile?.name}

                  <hr className="border-0 my-1" />

                  <small className="fw-medium text-uppercase">
                    {DOC_TYPE_INFO?.[selectedFile?.type]?.type}
                  </small>
                </p>
                {uploadProgress > 0 && !selectedDocID && (
                  <ProgressBar
                    striped
                    now={uploadProgress}
                    animated={uploadProgress > 98}
                    className="position-absolute progress-bar-bottom"
                  />
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default FilePreview;
