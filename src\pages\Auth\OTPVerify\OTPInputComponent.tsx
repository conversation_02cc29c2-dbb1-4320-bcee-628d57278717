import React from "react";
import OTPInput from "react-otp-input";

interface OTPInputComponentProps {
  otp: string;
  setOTP: (otp: string) => void;
  message: string | null;
  error: string | null;
  setError?: any;
}

const OTPInputComponent: React.FC<OTPInputComponentProps> = ({
  otp,
  setOTP,
  message,
  error,
  setError,
}) => {
  return (
    <div className="position-relative">
      <OTPInput
        value={otp}
        onChange={(value) => {
          setOTP(value);
          setError(null);
        }}
        inputType="number"
        numInputs={4}
        containerStyle="otp-container"
        renderInput={(props: any, idx: any) => (
          <div className="form-group position-relative">
            <input
              {...props}
              className="text-center form-control"
              autoFocus={idx === 0}
            />
          </div>
        )}
      />
      {message && <div className="f-error text-success">{message}</div>}
      {error && <span className="f-error text-danger">{error}</span>}
    </div>
  );
};

export default OTPInputComponent;
