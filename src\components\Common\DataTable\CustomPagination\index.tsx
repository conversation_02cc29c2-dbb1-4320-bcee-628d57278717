import {
  RiArrowLeftCircleFill,
  RiArrowRightCircleFill,
} from "@remixicon/react";
import React from "react";
import { Button, Form } from "react-bootstrap";
import "./styles.scss";

interface CustomPaginationProps {
  paginationConfig: Record<string, any>;
  setPaginationConfig: any;
  totalCount: any;
}

const CustomPagination: React.FC<CustomPaginationProps> = ({
  paginationConfig = {
    page: 1,
    limit: 25,
  },
  setPaginationConfig = () => {},
  totalCount = 0,
}) => {
  const { page, limit } = paginationConfig || {};
  const totalPages = Math.ceil(totalCount / limit);

  const handleLimitPerPageChange = (evt: any) => {
    setPaginationConfig({
      ...paginationConfig,
      limit: evt.target.value,
    });
  };

  const handlePageChange = (type: string) => {
    if (type === "prev") {
      setPaginationConfig({
        ...paginationConfig,
        page: page - 1,
      });
    } else {
      setPaginationConfig({
        ...paginationConfig,
        page: page + 1,
      });
    }
  };

  return (
    <div className="datatable-pagination bg-white position-sticky">
      <hr className="mt-0 mb-2 p-0 border-0 w-100 d-block" />

      <div
        className="d-flex flex-sm-row flex-column align-items-center justify-content-end"
        style={{ gap: "20px" }}
      >
        <div
          className="show-entries d-flex align-items-center"
          style={{ gap: "10px" }}
        >
          <Form.Label className="d-block m-0 fw-bold">Show Entries</Form.Label>
          <Form action="#!">
            <Form.Select
              className="m-0"
              value={limit}
              onChange={handleLimitPerPageChange}
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </Form.Select>
          </Form>
        </div>

        <div className="d-flex align-items-center" style={{ gap: "20px" }}>
          <div className="current-entry">
            <p className="mb-0">
              <span>{(page - 1) * limit + 1}</span>
              <span className="mx-1">-</span>
              <span>{Math.min(page * limit, totalCount)}</span>
              <span className="mx-2">of</span>
              <span>{totalCount}</span>
            </p>
          </div>

          <div
            className="pagination d-flex align-items-center"
            style={{ gap: "20px" }}
          >
            <Button
              variant="Link"
              className="m-0 p-0 border-0"
              disabled={page === 1}
              onClick={() => handlePageChange("prev")}
            >
              <RiArrowLeftCircleFill size={"25px"} />
            </Button>

            <Button
              variant="Link"
              className="m-0 p-0 border-0"
              disabled={page === totalPages || totalPages === 0}
              onClick={() => handlePageChange("next")}
            >
              <RiArrowRightCircleFill size={"25px"} />
            </Button>
          </div>
        </div>
      </div>

      <hr className="mb-0 mt-2 p-0 border-0 w-100 d-block" />
    </div>
  );
};

export default CustomPagination;
