import { RiCheckboxCircleFill } from "@remixicon/react";
import { BackButton } from "components";
import { SUCCESS_PAGE_CONTENT } from "globals";
import parse from "html-react-parser";
import { Card, Col, Container, Image, Row, Spinner } from "react-bootstrap";
import { ROUTE_PATH } from "routes";
import { SuccessPageContent } from "types";
import "./styles.scss";

interface HomeSuccessProps {
  type: keyof SuccessPageContent;
}

const HomeSuccess: React.FC<HomeSuccessProps> = ({ type }: any) => {
  const content = SUCCESS_PAGE_CONTENT[type];
  if (!content) {
    return null;
  }

  const {
    heading,
    subheading,
    description,
    imagePath,
    useIcon,
    btnText = "Back To Home",
    redirectURL = ROUTE_PATH.HOME,
    hideBtn = false,
  } = content;

  return (
    <main className="home-success-section d-flex bg-white flex-column align-items-stretch w-100">
      <div className="home-success-section-container">
        <Container fluid>
          <Row className="justify-content-center align-items-center">
            <Col lg="9" xxl="7">
              <div className="w-100 m-0">
                <Card className="auth-form-card justify-content-center align-items-center">
                  <Card.Body className="d-flex gap-4 flex-column align-items-center justify-content-center">
                    <div
                      className="auth-form d-flex justify-content-center align-items-center flex-column w-100"
                      style={{ gap: "30px" }}
                    >
                      <div
                        className="d-flex flex-column"
                        style={{ gap: "23px" }}
                      >
                        <div className="text-center">
                          {["LOADING", "PLEASE_WAIT"].includes(type) ? (
                            <Spinner />
                          ) : (
                            <>
                              {useIcon ? (
                                <RiCheckboxCircleFill
                                  size={"150px"}
                                  color="#ad986f"
                                />
                              ) : (
                                <Image
                                  src={imagePath}
                                  style={{ width: "120px" }}
                                />
                              )}
                            </>
                          )}
                        </div>

                        <h1 className="auth-form-heading text-uppercase mb-0 text-center">
                          {heading}
                          <br />
                          {parse(subheading)}
                        </h1>

                        <p className="mb-0 auth-form-description font-gray text-center">
                          {parse(description)}
                        </p>
                      </div>

                      {!hideBtn && type !== "LOADING" && (
                        <div className="website-form">
                          <div
                            className="action-btns d-flex flex-column"
                            style={{ gap: "30px" }}
                          >
                            <BackButton title={btnText} url={redirectURL} />
                          </div>
                        </div>
                      )}
                    </div>
                  </Card.Body>
                </Card>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </main>
  );
};

export default HomeSuccess;
