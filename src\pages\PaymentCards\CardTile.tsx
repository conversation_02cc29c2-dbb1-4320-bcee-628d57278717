import { RiDeleteBinLine } from "@remixicon/react";
import { paymentCardsConfig } from "globals";
import { useDeleteCard, useSetDefaultCard } from "hooks";
import { Button, Card } from "react-bootstrap";
import { generateNickName } from "utils";

const CardTile = ({
  cardInfo = {},
  setFilteredCards,
  defaultCard,
  setDefaultCard,
  isMultipleCards,
}: any) => {
  const { card, billing_details } = cardInfo;
  const { onClickDelete } = useDeleteCard({ setFilteredCards });
  const { onClickSetDefault } = useSetDefaultCard({ setDefaultCard });

  const isDefaultCard = defaultCard === cardInfo?.id;
  const disableDelete = isDefaultCard || !isMultipleCards;

  return (
    <li className="d-flex flex-md-row flex-column justify-content-between align-items-lg-center align-items-start position-relative">
      <div
        className="card-data mb-lg-0 mb-2 d-flex justify-content-start align-items-center"
        style={{ gap: "15px" }}
      >
        {paymentCardsConfig?.[card?.brand] ? (
          <Card className="payment-card text-center border-0 shadow-lg">
            <Card.Body className="d-flex align-items-center justify-content-center flex-column">
              <Card.Img
                src={paymentCardsConfig?.[card?.brand]}
                alt={card?.brand || "Payment Card"}
                className="card-data-image rounded-2"
              />
            </Card.Body>
          </Card>
        ) : (
          <div className="card-data-nickname rounded-2 fw-bold text-uppercase fs-4 text-center bg-brown font-light">
            {generateNickName(card?.brand)}
          </div>
        )}

        <div className="card-data-details">
          <p className="mb-0 name fw-bold text-capitalize">
            {/* {card?.brand} {getExpiryDuration(card?.exp_month, card?.exp_year)}
            <br />
            <small className="font-gray fw-medium d-inline-block">
              Exp. Date {card?.exp_month}/{card?.exp_year}
            </small> */}
            {billing_details?.name ? billing_details.name : card?.brand}
            <br />
            <small className="font-gray fw-medium d-inline-block">
              Exp. Date {card?.exp_month}/{card?.exp_year}
            </small>
          </p>
        </div>
      </div>

      {isDefaultCard ? (
        <p className="card-status-label default text-center mb-0 d-flex justify-content-center align-items-center font-gray rounded-pill">
          Default
        </p>
      ) : (
        <p
          className="card-status-label action-btn text-center mb-0 d-flex justify-content-center align-items-center font-gray rounded-pill"
          style={{ cursor: "pointer" }}
          onClick={() => onClickSetDefault(cardInfo?.id)}
        >
          Set as Default
        </p>
      )}

      <div
        className={`d-lg-block d-flex justify-content-center align-items-center ${
          disableDelete ? "invisible" : ""
        }`}
      >
        <Button
          variant="Link"
          className="border-0"
          onClick={(evt) => onClickDelete(evt, cardInfo?.id)}
        >
          <RiDeleteBinLine className="text-danger" size={20} />
        </Button>
      </div>
    </li>
  );
};

export default CardTile;
