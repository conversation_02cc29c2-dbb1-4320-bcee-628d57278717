import React from "react";
import { Spinner } from "react-bootstrap";
import "./styles.scss";

interface LoadingOverlayProps {
  show: boolean;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ show }) => {
  return (
    <>
      {show && (
        <div className="spinner-wrapper position-fixed top-0 bottom-0 start-0 end-0 w-100 h-100 d-flex justify-content-center align-items-center">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
        </div>
      )}
    </>
  );
};

export default LoadingOverlay;
