import { useResendOTPEmailMutation, useResendOTPMutation } from "api";
import { useEffect, useState } from "react";
import { Button } from "react-bootstrap";

interface ResendOTPButtonProps {
  otpId: string | null;
  setMessage: (message: any) => void;
  setOTP: (otp: any) => void;
  type: string;
  email: string | null;
}

const ResendOTP: React.FC<ResendOTPButtonProps> = ({
  otpId,
  setMessage,
  setOTP,
  type,
  email,
}) => {
  const { mutateAsync: resendOTPMutation } = useResendOTPMutation();
  const { mutateAsync: resendOTPEmailMutation } = useResendOTPEmailMutation();

  const [timer, setTimer] = useState(30);
  const [isTimerRunning, setIsTimerRunning] = useState(false);

  const handleRequestOtp = async () => {
    let payload: any = {
      id: otpId,
    };
    let resendOTP: any = resendOTPMutation;

    if (type === "signUp") {
      payload = {
        email,
      };
      resendOTP = resendOTPEmailMutation;
    }
    resendOTP(payload).then((result: any) => {
      if (result?.success) {
        setOTP(null);
        setTimer(30);
        setIsTimerRunning(true);
        setMessage(result?.message);
        setTimeout(() => setMessage(null), 2000);
      } else {
        console.error(result?.message);
      }
    });
  };

  useEffect(() => {
    let countdown: any = null;

    if (isTimerRunning) {
      countdown = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
    }

    return () => clearInterval(countdown);
  }, [isTimerRunning]);

  useEffect(() => {
    if (timer === 0) {
      setIsTimerRunning(false);
    }
  }, [timer]);

  return (
    <>
      Didn't receive code?
      <Button
        variant="link"
        style={{ color: "#60A799" }}
        onClick={() => {
          handleRequestOtp();
        }}
        disabled={isTimerRunning}
        className="mb-0 p-0 fw-bold text-decoration-none ms-1"
      >
        {isTimerRunning ? `Wait for ${timer} seconds` : "Resend OTP"}
      </Button>
    </>
  );
};

export default ResendOTP;
