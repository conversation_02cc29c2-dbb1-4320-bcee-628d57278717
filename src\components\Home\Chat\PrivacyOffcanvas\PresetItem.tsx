import { HoverTooltip } from "components/Common";
import { PRESET_OPTIONS } from "globals";
import { useState } from "react";
import { Accordion, Form } from "react-bootstrap";

const PresetItem = ({
  setLocalConfiguration,
  localConfiguration,
  activePreset,
  setActivePreset,
  isCustomAvailable,
}: any) => {
  const [expanded, setExpanded] = useState<string | null>(null);

  const { enable_privacy: isPrivacyEnabled = false } = localConfiguration ?? {};

  const handleToggle = (currentKey: string) => {
    setExpanded(expanded === currentKey ? null : currentKey);
  };

  const handleClickPreset = (option: string, isSelected: boolean) => {
    if (!isPrivacyEnabled) {
      return;
    }
    setActivePreset(option);
    const availableEntities = PRESET_OPTIONS[option].entities();

    if (isSelected) return;
    let newPreset: any = [];
    if (
      [PRESET_OPTIONS.custom1.key, PRESET_OPTIONS.custom2.key].includes(option)
    ) {
      if (isCustomAvailable) {
        newPreset = [
          ...localConfiguration.preset,
          {
            type: option,
            entity_values: availableEntities,
          },
        ];
      } else {
        newPreset = [
          {
            type: option,
            entity_values: availableEntities,
          },
        ];
      }
    } else {
      newPreset = [
        {
          type: option,
          entity_values: availableEntities,
        },
      ];
    }
    setLocalConfiguration((prev: any) => ({
      ...prev,
      preset: newPreset,
    }));
  };

  const handleRemovePreset = (evt: any, option: string) => {
    evt.stopPropagation();
    const newPreset = localConfiguration?.preset?.filter(
      (item: any) => item?.type !== option,
    );
    setLocalConfiguration((prev: any) => ({
      ...prev,
      preset: newPreset,
    }));
    setActivePreset("");
  };

  return (
    <Accordion.Item
      eventKey="preset"
      className="bg-transparent coming-soon-features"
    >
      <Accordion.Header
        className="without-switch position-relative"
        onClick={() => handleToggle("preset")}
      >
        Presets
        <p className="mb-0 coming-soon-tag text-center d-flex justify-content-center align-items-center text-capitalize position-absolute">
          Coming Soon
        </p>
      </Accordion.Header>

      <Accordion.Body>
        <div className="option-check-list d-flex flex-column">
          <Form
            className="d-flex flex-sm-row flex-column"
            style={{ gap: "10px" }}
          >
            {Object.keys(PRESET_OPTIONS).map((option: any, index: any) => {
              const isSelected = localConfiguration?.preset?.some(
                (item: any) => item?.type === option,
              );
              return (
                <HoverTooltip
                  customClass="tooltip-custom-width"
                  title={PRESET_OPTIONS[option]?.tooltipContent?.title}
                  description={
                    PRESET_OPTIONS[option]?.tooltipContent?.description
                  }
                  key={index}
                >
                  <span
                    // className={`d-block btn text-center text-capitalize option-button ${activePreset === option ? "selected border-0" : ""} non-check-tick position-relative ${isPrivacyEnabled ? "" : "opacity-50"}`}
                    className="d-block btn text-center text-capitalize option-button non-check-tick position-relative opacity-50"
                    key={index}
                    // onClick={() => handleClickPreset(option, isSelected)}
                    style={{
                      position: "relative",
                    }}
                  >
                    {/* {isPrivacyEnabled &&
                      isSelected &&
                      [
                        PRESET_OPTIONS.custom1.key,
                        PRESET_OPTIONS.custom2.key,
                      ].includes(option) && (
                        <Button
                          className="preset-remove-btn position-absolute bg-white border-0 p-0 rounded-circle lh-1 text-center"
                          onClick={(evt) => handleRemovePreset(evt, option)}
                        >
                          <RiCloseCircleLine className="w-100 h-100" />
                        </Button>
                      )} */}
                    {PRESET_OPTIONS[option].title}
                  </span>
                </HoverTooltip>
              );
            })}
          </Form>
        </div>
      </Accordion.Body>
    </Accordion.Item>
  );
};

export default PresetItem;
