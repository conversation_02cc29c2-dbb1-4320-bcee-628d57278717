import { getEntitiesBasedOnPreset } from "stores";

export const PRESET_OPTIONS: any = {
  basic: {
    key: "basic",
    title: "Basic",
    tooltipContent: {
      title: "Basic",
      description:
        "Redact and synthesise Name and Address. Thereby anonymising the data set.<br/><br/>This setting will give you the most refined responses from the AI engine, whilst anonymising the data.",
    },
    entities: () => getEntitiesBasedOnPreset("basic"),
  },
  full: {
    key: "full",
    title: "Full",
    tooltipContent: {
      title: "Full",
      description:
        "Redact and synthesise all 50+ PII filters.<br/><br/>This setting is the most secure but may return less specific responses.<br/><br/>For example, it will redact medical conditions, therefore if you asked for a prognosis, the Al model is unable to give you a reasoned answer.",
    },
    entities: () => getEntitiesBasedOnPreset("full"),
  },
  custom1: {
    key: "custom1",
    title: "Custom 1",
    tooltipContent: {
      title: "Custom 1",
      description:
        "Create your own set of custom PII filters.<br/><br/> Chose which of the 50+ filters you would like selected, and press save at the bottom right of the screen.",
    },
    entities: () => getEntitiesBasedOnPreset("full"),
  },
  custom2: {
    key: "custom2",
    title: "Custom 2",
    tooltipContent: {
      title: "Custom 2",
      description:
        "Create your own set of custom PII filters.<br/><br/> Chose which of the 50+ filters you would like selected, and press save at the bottom right of the screen.",
    },
    entities: () => getEntitiesBasedOnPreset("full"),
  },
};
