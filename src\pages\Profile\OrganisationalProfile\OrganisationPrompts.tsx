import { Prompts } from "components";
import { Form } from "react-bootstrap";

export default function OrganisationPrompts({
  orgConfig,
  setOrgConfig,
  organisation,
  onSavePrompt,
  onResetPrompt,
  onLocalReset,
  getLocalPrompts,
}: any) {
  return (
    <>
      <div className="form-group position-relative">
        <div className="d-flex justify-content-between align-items-center">
          <label className="form-label fw-bold">Organisation Prompts</label>
          <Form.Switch
            checked={orgConfig.show_prompt}
            onChange={(e) =>
              setOrgConfig({
                ...orgConfig,
                show_prompt: e.target.checked,
              })
            }
          />
        </div>
      </div>
      <div>
        <Prompts
          isLoading={false}
          prompts={
            organisation?.prompts?.length
              ? [
                  ...organisation.prompts,
                  ...getLocalPrompts().slice(organisation?.prompts.length),
                ]
              : getLocalPrompts()
          }
          allStoredPrompts={organisation?.prompts || []}
          customStyling={{
            position: "unset",
            paddingBottom: "20px",
          }}
          onSavePrompt={onSavePrompt}
          onResetPrompt={onResetPrompt}
          promptConfig={{
            isPromptEnabled: false,
            isPromptEditable: true,
          }}
          onLocalReset={organisation?.id ? null : onLocalReset}
        />
      </div>
    </>
  );
}
