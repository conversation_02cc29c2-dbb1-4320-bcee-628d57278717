import { Editor } from "@tiptap/react";
import { useCallback, useEffect, useState } from "react";

interface UseSectionLockProps {
  editor: Editor | null;
  sectionIndex: number;
  isSectionLocked?: boolean;
  onSectionLockChange?: (index: number, isLocked: boolean) => void;
}

interface LockState {
  isFullyLocked: boolean;
  isPartiallyLocked: boolean;
  lockedContentCount: number;
  totalContentCount: number;
}

export const useSectionLock = ({
  editor,
  sectionIndex,
  isSectionLocked = false,
  onSectionLockChange,
}: UseSectionLockProps) => {
  const [lockState, setLockState] = useState<LockState>({
    isFullyLocked: false,
    isPartiallyLocked: false,
    lockedContentCount: 0,
    totalContentCount: 0,
  });

  const analyzeLockState = useCallback(() => {
    if (!editor) return;

    const doc = editor.state.doc;
    let lockedCount = 0;
    let totalTextNodes = 0;

    // Traverse the document to count locked and total text nodes
    doc.descendants((node, pos) => {
      if (node.isText && node.text && node.text.trim()) {
        totalTextNodes++;

        // Check if this text node is within a locked mark
        const marks = node.marks;
        const hasLockMark = marks.some(
          (mark) => mark.type.name === "contentLock",
        );

        if (hasLockMark) {
          lockedCount++;
        }
      }
      return true;
    });

    const isFullyLocked = totalTextNodes > 0 && lockedCount === totalTextNodes;
    const isPartiallyLocked = lockedCount > 0 && lockedCount < totalTextNodes;

    setLockState({
      isFullyLocked,
      isPartiallyLocked,
      lockedContentCount: lockedCount,
      totalContentCount: totalTextNodes,
    });
  }, [editor]);

  const toggleSectionLock = useCallback(() => {
    if (!editor) return;

    const { isFullyLocked } = lockState;

    if (isFullyLocked || isSectionLocked) {
      // Unlock all content
      editor.chain().focus().selectAll().unsetContentLock().run();
      // Update section lock state
      if (onSectionLockChange) {
        onSectionLockChange(sectionIndex, false);
      }
    } else {
      // Lock all content
      editor.chain().focus().selectAll().setContentLock().run();
      // Update section lock state
      if (onSectionLockChange) {
        onSectionLockChange(sectionIndex, true);
      }
    }

    // Re-analyze after a short delay to ensure the changes are applied
    setTimeout(analyzeLockState, 100);
  }, [
    editor,
    lockState.isFullyLocked,
    isSectionLocked,
    analyzeLockState,
    sectionIndex,
    onSectionLockChange,
  ]);

  const lockSelectedContent = useCallback(() => {
    if (!editor) return;

    const { selection } = editor.state;
    if (selection.empty) return;

    editor.chain().focus().setContentLock().run();
    setTimeout(analyzeLockState, 100);
  }, [editor, analyzeLockState]);

  const unlockSelectedContent = useCallback(() => {
    if (!editor) return;

    const { selection } = editor.state;
    if (selection.empty) return;

    editor.chain().focus().unsetContentLock().run();
    setTimeout(analyzeLockState, 100);
  }, [editor, analyzeLockState]);

  // Listen to editor changes
  useEffect(() => {
    if (!editor) return;

    const handleUpdate = () => {
      analyzeLockState();
    };

    editor.on("transaction", handleUpdate);
    editor.on("update", handleUpdate);

    // Initial analysis
    analyzeLockState();

    return () => {
      editor.off("transaction", handleUpdate);
      editor.off("update", handleUpdate);
    };
  }, [editor, analyzeLockState]);

  const getLockedContentSummary = useCallback(() => {
    if (!editor) return null;

    const doc = editor.state.doc;
    const lockedContent: string[] = [];

    doc.descendants((node, pos) => {
      if (node.isText && node.text && node.text.trim()) {
        const marks = node.marks;
        const hasLockMark = marks.some(
          (mark) => mark.type.name === "contentLock",
        );

        if (hasLockMark) {
          lockedContent.push(node.text.trim());
        }
      }
      return true;
    });

    return {
      lockedTexts: lockedContent,
      count: lockedContent.length,
    };
  }, [editor]);

  return {
    lockState,
    toggleSectionLock,
    lockSelectedContent,
    unlockSelectedContent,
    analyzeLockState,
    getLockedContentSummary,
  };
};
