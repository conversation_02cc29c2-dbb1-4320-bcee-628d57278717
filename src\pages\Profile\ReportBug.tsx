import { RiAttachment2 } from "@remixicon/react";
import { useReportBugMutation } from "api";
import { BackBreadcrumb } from "components";
import { ErrorMessage, Field, Form, Formik, FormikProps } from "formik";
import { ReportBugInitialValues } from "formSchema/initialValues";
import { ReportBugValidations } from "formSchema/schemaValidations";
import { <PERSON><PERSON>, Card, Col, Container, Row, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";
import { convertObjToFormData, isValidFileSize, isValidFileType } from "utils";
import "./styles.scss";

const ReportBug = () => {
  const { mutateAsync: reportBug } = useReportBugMutation();

  const handleSubmit = async (
    values: any,
    { setSubmitting, resetForm }: any,
  ) => {
    const payload = convertObjToFormData(values);
    try {
      const response: any = await reportBug(payload);
      if (response?.success) {
        toast.success(response?.message);
        resetForm();
      }
    } catch (error: any) {
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    setFieldValue: any,
  ) => {
    const file = event.target.files && event.target.files[0];
    if (file) {
      if (!isValidFileType(file) || !isValidFileSize(file)) {
        return;
      }
      setFieldValue("attachment", file);
    }
  };

  return (
    <main className="profile-section d-flex bg-white flex-column align-items-stretch w-100">
      <BackBreadcrumb />
      <div className="profile-section-form-container">
        <Container fluid>
          <Row className="justify-content-center align-items-center">
            <Col lg="9" xxl="7">
              <div className="auth-form w-100 m-0">
                <Card className="auth-form-card justify-content-center aligh-items-center">
                  <Card.Body className="d-flex gap-4 flex-column align-items-center justify-content-center">
                    <div className="d-flex flex-column" style={{ gap: "10px" }}>
                      <h1 className="mb-0 text-center auth-form-heading text-uppercase fw-bold">
                        report a bug
                      </h1>
                    </div>

                    <div className="website-form w-100">
                      <Formik
                        initialValues={ReportBugInitialValues}
                        validationSchema={ReportBugValidations}
                        onSubmit={handleSubmit}
                      >
                        {({
                          isSubmitting,
                          setFieldValue,
                          values,
                        }: FormikProps<any>) => (
                          <Form
                            className="d-flex flex-column"
                            style={{ gap: "30px" }}
                          >
                            <div className="form-group position-relative">
                              <label className="form-label fw-bold">
                                Title
                              </label>
                              <Field
                                name="title"
                                className="form-control"
                                placeholder="Enter title"
                                type="text"
                              />
                              <ErrorMessage component={"span"} name="title" />
                            </div>

                            <div className="form-group position-relative">
                              <label className="form-label fw-bold">
                                Attachment (if Any)
                              </label>
                              <input
                                type="file"
                                className="d-none"
                                id="attachment"
                                onChange={(event: any) =>
                                  handleFileChange(event, setFieldValue)
                                }
                                accept=".png, .jpg, .jpeg"
                              />
                              <label
                                htmlFor="attachment"
                                className="form-control d-flex justify-content-start align-items-center"
                              >
                                <p className="mb-0 font-gray opacity-75">
                                  {values?.attachment?.name ?? "Attachment"}
                                </p>
                                <RiAttachment2 color="#70828D" />
                              </label>
                            </div>

                            <div className="form-group position-relative">
                              <label className="form-label fw-bold">
                                Description
                              </label>
                              <Field
                                name="description"
                                className="form-control pt-2"
                                placeholder="Enter description"
                                type="text"
                                as="textarea"
                              />
                              <ErrorMessage
                                component={"span"}
                                name="description"
                              />
                            </div>

                            <div
                              className="action-btns d-flex flex-column"
                              style={{ gap: "30px" }}
                            >
                              <Button
                                type="submit"
                                className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                                disabled={isSubmitting}
                              >
                                {isSubmitting ? <Spinner /> : "Submit"}
                              </Button>
                            </div>
                          </Form>
                        )}
                      </Formik>
                    </div>
                  </Card.Body>
                </Card>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </main>
  );
};

export default ReportBug;
