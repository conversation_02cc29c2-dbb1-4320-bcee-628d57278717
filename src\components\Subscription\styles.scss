@use "/src/styles/mixins/mixins.scss" as mixins;

.subscription-card {
  padding: 30px 30px;
  gap: 20px;
  border-radius: 20px;
  border: 5px solid #f0eeee80;
  box-shadow:
    0px 135.639px 108.511px 0px rgba(26, 26, 26, 0.02),
    0px 87.914px 63.549px 0px rgba(26, 26, 26, 0.01);

  @media only screen and (min-width: 1400px) and (max-width: 1599px) {
    padding: 30px 20px;
  }

  @media only screen and (min-width: 768px) {
    height: 550px;
  }

  @media only screen and (max-width: 576px) {
    padding: 30px 20px;
  }

  &:hover {
    background-color: #0d314914;
  }

  &-status {
    font-size: 25px;
    font-weight: 900;
    line-height: 1;
    margin-bottom: 0px;

    span {
      color: #60a799;
      font-size: 16px;
      font-weight: 500;
    }
  }

  &-heading {
    font-size: 23px;
    font-weight: 600;
    line-height: 1;
    margin-bottom: 10px;
  }

  &-motive-line {
    color: #60a799;
    font-size: 15px;
    font-weight: 500;
    line-height: 1;
    margin-bottom: 20px;
  }

  &-key-features {
    max-height: 250px;
    overflow-x: hidden;
    overflow-y: auto;
    gap: 5px;
    padding-right: 25px;

    @include mixins.slim-scrollbar;

    ul {
      padding: 0;
      margin: 0;
    }

    li {
      font-size: 15px;
      font-weight: 500;
      padding-left: 25px;
      list-style: none;
      position: relative;

      &:not(:last-child) {
        margin-bottom: 10px;
      }

      p {
        flex: 1;
      }

      &::before {
        content: "";
        display: block;
        position: absolute;
        width: 15px;
        height: 15px;
        background-image: url("../../assets/images/greenCheck.webp");
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center center;
        top: 5px;
        left: 0;
      }
    }

    &-icon {
      width: 30px;
      height: 30px;
      background-color: #60a799;
      font-size: 20px;
      line-height: 33px;
    }
  }

  .action-btns {
    margin-top: 30px;

    .btn-transparent {
      @include mixins.btn-transparent;
      @include mixins.button-layer-hover;

      & {
        color: #0d3149;
      }

      @media only screen and (min-width: 1400px) and (max-width: 1599px) {
        width: 100%;
      }

      &:hover {
        color: #ffffff;
      }
    }

    .selected-btn {
      background-color: #ad986f;
      color: #ffffff;
    }
  }

  .dotted-underline {
    border-bottom: 1px dashed;
  }
}
