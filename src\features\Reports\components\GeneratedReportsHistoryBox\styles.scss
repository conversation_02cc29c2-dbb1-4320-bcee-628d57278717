@use "/src/styles/mixins/mixins.scss" as mixins;

@media only screen and (max-width: 991px) {
  .chat-section .history-box {
    display: none !important;
  }
}

.history-box {
  border-radius: 12px;
  padding: 15px 15px 15px 0px;

  @media only screen and (max-width: 991px) {
    padding-top: 10px;
  }

  &-listing-wrapper {
    width: 250px;
    overflow-x: hidden;
    overflow-y: scroll;
    max-height: 100%;
    gap: 10px;
    height: calc(100% - 60px);

    @media only screen and (max-width: 991px) {
      width: 100%;
      height: calc(100% - 180px);
    }

    @include mixins.slim-scrollbar;

    &-data {
      &-time {
        padding: 0px 15px 0px 15px;
        background-color: #ffffff;
        z-index: 2;
      }

      &-list {
        padding: 0px 10px 0px 15px;
        gap: 5px;

        .list-item {
          &-wrapper {
            border-radius: 8px;

            &-delete {
              width: 30px;
              border-top-right-radius: 8px;
              border-bottom-right-radius: 8px;
              line-height: 28px;
              transition: all 0.4s ease-in-out;
              line-height: 1;

              @media only screen and (min-width: 992px) {
                opacity: 0;
              }

              &-list {
                gap: 5px;
                width: auto;
                height: auto;
                background-color: #ffffff;
                border: 1px solid #e7e7e7;
                z-index: 10;
                box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.12);
                right: 0;
                top: 40px;
                border-radius: 6px;
                padding: 10px 10px;

                svg {
                  vertical-align: text-bottom;
                }

                button,
                a {
                  font-size: 14px;
                  padding: 7px 5px;

                  &:hover {
                    background-color: #e7e7e7;
                  }
                }
              }
            }

            &-description {
              color: #8c8c8c;
              font-size: 14px;
              font-weight: 500;
              padding: 10px 10px 10px 10px;
              transition: all 0.4s ease;
              width: 90%;
              line-height: 1;
            }

            &:hover {
              background-color: #ebebeb;
              transition: all 0.4s ease-in-out;
              color: #0d3149;

              @media only screen and (min-width: 992px) {
                .list-item-wrapper-delete {
                  opacity: 1;
                }
              }
            }
          }

          &.active-chat {
            .list-item-wrapper {
              background-color: #ebebeb;
              transition: all 0.4s ease-in-out;

              &-description {
                color: #0d3149;
              }

              &-delete {
                opacity: 1;
              }
            }
          }

          &.archived-item {
            .list-item-wrapper {
              border-left: 4px solid #ad986f;
              background-color: #f5f5f5;

              &-description {
                position: relative;
                padding-left: 8px;
              }
            }
          }
        }
      }
    }

    .infinite-scroll-component__outerdiv,
    .infinite-scroll-component__outerdiv > :first-child {
      height: 100% !important;
      overflow: visible !important;
    }
  }

  @media only screen and (max-width: 991px) {
    .start-new-chat {
      position: sticky;
      top: 0px;
      z-index: 5;
    }
  }

  @keyframes slideDownFadeIn {
    0% {
      opacity: 0;
      transform: translateY(-10px);
    }

    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUpFadeIn {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }

    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .dropdown-animate-down {
    animation: slideDownFadeIn 0.1s ease-out;
  }

  .dropdown-animate-up {
    animation: slideUpFadeIn 0.1s ease-out;
  }
}
