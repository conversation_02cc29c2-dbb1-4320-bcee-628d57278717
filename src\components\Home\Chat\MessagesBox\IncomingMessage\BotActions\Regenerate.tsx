import { RiRefreshLine } from "@remixicon/react";
import { useSendMessageMutation } from "api";
import { HoverTooltip } from "components/Common";
import { useNavigate, useParams } from "react-router-dom";
import { handleSendTextMessage } from "utils";

const Regenerate = ({ messageItem }: any) => {
  const { text, metadata } = messageItem ?? {};

  const navigate = useNavigate();
  const { id: chat_id } = useParams();

  const { mutateAsync: sendMessage } = useSendMessageMutation();

  const handleRegenerate = async () => {
    await handleSendTextMessage({
      inputValue: text,
      chat_id,
      navigate,
      sendMessage,
      metadata,
    });
  };
  return (
    <HoverTooltip title="Regenerate" customClass="fw-bold">
      <span
        className="d-block text-decoration-none cursor-pointer"
        onClick={handleRegenerate}
      >
        <RiRefreshLine size={18} />
      </span>
    </HoverTooltip>
  );
};

export default Regenerate;
