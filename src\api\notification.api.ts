import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals";
import { apiClient } from "./apiClient";
import { PaginationInterface } from "types";

export const useNotificationsQuery = ({
  params,
}: Record<string, PaginationInterface>) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_NOTIFICATIONS, {
        params,
      });
      return response?.data;
    },
    queryKey: ["notifications"],
  });

export const useReadNotificationMutation = () =>
  useMutation({
    mutationFn: async (payload: Record<string, string | number>) => {
      const response = await apiClient.post(
        API_ENDPOINTS.READ_NOTIFICATION,
        payload,
      );
      return response;
    },
  });
