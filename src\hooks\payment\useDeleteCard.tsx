import { useDeleteCardMutation } from "api";
import { setConfirmModalConfig } from "stores";
import toast from "react-hot-toast";

const useHandleUserStatus = ({ setFilteredCards }: any) => {
  const { mutateAsync: deleteCard } = useDeleteCardMutation();

  const handleDeleteCard = async (id: any) => {
    try {
      const result: any = await deleteCard({ id });
      if (result?.success) {
        toast.success(result?.message || "Card Deleted Successfully!");
        setFilteredCards((prev: any) =>
          prev.filter((item: any) => item.id !== id),
        );
      }
    } catch (err: any) {
      console.log(err);
    }
  };

  const onClickDelete = (evt: any, id: number) => {
    evt.stopPropagation();
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleDeleteCard(id),
        content: {
          heading: "Delete Card",
          description: "Are you sure you want to delete this card?",
        },
      },
    });
  };

  return { onClickDelete };
};

export default useHandleUserStatus;
