import useReportStore from "features/Reports/store/report";
import { useAutoSave } from "hooks";
import HeaderBar from "./HeaderBar";
import useCheckReportSection from "./hooks/useCheckReportSection";
import SectionBlock from "./SectionBlock";
import "./styles.scss";

interface ReportBuilderProps {
  onSaveReport: any;
  children?: React.ReactNode;
  saveReportSilenty: any;
}

const ReportBuilder = ({
  onSaveReport,
  children = <></>,
  saveReportSilenty,
}: ReportBuilderProps) => {
  const reportInfo: any = useReportStore((state) => state.reportInfo);
  const { title } = reportInfo || {};
  const sections = [
    {
      id: "1623",
      report_id: "327",
      title: "Resize Table",
      content: {
        type: "html",
        content:
          `<table style="min-width:100px; width:100px; table-layout:fixed">
<colgroup><col style="min-width:25px; width:25px"/><col style="min-width:25px; width:25px"/><col style="min-width:25px; width:25px"/><col style="min-width:25px; width:25px"/></colgroup>
<thead>
<tr>
<th data-colwidth="25" style="min-width:25px; width:25px">Name</th>
<th data-colwidth="25" style="min-width:25px; width:25px">Age</th>
<th data-colwidth="25" style="min-width:25px; width:25px">Salary</th>
<th data-colwidth="25" style="min-width:25px; width:25px">Department</th>
</tr>
</thead>
<tbody>
<tr>
<td data-colwidth="25" style="min-width:25px; width:25px">Tom</td>
<td data-colwidth="25" style="min-width:25px; width:25px">25</td>
<td data-colwidth="25" style="min-width:25px; width:25px">5000</td>
<td data-colwidth="25" style="min-width:25px; width:25px">Sales</td>
</tr>
<tr>
<td data-colwidth="25" style="min-width:25px; width:25px">Max</td>
<td data-colwidth="25" style="min-width:25px; width:25px">35</td>
<td data-colwidth="25" style="min-width:25px; width:25px">7000</td>
<td data-colwidth="25" style="min-width:25px; width:25px">Accounts</td>
</tr>
</tbody>
</table>`,
      },
      is_locked: false,
      position: 3,
      created_at: "2025-09-17T14:04:38.049Z",
      updated_at: "2025-09-17T14:04:38.049Z",
      deleted_at: null,
    },
  ];
  const { isGenerateSection } = useCheckReportSection();

  const sortedSections = sections?.sort(
    (a: any, b: any) => a.position - b.position
  );

  // useAutoSave({
  //   onAutoSave: saveReportSilenty,
  //   autoSaveArgs: { saveSilently: true },
  // });

  return (
    <div className="report-builder-section bg-white h-100 w-100 rounded p-3 overflow-auto">
      <HeaderBar
        title={title}
        onSaveReport={onSaveReport}
        isGenerateSection={isGenerateSection}
      />
      {sortedSections.map((block: any, idx: number) => (
        <SectionBlock
          key={`${reportInfo.id || "new"}-${idx}-${block.id || block.position}`}
          {...block}
          index={idx}
          saveReportSilenty={saveReportSilenty}
          isGenerateSection={isGenerateSection}
        />
      ))}
      {children}
    </div>
  );
};

export default ReportBuilder;
