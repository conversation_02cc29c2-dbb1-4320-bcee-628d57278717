import { usePrivateAIEntitiesQuery, useUpdateRedactSettingMutation } from "api";
import { PRESET_OPTIONS } from "globals";
import { useEffect, useMemo, useState } from "react";
import toast from "react-hot-toast";
import { setUserInfo } from "stores";
import useUserStore, { getOrganisationInfo } from "stores/user";
import useUtilStore, {
  DEFAULT_PRESET,
  getEntitiesBasedOnPreset,
  setAllowedFileTypes,
  setAllStoredEntities,
  setEntityConfiguration,
} from "stores/util";

export const usePrivacyFilterConfig = () => {
  const entityConfiguration = useUtilStore(
    (state) => state.entityConfiguration,
  );
  const userDetails = useUserStore((state) => state.userInfo.user);
  const organisation = getOrganisationInfo();
  const userInfo = useUserStore((state) => state.userInfo);

  const [localConfiguration, setLocalConfiguration] =
    useState<any>(entityConfiguration);
  const [activePreset, setActivePreset] = useState<string>("");

  const { isAllFiltersRequired, isOnlyBasicFiltersRequired } = useMemo(
    () => ({
      isAllFiltersRequired: organisation?.settings?.all_filters,
      isOnlyBasicFiltersRequired: organisation?.settings?.basic_filters,
    }),
    [organisation],
  );

  const { data: { entities = [], allowedFileTypes = [] } = {} } =
    usePrivateAIEntitiesQuery();

  const { mutateAsync: updateRedactSettingMutation } =
    useUpdateRedactSettingMutation();

  const isCustomAvailable = localConfiguration?.preset?.some((item: any) => {
    return item?.type?.includes("custom");
  });

  useEffect(() => {
    if (allowedFileTypes?.length) {
      // const allEntities = entities.flatMap(
      //   (item: any) => item?.entities?.map((entity: any) => entity?.value) ?? []
      // );
      // setEntityConfiguration({
      //   ...entityConfiguration,
      //   entity_values: allEntities,
      // });
      setAllowedFileTypes(allowedFileTypes);
    }
  }, [allowedFileTypes]);

  useEffect(() => {
    if (entities?.length) {
      const updateConfiguration = (presetKey: any) => {
        const presetEntities = getEntitiesBasedOnPreset(presetKey, entities);
        const newConfiguration = {
          ...localConfiguration,
          preset: [{ entity_values: presetEntities, type: DEFAULT_PRESET }],
        };
        setLocalConfiguration(newConfiguration);
        setEntityConfiguration(newConfiguration);
        return newConfiguration;
      };

      if (isAllFiltersRequired) {
        updateConfiguration(PRESET_OPTIONS.full.key);
      } else if (isOnlyBasicFiltersRequired) {
        updateConfiguration(PRESET_OPTIONS.basic.key);
      } else if (!userDetails?.redact_setting) {
        updateConfiguration(DEFAULT_PRESET);
      } else {
        setLocalConfiguration(userDetails?.redact_setting);
        setEntityConfiguration(userDetails?.redact_setting);
      }
      setAllStoredEntities(entities);
    }
  }, [entities?.length, isAllFiltersRequired, isOnlyBasicFiltersRequired]);

  const handleSave = async () => {
    try {
      const result: any = await updateRedactSettingMutation(localConfiguration);
      if (result?.success) {
        toast.success(result?.message);
        setUserInfo({
          ...userInfo,
          user: {
            ...userInfo?.user,
            redact_setting: result?.data?.redact_setting,
          },
        });
        setEntityConfiguration(result?.data?.redact_setting);
      }
    } catch (err: any) {
      console.log(err);
    }
  };

  useEffect(() => {
    if (!activePreset) {
      if (isCustomAvailable && localConfiguration?.preset?.length > 1) {
        setActivePreset(PRESET_OPTIONS.custom1.key);
      } else {
        setActivePreset(localConfiguration?.preset[0]?.type);
      }
    }
  }, [localConfiguration]);

  const ACTION_CONFIG = {
    show_ai_toggler: !isAllFiltersRequired && !isOnlyBasicFiltersRequired,
    show_preset: !isAllFiltersRequired && !isOnlyBasicFiltersRequired,
    show_save: !isAllFiltersRequired,
    all_entities_disabled: isAllFiltersRequired,
    basic_filters_disabled: isOnlyBasicFiltersRequired,
  };

  return {
    entities,
    localConfiguration,
    setLocalConfiguration,
    entityConfiguration,
    activePreset,
    setActivePreset,
    ACTION_CONFIG,
    isCustomAvailable,
    handleSave,
  };
};
