import { IMAGE_PATH, SETTINGS } from "globals";
import { useAIResponseStatus } from "hooks";
import { Image } from "react-bootstrap";
import ReactMarkdown from "react-markdown";
import remarkBreaks from "remark-breaks";
import remarkGfm from "remark-gfm";
import BotActions from "./BotActions";
import "./styles.scss";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { nightOwl as codeTheme } from "react-syntax-highlighter/dist/cjs/styles/prism";

const LinkRenderer = (props: any) => {
  return (
    <a href={props.href} target="_blank" rel="noreferrer">
      {props.children}
    </a>
  );
};

const IncomingMessage = ({
  messageItem,
  isLastMessage,
  audioRef,
  history,
  setHistory,
  isPlaying,
  setIsPlaying,
  responseStatusConfig,
}: any) => {
  const { reid_text, prompt_id } = messageItem ?? {};
  const { currentStatus, isProcessing } = useAIResponseStatus({
    ...responseStatusConfig,
    reid_text,
    prompt_id,
  });
  return (
    <div className="incoming">
      <div className="incoming-data d-flex align-items-start">
        <div className="incoming-data-user position-relative">
          <div className="incoming-data-logo rounded-circle d-flex align-items-center">
            <Image
              src={IMAGE_PATH.singleLogo}
              alt="logo"
              className="object-fit-contain w-100 h-100"
            />
          </div>
        </div>

        <div className="incoming-data-details d-flex flex-column">
          {/* <p className="mb-0 incoming-data-details-timing">
            <span className="day">Thursday</span>
            <span className="time">11:40am</span>
          </p> */}

          <p className="user-name my-0 fw-bold">{SETTINGS.APP_NAME}</p>

          {!isProcessing && reid_text && (
            <>
              <div className="user-message-text">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm, remarkBreaks]}
                  components={{
                    a: LinkRenderer,
                    code({ node, inline, className, children, ...props }: any) {
                      const match = /language-(\w+)/.exec(className || "");

                      return !inline && match ? (
                        <SyntaxHighlighter
                          style={codeTheme}
                          PreTag="div"
                          language={match[1]}
                          {...props}
                        >
                          {String(children).replace(/\n$/, "")}
                        </SyntaxHighlighter>
                      ) : (
                        <code className={className} {...props}>
                          {children}
                        </code>
                      );
                    },
                  }}
                >
                  {reid_text}
                </ReactMarkdown>
              </div>
              <BotActions
                messageItem={messageItem}
                isLastMessage={isLastMessage}
                audioRef={audioRef}
                history={history}
                setHistory={setHistory}
                isPlaying={isPlaying}
                setIsPlaying={setIsPlaying}
              />
            </>
          )}
          {isProcessing && <span className="shimmer">{currentStatus}</span>}
        </div>
      </div>
    </div>
  );
};

export default IncomingMessage;
