@use "/src/styles/mixins/mixins.scss";

.modal {
  @include mixins.slim-scrollbar;

  &-backdrop {
    background: transparent;
    background-color: #0d31496d;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    opacity: 1 !important;
  }

  &-content {
    border: none;
  }

  &-dialog {
    // max-width: 100%;

    @media only screen and (min-width: 992px) {
      width: 550px;
    }
  }

  &-body {
    // padding: 60px;
    padding: 60px 40px;
    border-radius: 20px;

    @media only screen and (max-width: 991px) {
      padding: 20px;

      .auth-form {
        padding: 50px 0px;
      }
    }
  }

  &-close-button {
    width: 60px;
    height: 60px;
    padding: 10px;
    top: -30px;
    right: -30px;

    @media only screen and (max-width: 991px) {
      width: 35px;
      height: 35px;
      padding: 3px;
      top: 10px;
      right: 10px;
    }
  }
}
