@use "/src/styles/mixins/mixins.scss";

.dictate-modal {
  .modal-content {
    border-radius: 20px;
    height: 500px;
    display: flex;
    flex-direction: column;
  }

  .modal-dialog {
    width: 100%;
    max-width: 440px;
    margin: auto;
    transition: max-width 0.3s ease;
    
    &.dictate-edit {
      max-width: 700px;
    }

    @media only screen and (max-width: 576px) {
      max-width: 90vw;

      &.dictate-edit {
        max-width: 95vw;
      }
    }
  }

  .modal-body {
    padding: 2.5rem 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: stretch;
    align-items: stretch;
  }

  .btn-light {
    border: 1px solid #0d3149;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;

    svg {
      display: block;
    }
  }

  .transcription-textarea {
    border-radius: 10px;
    resize: none;
    overflow: auto;
    outline: none;
    box-shadow: none;
    border: 1px solid #ccc;
    
    @include mixins.arrow-scrollbar;
  }
}
