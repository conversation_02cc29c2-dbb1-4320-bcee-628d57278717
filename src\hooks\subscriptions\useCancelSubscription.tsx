import { RiCloseCircleLine } from "@remixicon/react";
import { useCancelSubscriptionMutation } from "api";
import toast from "react-hot-toast";
import { setConfirmModalConfig } from "stores";

const useCancelSubscription = ({ refetch }: any) => {
  const { mutateAsync: cancelSubscription } = useCancelSubscriptionMutation();

  const handleCancelSubscription = async (id: any) => {
    try {
      const result: any = await cancelSubscription({ subscription_id: id });
      if (result?.success) {
        toast.success(
          result?.message || "Subscription cancelled successfully!",
        );
        refetch();
      }
    } catch (err: any) {
      console.log(err);
    }
  };

  const onClickCancel = (evt: any, id: number) => {
    evt.stopPropagation();
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleCancelSubscription(id),
        content: {
          heading: "Cancel Subscription",
          description: "Are you sure you want to cancel this subscription?",
        },
        icon: RiCloseCircleLine,
      },
    });
  };

  return { onClickCancel };
};

export default useCancelSubscription;
