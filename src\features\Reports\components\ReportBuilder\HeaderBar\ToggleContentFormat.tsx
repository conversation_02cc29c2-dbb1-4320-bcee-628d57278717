import { RiBracesLine, RiHtml5Line } from "@remixicon/react";
import ActionButton from "features/Reports/components/ActionButton";
import toast from "react-hot-toast";

const ToggleContentFormat = ({
  useJsonFormat = false,
  setUseJsonFormat,
}: {
  useJsonFormat: boolean;
  setUseJsonFormat: (useJsonFormat: boolean) => void;
}) => {
  const handleToggleFormat = () => {
    setUseJsonFormat(!useJsonFormat);
    toast.success(`Switched to ${!useJsonFormat ? "JSON" : "HTML"} format`);
  };
  return (
    <ActionButton
      icon={useJsonFormat ? RiBracesLine : RiHtml5Line}
      onClick={handleToggleFormat}
      title={`Currently using ${useJsonFormat ? "JSON" : "HTML"} format - Click to switch`}
      className={useJsonFormat ? "active" : ""}
    />
  );
};

export default ToggleContentFormat;
