import React from "react";

interface MailIconProps {
  fill?: string;
  width?: string;
  height?: string;
}

const MailIcon: React.FC<MailIconProps> = ({
  fill = "#70828D",
  width = "20px",
  height = "20px",
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 0H19C19.5523 0 20 0.44772 20 1V17C20 17.5523 19.5523 18 19 18H1C0.44772 18 0 17.5523 0 17V1C0 0.44772 0.44772 0 1 0ZM18 4.23792L10.0718 11.338L2 4.21594V16H18V4.23792ZM2.51146 2L10.0619 8.662L17.501 2H2.51146Z"
        fill={fill}
      />
    </svg>
  );
};

export default MailIcon;
