import { Button, Placeholder } from "react-bootstrap";

const SubscriptionCardSkeleton = () => {
  return (
    <div className="subscription-card d-flex flex-column justify-content-between">
      <div>
        <Placeholder as="p" animation="wave">
          <Placeholder xs={6} />
        </Placeholder>

        <hr className="p-0 border-0" />

        <Placeholder as="p" animation="wave">
          <Placeholder xs={8} />
        </Placeholder>

        <Placeholder as="p" animation="wave">
          <Placeholder xs={5} />
        </Placeholder>

        <ul className="list-unstyled d-flex flex-column subscription-card-key-features m-0">
          <Placeholder as="li" animation="wave">
            <Placeholder xs={9} />
          </Placeholder>
          <Placeholder as="li" animation="wave">
            <Placeholder xs={7} />
          </Placeholder>
          <Placeholder as="li" animation="wave">
            <Placeholder xs={8} />
          </Placeholder>
        </ul>
      </div>

      <div className="action-btns position-relative z-0 mb-0 w-100">
        <Button
          variant="link"
          className="selected-btn text-uppercase border-brown btn-transparent text-center d-flex align-items-center justify-content-center position-relative overflow-hidden text-decoration-none mx-auto"
        >
          <Placeholder as="span" animation="wave">
            <Placeholder xs={4} />
          </Placeholder>
        </Button>
      </div>
    </div>
  );
};

export default SubscriptionCardSkeleton;
