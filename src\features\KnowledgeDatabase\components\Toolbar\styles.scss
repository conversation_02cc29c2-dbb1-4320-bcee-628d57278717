.knowledge-base-toolbar {
  .nav-pills {
    .nav-link {
      background-color: transparent;
      color: #0d3149;

      &.active {
        font-weight: bold;
      }
    }
  }

  .custom-breadcrumb {
    ol {
      margin-bottom: 0;
    }

    .dropdown.breadcrumb-item {
      padding: 0;
      .dropdown-toggle {
        background: none !important;
        border: none !important;
        box-shadow: none !important;
        color: #0d3149 !important;
        padding: 0 6px;
        font-size: 18px;
        line-height: 1;
        vertical-align: middle;
        &::after {
          display: none !important;
        }
      }
      .dropdown-menu {
        min-width: 160px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(13, 49, 73, 0.08);
        padding: 0.25rem 0;
        background: #fff;
        z-index: 1000;
      }
      .dropdown-item {
        color: #0d3149;
        font-size: 14px;
        padding: 8px 16px;
        transition: background 0.2s;
        &:hover,
        &:focus {
          background: #f1f1f1;
          color: #0d3149;
        }
      }
    }
  }
}
