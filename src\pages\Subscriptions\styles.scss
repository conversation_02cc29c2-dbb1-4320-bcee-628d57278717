@use "/src/styles/mixins/mixins.scss" as mixins;

.subscriptions {
  padding: 30px;
  gap: 30px;
  overflow: hidden;
  position: relative;
  z-index: 5;
  height: calc(100vh - 195px);
  border-radius: 12px;
  box-shadow:
    0px 65px 47px 0px rgba(26, 26, 26, 0.04),
    0px 100px 80px 0px rgba(26, 26, 26, 0.05);

  @media (min-height: 500px) and (max-height: 1070px) {
    overflow-x: hidden;
    overflow-y: auto;
    @include mixins.slim-scrollbar;
  }

  @media only screen and (max-width: 991px) {
    height: auto;
  }

  @media only screen and (max-width: 576px) {
    padding: 15px 15px 30px 15px;
  }

  @include mixins.breadcrumb-commom-style;

  hr {
    height: 5px;
    background-image: url("../../assets/images/dividerLg.webp");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
  }
}
