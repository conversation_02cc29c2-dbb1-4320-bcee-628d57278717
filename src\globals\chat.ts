import {
  RiChat3Line,
  RiCodeBoxLine,
  RiFileListLine,
  RiLightbulbLine,
  RiLineChartLine,
  RiMailLine,
  RiMoneyPoundCircleLine,
  RiPhoneLine,
  RiSendPlaneLine,
  RiTranslate,
  RiUserSettingsLine,
} from "@remixicon/react";

export const FullLoadingStatus = [
  { label: "Thinking...", seconds: 2 },
  { label: "Redacting...", seconds: 2 },
  { label: "Securing...", seconds: 2 },
  { label: "Processing...", seconds: 1 },
  { label: "Outputting...", seconds: 2 },
];

export const SimpleLoadingStatus = [
  { label: "Processing Prompt...", seconds: 2 },
  { label: "Outputting...", seconds: 2 },
];

export const DEFAULT_PROMPT_ICONS: any = {
  email: RiMailLine,
  post: RiSendPlaneLine,
  document: RiFileListLine,
  job: RiUserSettingsLine,
  graph: Ri<PERSON>ine<PERSON>hartLine,
  translate: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  code: <PERSON><PERSON><PERSON>odeBoxL<PERSON>,
  idea: Ri<PERSON>ightbulbLine,
  phone: RiPhoneL<PERSON>,
  money: RiMoneyPoundCircleLine,
  speech: RiChat3Line,
};

export const DEFAULT_PROMPTS = [
  {
    prompt_id: 1,
    icon: "email",
    title: "Compose an email for a client",
    prompt:
      "Compose an email for a corporate user, ask me who the recipient is and what subject you would like the email to consider. Please list some examples of emails other corporate users may want to create, by saying 'Other users have found it useful compose emails such as'",
  },
  {
    prompt_id: 2,
    icon: "post",
    title: "Create a LinkedIn post",
    prompt:
      "Please ask the user for a company website address, or to upload a company specific document, that you can review, which you can then use to create personalised LinkedIn post ideas. Please follow up by saying that you can provide generic examples if preferred.",
  },
  {
    prompt_id: 3,
    icon: "document",
    title: "Summarise a document",
    prompt:
      "Please ask for the user to provide context of a document in order for you to provide a summary. Please also list some other things you can do with a document. Please list some examples of documents a financial adviser may want to upload, by saying 'Other users have found it useful to load these types of documents.'",
  },
  {
    prompt_id: 4,
    icon: "job",
    title: "Create a job specification",
    prompt:
      "I would like to create an example job profile. What do you need from me to create one?",
  },
];
