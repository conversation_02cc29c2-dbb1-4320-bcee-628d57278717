@use "/src/styles/mixins/mixins.scss" as mixins;

.profile-section {
  padding: 30px;
  gap: 30px;
  overflow: hidden;
  position: relative;
  z-index: 5;
  height: calc(100vh - 195px);
  border-radius: 12px;
  box-shadow:
    0px 65px 47px 0px rgba(26, 26, 26, 0.04),
    0px 100px 80px 0px rgba(26, 26, 26, 0.05);

  @media (min-height: 500px) and (max-height: 1070px) {
    overflow-x: hidden;
    overflow-y: auto;
    @include mixins.slim-scrollbar;
  }

  @media only screen and (max-width: 991px) {
    height: auto;
  }

  @media only screen and (max-width: 576px) {
    padding: 15px 15px 30px 15px;
  }

  @include mixins.breadcrumb-commom-style;

  hr {
    height: 5px;
    background-image: url("../../assets/images/dividerLg.webp");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
  }

  &-action-card-list {
    @media (min-width: 1400px) {
      &-col {
        flex: 0 0 auto;
        width: 20%;
      }
    }
    &-item {
      padding: 30px 10px;
      gap: 20px;
      border-radius: 20px;
      border: 5px solid #fcfcfc;
      box-shadow:
        0px 135.639px 108.511px 0px rgba(26, 26, 26, 0.02),
        0px 87.914px 63.549px 0px rgba(26, 26, 26, 0.01);

      @media only screen and (min-width: 1400px) and (max-width: 1560px) {
        padding: 30px 0px;
      }

      @media only screen and (min-width: 768px) {
        min-height: 276px;
      }

      @media only screen and (max-width: 576px) {
        padding: 30px 0px;

        .card-body {
          padding-left: 0;
          padding-right: 0;
        }
      }

      &:hover {
        background-color: #0d314914;
      }

      .action-img {
        width: 60px;
        height: 60px;
      }

      .heading {
        font-size: 20px;
        font-weight: 700;

        @media only screen and (min-width: 1400px) and (max-width: 1560px) {
          font-size: 18px;
        }
      }

      .sub-heading {
        font-size: 20px;
        font-weight: 500;

        @media only screen and (min-width: 1400px) and (max-width: 1560px) {
          font-size: 18px;
        }

        @media only screen and (max-width: 576px) {
          font-size: 17px;
        }

        span {
          font-size: 16px;
          color: red;
        }
      }

      .split-heading {
        font-size: 18px;
        font-weight: 500;

        small {
          font-size: 16px;
          font-weight: 400;
        }
      }

      .inter-img {
        width: 20px;
      }
    }
  }

  &-form-container {
    .auth-form {
      @media only screen and (max-width: 576px) {
        padding: 0px 0px 30px 0px;
      }

      &-heading {
        @media only screen and (max-width: 576px) {
          font-size: 20px;
        }
      }

      .website-form .form-control {
        @media only screen and (max-width: 576px) {
          font-size: 16px;
        }
      }

      .website-form input[type="checkbox"] {
        width: 32px;
        height: 16px;
        cursor: pointer;
        border: 2px solid #ad986f;
        background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9Jy00IC00IDggOCc+PGNpcmNsZSByPSczJyBmaWxsPSdyZ2JhKDE3MywgMTUyLCAxMTEpJy8+PC9zdmc+DQoNCg==");
        border-radius: 10px;

        &:checked {
          background-color: #ad986f;
          background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9Jy00IC00IDggOCc+PGNpcmNsZSByPSczJyBmaWxsPScjZmZmJy8+PC9zdmc+DQoNCg==");
        }
      }

      &-card {
        padding: 30px 60px;
        border-radius: 20px;
        border: 5px solid #fcfcfc;
        box-shadow:
          0px 65px 47px 0px rgba(26, 26, 26, 0.04),
          0px 100px 80px 0px rgba(26, 26, 26, 0.05);

        @media only screen and (min-width: 992px) and (max-width: 1199px) {
          padding: 30px 40px;

          .card-body {
            padding: 0px;
          }
        }

        @media only screen and (max-width: 576px) {
          padding: 10px 0px;
        }

        &-user-img {
          width: 120px;
          height: 120px;
          background-color: #ad986f;
          padding: 5px;
        }
      }

      &-file-input {
        &-label {
          right: 0;
          bottom: 0;
          background-color: #0d3149;
          width: 43px;
          height: 43px;
          line-height: 32px;
          border: 3px solid #f9f9f9;
        }

        @include mixins.universal-heading-subheading;
      }
    }
  }

  .medium-spinner {
    width: 1.5rem;
    height: 1.5rem;
  }
}
