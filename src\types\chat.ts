import { PaginationInterface } from "./utils";

export interface EntityConfigurationInterface {
  entity_values: string[];
  preset_type: string;
  enable_privacy: boolean;
}

export interface SendMessagePayloadInterface
  extends EntityConfigurationInterface {
  text: string;
  chat_id: string;
  message_type: string;
  chat_history: {
    text: string;
    reid_text: string;
  };
  metadata?: {
    name: string;
    type: string;
    size: string;
    doc_id: any;
  };
  timezone?: string;
}

export interface TempNewChatInterface {
  chat_id: string;
  created_at: string;
  text: string;
}

export interface ChatHistoryParamsInterface extends PaginationInterface {}

export interface PromptInterface {
  prompt_id?: number | undefined;
  title: string;
  prompt: string;
  icon: string;
  templates_file?: string;
  file?: string;
}
