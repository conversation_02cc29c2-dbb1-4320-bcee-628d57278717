import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { useEffect, useState } from "react";
import CardForm from "./CardForm";
import "./styles.scss";
import { useConfigQuery } from "api";

const SaveCard = () => {
  const [stripePromise, setStripePromise] = useState<any>(null);

  const { data: { publishableKey = null } = {} }: any = useConfigQuery();

  useEffect(() => {
    if (publishableKey) {
      setStripePromise(loadStripe(publishableKey));
    }
  }, [publishableKey]);

  return (
    <div
      className="auth-form d-flex justify-content-center align-items-center flex-column save-card-form"
      style={{ gap: "30px" }}
    >
      <div className="d-flex flex-column" style={{ gap: "23px" }}>
        <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
          Payment Details
        </h1>

        <p className="mb-0 auth-form-description font-gray text-center">
          Lorem ipsum dolor sit amet consectetur. Amet elementum rhoncus
          <br className="d-lg-block d-none" />
          at pulvinar in neque. Adipiscing.
        </p>
      </div>

      <div className="website-form">
        {stripePromise && (
          <Elements stripe={stripePromise}>
            <CardForm />
          </Elements>
        )}
      </div>
    </div>
  );
};

export default SaveCard;
