import { RiCameraLine } from "@remixicon/react";
import { IMAGE_PATH } from "globals";
import { Image } from "react-bootstrap";
import { isValidFileSize, isValidFileType } from "utils";
import "./styles.scss";

interface LogoPictureProps {
  logo?: any;
  setFieldValue?: any;
  name?: string;
  imageProps?: any;
  imageClassName?: string;
  imgRef?: any;
}

const LogoPicture: React.FC<LogoPictureProps> = ({
  logo,
  setFieldValue,
  name = "logo",
  imageProps,
  imageClassName = "",
  imgRef,
}) => {
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files && event.target.files[0];
    if (file) {
      if (!isValidFileType(file) || !isValidFileSize(file)) {
        return;
      }
      setFieldValue(name, file);
    }
  };

  return (
    <div className="doc-settings-logo-input w-auto position-relative align-self-center">
      {logo ? (
        <Image
          src={logo instanceof File ? URL.createObjectURL(logo) : logo}
          alt="logo"
          className={`doc-settings-logo-img object-fit-cover rounded-circle bg-transparent border border-4 border-brown p-0 ${imageClassName}`}
          {...imageProps}
        />
      ) : (
        <Image
          src={IMAGE_PATH.avatarIcon}
          alt="default logo"
          className={`doc-settings-logo-img object-fit-cover rounded-circle bg-transparent border border-4 border-brown p-0 ${imageClassName}`}
          // {...imageProps}
        />
      )}

      {setFieldValue && (
        <>
          <input
            type="file"
            className="d-none"
            id={`${name}Upload`}
            onChange={handleFileChange}
            accept=".png, .jpg, .jpeg"
            ref={imgRef}
          />
          <label
            htmlFor={`${name}Upload`}
            className="doc-settings-logo-input-label text-center rounded-circle position-absolute d-flex justify-content-center align-items-center cursor-pointer"
          >
            <RiCameraLine size={"22px"} color="#f9f9f9" />
          </label>
        </>
      )}
    </div>
  );
};

export default LogoPicture;
