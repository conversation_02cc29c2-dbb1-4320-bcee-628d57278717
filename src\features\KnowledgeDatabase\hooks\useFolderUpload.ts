import { useInvalidateQuery } from "hooks";
import { useCallback } from "react";
import { useParams } from "react-router-dom";
import useUploadProgressStore from "stores/upload";
import { useAddKnowledgeBase } from "../api";

export const getFilesFromDataTransferItems = async (
    items: DataTransferItemList
): Promise<File[]> => {
    const queue: File[] = [];

    const traverseFileTree = (item: any, path = ""): Promise<void> => {
        return new Promise((resolve) => {
            if (item.isFile) {
                item.file((file: File) => {
                    const fullPath = path + file.name;
                    Object.defineProperty(file, "webkitRelativePath", {
                        value: fullPath,
                    });
                    queue.push(file);
                    resolve();
                });
            } else if (item.isDirectory) {
                const dirReader = item.createReader();
                dirReader.readEntries(async (entries: any[]) => {
                    for (const entry of entries) {
                        await traverseFileTree(entry, path + item.name + "/");
                    }
                    resolve();
                });
            }
        });
    };

    const promises: Promise<void>[] = [];
    for (let i = 0; i < items.length; i++) {
        const entry = (items[i]).webkitGetAsEntry?.();
        if (entry) {
            promises.push(traverseFileTree(entry));
        }
    }

    await Promise.all(promises);
    return queue;
};

export interface FileNode {
    name: string;
    file?: File;
    children?: FileNode[];
}

interface UploadFileResponse {
    id: string;
    name: string;
}

const useFolderUpload = () => {
    // const [tree, setTree] = useState<FileNode[]>([]);
    const { mutateAsync: addKnowledgeBase } = useAddKnowledgeBase();
    const { parent_id: parentId } = useParams();
    const [invalidateQueries] = useInvalidateQuery();

    async function uploadFile({
        file,
        parentId,
        owner_type,
    }: {
        file: File,
        parentId: string | undefined,
        owner_type?: string,
    }): Promise<UploadFileResponse> {
        const formData = new FormData();
        formData.append("file", file);
        if (owner_type) {
            formData.append("owner_type", owner_type);
        }
        if (parentId) {
            formData.append("parent_id", parentId);
        }

        const response: any = await addKnowledgeBase(formData);
        if (!response?.success) {
            throw new Error(`Failed to upload ${file.name}`);
        }
        return response;
    }

    // const buildTree = (files: File[]): FileNode[] => {
    //     const root: FileNode[] = [];

    //     files.forEach((file) => {
    //         const parts = (file as File).webkitRelativePath.split("/");
    //         let currentLevel = root;

    //         parts.forEach((part, index) => {
    //             const isFile = index === parts.length - 1;

    //             let existingNode = currentLevel.find((n) => n.name === part);
    //             if (!existingNode) {
    //                 existingNode = { name: part };
    //                 if (!isFile) existingNode.children = [];
    //                 currentLevel.push(existingNode);
    //             }

    //             if (!isFile) {
    //                 currentLevel = existingNode.children!;
    //             } else {
    //                 existingNode.file = file;
    //             }
    //         });
    //     });

    //     return root;
    // };

    const handleFolderUpload = useCallback(
        async (e: React.ChangeEvent<HTMLInputElement>, owner_type: string) => {
            e.preventDefault();
            const files = Array.from(e.target.files ?? []);
            if (files.length === 0) return;

            // Set global upload progress
            useUploadProgressStore.getState().setProgress(files.length);

            // Upload files sequentially
            for (const file of files) {
                try {
                    await uploadFile({ file, parentId, owner_type });
                    useUploadProgressStore.getState().incrementUploaded();
                    console.log(`Uploaded ${file.name}`);
                } catch (error) {
                    console.error(`Error uploading ${file.name}:`, error);
                    // Optionally show user feedback (e.g., toast notification)
                }
            }

            useUploadProgressStore.getState().finishUpload();
            setTimeout(() => {
                useUploadProgressStore.getState().resetProgress();
            }, 1000); // Optionally keep widget for 1s after finish
            invalidateQueries(['knowledge-base'])
        },
        [parentId]
    );

    const handleDropUpload = useCallback(
        async (e: React.DragEvent<HTMLDivElement>) => {
            e.preventDefault();
            const items = e.dataTransfer.items;
            const files = await getFilesFromDataTransferItems(items);
            if (files.length === 0) return;

            // Set global upload progress
            useUploadProgressStore.getState().setProgress(files.length);

            // Upload files sequentially
            for (const file of files) {
                try {
                    await uploadFile({ file, parentId });
                    useUploadProgressStore.getState().incrementUploaded();
                    console.log(`Uploaded ${file.name}`);
                } catch (error) {
                    console.error(`Error uploading ${file.name}:`, error);
                    // Optionally show user feedback (e.g., toast notification)
                }
            }

            useUploadProgressStore.getState().finishUpload();
            setTimeout(() => {
                useUploadProgressStore.getState().resetProgress();
            }, 1000); // Optionally keep widget for 1s after finish
            invalidateQueries(['knowledge-base'])
        },
        [parentId]
    );

    return {
        // tree,
        handleFolderUpload,
        handleDropUpload,
    };
};

export default useFolderUpload;