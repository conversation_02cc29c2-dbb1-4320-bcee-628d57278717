import { FC } from "react";
import "./styles.scss";

interface TableToolbarProps {
  title: string;
  description: string;
  icon: any;
}

const TableToolbar: FC<TableToolbarProps> = ({ title, description, icon }) => {
  const ToolbarIcon = icon;
  return (
    <div className="toolbar-wrapper d-flex flex-sm-row flex-column justify-content-sm-between justify-content-center align-items-stretch my-4">
      <div className="toolbar-wrapper-left">
        <div className="icon d-inline-block lh-1 align-middle">
          <ToolbarIcon className="toolbar-icon object-fit-contain" />
        </div>

        <p className="title mb-0 ms-2 d-inline-block lh-1 align-middle">
          {title}
        </p>

        <p className="mb-0 mt-1 description">{description}</p>
      </div>
    </div>
  );
};

export default TableToolbar;
