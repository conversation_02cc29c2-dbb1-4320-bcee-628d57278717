import { CustomDropdown } from "components";
import { useGenerateReportMutation, useGetReports } from "features/Reports/api";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { setReportInfo } from "features/Reports/store";
import { useInvalidateQuery } from "hooks";
import { useState } from "react";
import { Button, Container, Form } from "react-bootstrap";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { convertObjToFormData, getTimezone } from "utils";
import FileUploadContainer from "./FileUploadContainer";
import "./styles.scss";
import GenerateReportModal from "./GenerateReportModal";

const ReportGenerator = () => {
  const [searchQuery, setSearchQuery] = useState<string>("");
  const { data: { data: reportsData = [] } = {} } = useGetReports({
    page: 1,
    limit: 100,
    keyword: searchQuery || undefined,
  });
  const { mutateAsync: generateReport } = useGenerateReportMutation();
  const [invalidateQueries] = useInvalidateQuery();

  const initialReportInfo = {
    template_id: "",
    template_name: "",
    client_name: "",
    files: [],
    enable_privacy: false,
  };

  const [reportForm, setReportForm] = useState<{
    template_id: string;
    template_name: string;
    client_name: string;
    files: File[];
    enable_privacy?: boolean;
  }>(initialReportInfo);
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  const handleGenerate = async () => {
    if (!reportForm.template_id || reportForm.files.length === 0) {
      toast.error("Please select a report template and upload files.");
      return;
    }
    setIsLoading(true);
    try {
      const payload = convertObjToFormData({
        ...reportForm,
        timezone: getTimezone(),
      });
      const result: any = await generateReport(payload);
      if (result?.success) {
        toast.success(result?.message || "Report generated successfully!");
        setReportForm(initialReportInfo);
        setReportInfo(result?.data || {});
        invalidateQueries(["generated-reports-list"]);
        navigate(`${REPORTS_ROUTE_PATH.GENERATE_REPORT}/${result?.data?.id}`);
      }
    } catch (err: any) {
      console.log(err);
    } finally {
      setIsLoading(false);
    }
  };

  const reportTemplates = reportsData.map((report: any) => ({
    label: report.title,
    value: JSON.stringify({ id: report.id, name: report.title }),
  }));

  return (
    <>
      <Container
        fluid
        className="report-generator-section bg-white rounded p-4 d-flex flex-column gap-3"
      >
        <div className="d-flex gap-3 flex-column flex-sm-row">
          <CustomDropdown
            title="Select Report Template"
            items={reportTemplates}
            className="select-report-dropdown w-100"
            onSelect={(value) => {
              const selectedTemplate = JSON.parse(value);
              setReportForm({
                ...reportForm,
                template_id: selectedTemplate.id,
                template_name: selectedTemplate.name,
              });
              setSearchQuery("");
            }}
            searchEnabled={true}
            onSearch={setSearchQuery}
          />

          <div className="form-group w-100">
            <input
              className="form-control"
              placeholder="Enter Client Name"
              value={reportForm.client_name}
              onChange={(e) =>
                setReportForm({ ...reportForm, client_name: e.target.value })
              }
            />
          </div>
        </div>

        <FileUploadContainer
          onChange={(uploadedFiles: File[]) =>
            setReportForm({ ...reportForm, files: uploadedFiles })
          }
        />

        <div className="text-center">
          <Form
            onClick={(e) => e.stopPropagation()}
            className="d-flex align-items-center justify-content-center gap-2 mb-2"
          >
            <Form.Check
              type="switch"
              className={`${reportForm.enable_privacy ? "expanded" : ""} p-0`}
              checked={reportForm.enable_privacy}
              onChange={(evt) =>
                setReportForm({
                  ...reportForm,
                  enable_privacy: evt.target.checked,
                })
              }
            />
            <p className="mb-0 fw-bold">Enable Privacy Filters</p>
          </Form>
          <Button
            variant=""
            className="bg-brown border-brown text-uppercase font-light fw-bold px-5 py-2"
            onClick={handleGenerate}
            disabled={
              !reportForm.template_id ||
              reportForm.files.length === 0 ||
              isLoading
            }
          >
            {isLoading ? "Generating..." : "Generate Report"}
          </Button>
        </div>
      </Container>
      {isLoading && <GenerateReportModal show={isLoading} />}
    </>
  );
};

export default ReportGenerator;
