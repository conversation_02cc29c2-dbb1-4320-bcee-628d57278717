import { RiArticleFill } from "@remixicon/react";
import { useGetInvoices } from "api";
import {
  BackBreadcrumb,
  DataGridTable,
  HoverTooltip,
  TableToolbar,
} from "components";
import { IMAGE_PATH, SETTINGS } from "globals";
import { Button, Image } from "react-bootstrap";
import { formatUnixTimestamp } from "utils";
import "./styles.scss";

const Invoices = () => {
  const { data: { invoice = [] } = {}, isLoading } = useGetInvoices();

  const onClickExport = (e: any, url: any) => {
    e.stopPropagation();
    url && window.open(url, "_blank");
  };

  const columns: any = [
    {
      field: "number",
      headerName: "Invoice No.",
      renderCell: (row: any) => <p className="mb-0 fw-bold">{row?.number}</p>,
    },
    {
      field: "created",
      headerName: "Date",
      renderCell: (row: any) => (
        <p className="title mb-0 fw-bold">
          {formatUnixTimestamp(row?.created) ?? "NA"}
        </p>
      ),
    },
    {
      field: "amount",
      headerName: "Amount",
      renderCell: (row: any) => {
        const originalPrice = SETTINGS.CURRENCY_INFO.GBP.stripeConversion(
          row?.subtotal,
        );
        const discountedPrice = SETTINGS.CURRENCY_INFO.GBP.stripeConversion(
          row?.discount_amount,
        );
        const hasDiscount = row?.has_discount;

        return (
          <div className="price-container">
            {hasDiscount ? (
              <>
                <span className="fw-bold d-block">{discountedPrice}</span>
                <span className="text-muted text-decoration-line-through me-2">
                  {originalPrice}
                </span>
              </>
            ) : (
              <span className="fw-bold">{originalPrice}</span>
            )}
          </div>
        );
      },
    },
    {
      field: "status",
      headerName: "Status",
      renderCell: (row: any) => (
        <p
          className={`mb-0 rounded-pill text-center font-light fw-bold px-1 py-1 text-capitalize ${row?.status === "open" ? "error" : "success"} status-pill`}
        >
          {row?.status === "open" ? "Due" : row?.status}
        </p>
      ),
    },
    {
      field: "actions",
      headerName: "Actions",
      renderCell: (row: any) => (
        <HoverTooltip title="Download Invoice" customClass="fw-bold">
          <Button
            className="bg-blue border-blue p-2 lh-1 me-3 action-item-btn"
            onClick={(e: any) => onClickExport(e, row?.invoice_pdf)}
            disabled={!row?.invoice_pdf}
          >
            <Image
              src={IMAGE_PATH.pdfFileIcon}
              className="w-100 h-100 object-fit-contain"
              style={{ objectPosition: "center center" }}
            />
          </Button>
        </HoverTooltip>
      ),
    },
  ];

  return (
    <main className="invoice-wrapper bg-white">
      <BackBreadcrumb />
      <TableToolbar
        title="Invoices"
        description="View the status of your invoices, plus download copies for your
            records."
        icon={RiArticleFill}
      />
      <DataGridTable
        columns={columns}
        rows={invoice}
        showFooter={false}
        loading={isLoading}
      />
    </main>
  );
};

export default Invoices;
