import { Button, Form } from "react-bootstrap";

interface EditModeProps {
  config: {
    formattedDateTime: string;
    textareaRef: React.RefObject<HTMLTextAreaElement>;
    text: string;
    handleTextChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
    handleFinalFinish: () => void;
    hasAnyText: boolean;
  };
}

export default function EditMode({ config }: EditModeProps) {
  const {
    formattedDateTime,
    textareaRef,
    text,
    handleTextChange,
    handleFinalFinish,
    hasAnyText,
  } = config;
  return (
    <div className="w-100 d-flex flex-column align-items-stretch gap-3">
      <div className="px-1">
        <h5 className="mb-1 fw-bold">Dictation</h5>
        <small className="text-muted fw-semibold">{formattedDateTime}</small>
      </div>

      <Form.Control
        as="textarea"
        ref={textareaRef}
        value={text}
        onChange={handleTextChange}
        rows={12}
        className="transcription-textarea"
        autoFocus
      />

      <div className="w-100 d-flex justify-content-center">
        <Button
          variant=""
          className="bg-brown border-brown text-white fw-bold px-5 py-2"
          onClick={handleFinalFinish}
          disabled={!hasAnyText}
        >
          FINISH
        </Button>
      </div>
    </div>
  );
}
