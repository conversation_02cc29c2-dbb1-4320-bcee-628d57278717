import { RiAddLine, RiCloseLine, RiSubtractLine } from "@remixicon/react";
import { useWordCountPaymentDetailsMutation } from "api";
import { useFormik } from "formik";
import { SETTINGS } from "globals";
import parse from "html-react-parser";
import { useState } from "react";
import { Button, Form, Modal, Spinner } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { formatNumberWithCommas } from "utils";
import * as Yup from "yup";

interface OnlineWordCountModalProps {
  show: boolean;
  onClose: () => void;
  wordCountConfig?: any;
  row?: any;
  isOfflineAccount?: boolean;
}

export default function OnlineWordCountModal({
  show,
  onClose,
  wordCountConfig,
  row,
  isOfflineAccount,
}: OnlineWordCountModalProps) {
  const MIN_WORD_COUNT = 0;
  const STEP = 10000;

  const textContent = {
    heading: "Upgrade Word Count",
    label: "How many words would you like to purchase?",
    buttonText: "Next",
    disclaimer: `Addtiional words cost ${SETTINGS.CURRENCY_INFO.GBP.symbol}
        ${wordCountConfig?.word_limit_setting?.word_price} per 
        ${formatNumberWithCommas(
          wordCountConfig?.word_limit_setting?.minimum_word,
        )}
        .
        <br />
        Word count resets monthly. Unused allowance will be lost.
        <br />
        Limit can be purchased only once per billing cycle.
      `,
  };

  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const { mutateAsync: wordCountPaymentDetails } =
    useWordCountPaymentDetailsMutation();

  const formik: any = useFormik({
    initialValues: { word_count: isOfflineAccount ? row?.word_limit : 10000 },
    enableReinitialize: true,
    validationSchema: Yup.object({
      word_count: Yup.number()
        .required("Word count is required")
        .min(MIN_WORD_COUNT, `Word count must be at least ${MIN_WORD_COUNT}`)
        // .max(MAX_WORD_COUNT, `Word count must be at most ${formatNumberWithCommas(MAX_WORD_COUNT)}`)
        .test(
          "is-multiple-of-10000",
          "Word count must be a multiple of 10,000",
          (number) => {
            if (isOfflineAccount) return true;
            return number % STEP === 0;
          },
        ),
    }),
    onSubmit: async (values) => {
      if (!values?.word_count) return;

      try {
        setIsLoading(true);
        const result: any = await wordCountPaymentDetails({
          word_limit: values?.word_count,
          member_id: row?.id,
        });
        if (result?.success) {
          navigate(
            `${ROUTE_PATH.WORD_COUNT_PAYMENT}?word_limit=${values?.word_count}&member_id=${row?.id}`,
          );
        }
      } catch (err: any) {
        console.log(err);
      } finally {
        setIsLoading(false);
      }
    },
  });

  const renderError = (field: string) => {
    if (formik.touched[field] && formik.errors[field]) {
      return <span className="mb-2 text-danger">{formik.errors[field]}</span>;
    }
  };

  const handleClose = () => {
    formik.resetForm();
    onClose();
  };

  const handleIncrement = () => {
    formik.setFieldValue("word_count", formik.values.word_count + STEP);
  };

  const handleDecrement = () => {
    if (formik.values.word_count - STEP >= MIN_WORD_COUNT) {
      formik.setFieldValue("word_count", formik.values.word_count - STEP);
    }
  };

  return (
    <Modal show={show} onHide={handleClose} keyboard={false} centered>
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={handleClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>

        <div
          className="auth-form d-flex justify-content-center align-items-stretch flex-column"
          style={{ gap: "20px" }}
        >
          <div className="d-flex flex-column" style={{ gap: "15px" }}>
            <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1 fs-2">
              {textContent?.heading}
            </h1>
          </div>

          <div className="website-form w-100">
            <Form
              className="d-flex flex-column gap-3"
              onSubmit={formik.handleSubmit}
            >
              <div className="d-flex flex-lg-row flex-column gap-3">
                <Form.Group className="m-0 p-0 position-relative w-100">
                  <Form.Label className="mb-2 w-100 text-center">
                    {textContent?.label}
                  </Form.Label>
                  <div className="position-relative">
                    <Button
                      onClick={handleDecrement}
                      style={{
                        position: "absolute",
                        left: "10px",
                        top: "50%",
                        transform: "translateY(-50%)",
                        outline: "none",
                        border: "none",
                      }}
                      variant=""
                      disabled={formik.values.word_count <= MIN_WORD_COUNT}
                    >
                      <RiSubtractLine size="24px" className="icon-left" />
                    </Button>

                    <Form.Control
                      type="number"
                      placeholder="Enter word count"
                      min={MIN_WORD_COUNT}
                      className="text-center fw-bold p-2"
                      {...formik.getFieldProps("word_count")}
                      style={{ paddingLeft: "40px", paddingRight: "40px" }}
                    />

                    <Button
                      onClick={handleIncrement}
                      style={{
                        position: "absolute",
                        right: "10px",
                        top: "50%",
                        transform: "translateY(-50%)",
                        outline: "none",
                        border: "none",
                      }}
                      variant=""
                    >
                      <RiAddLine size="24px" className="icon-right" />
                    </Button>
                  </div>
                  <div className="d-flex flex-column mt-3">
                    {renderError("word_count")}
                  </div>
                </Form.Group>
              </div>

              <div
                className="action-btns mt-1 d-flex flex-column"
                style={{ gap: "30px" }}
              >
                <Button
                  type="submit"
                  className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                  disabled={isLoading}
                >
                  {isLoading ? <Spinner /> : textContent?.buttonText}
                </Button>
                {textContent?.disclaimer && (
                  <div className="text-disclaimer mx-3 text-center">
                    {parse(textContent?.disclaimer || "")}
                  </div>
                )}
              </div>
            </Form>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
}
