import { RiUserFollowLine } from "@remixicon/react";
import { useInvalidateQuery } from "hooks";
import { Dropdown, DropdownButton } from "react-bootstrap";
import toast from "react-hot-toast";
import { setConfirmModalConfig } from "stores";
import { useUpdatePermissionGroup } from "../api";

interface PermissionGroupProps {
  row: any;
  disabled?: boolean;
  isOpen?: boolean;
  onToggle?: () => void;
}

const PermissionGroup = ({ row, disabled = false, isOpen, onToggle }: PermissionGroupProps) => {
  const permissions = [
    {
      value: "admin,user",
      label: "All",
    },
    {
      value: "admin",
      label: "Admin",
    },
    {
      value: "user",
      label: "User",
    },
  ];

  const { mutateAsync: updatePermissionGroup } = useUpdatePermissionGroup();
  const [invalidateQueries] = useInvalidateQuery();

  const handlePermissionChange = async ({ row, permission }: any) => {
    try {
      const params = {
        id: row?.id,
        access_control: {
          ...row?.access_control,
          allowed_roles: permission,
        },
      };
      const result: any = await updatePermissionGroup(params);
      if (result?.success) {
        toast.success(result?.message || "Permission Updated Successfully!");
        invalidateQueries(["knowledge-base"]);
      }
    } catch (err: any) {
      console.log(err);
    }
  };

  const onSelectPermission = ({ row, permission }: any) => {
    const valueArr = permission.split(",");

    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handlePermissionChange({ row, permission: valueArr }),
        content: {
          heading: "Update Permission",
          description: `Are you sure you want to change permission?`,
        },
        icon: RiUserFollowLine,
        iconColor: "#ad986f",
      },
    });
  };

  const selectedPermission = row?.access_control?.allowed_roles?.join(",");
  const selectedPermissionLabel = permissions.find(
    (item) => item.value === selectedPermission
  )?.label;

  return (
    <DropdownButton
      className="status-dropdown active"
      title={selectedPermissionLabel}
      onClick={(e: any) => e.stopPropagation()}
      variant=""
      disabled={disabled}
      show={isOpen}
      onToggle={onToggle}
    >
      {permissions.map((item) => {
        return (
          <Dropdown.Item
            className="text-capitalize d-flex align-items-center gap-2"
            key={item.value}
            onClick={() => onSelectPermission({ row, permission: item.value })}
          >
            {item.label}
          </Dropdown.Item>
        );
      })}
    </DropdownButton>
  );
};

export default PermissionGroup;
