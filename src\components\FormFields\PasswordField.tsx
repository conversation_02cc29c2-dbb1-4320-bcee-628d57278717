import { RiEyeLine, RiEyeOffLine } from "@remixicon/react";
import { ErrorMessage, Field } from "formik";
import { useState } from "react";

const PasswordField = ({ fieldName, label, placeholder }: any) => {
  const [showPassword, setShowPassword] = useState<boolean>(false);

  const togglePasswordVisibility = () => {
    setShowPassword((prevState) => !prevState);
  };

  return (
    <>
      <label className="form-label">{label}</label>
      <Field
        name={fieldName}
        className="form-control"
        placeholder={placeholder}
        type={showPassword ? "text" : "password"}
        autoComplete="new-password"
      />
      <ErrorMessage component={"span"} name={fieldName} />

      {showPassword ? (
        <RiEyeLine
          size={"20px"}
          color="#70828D"
          onClick={togglePasswordVisibility}
          className="cursor-pointer"
        />
      ) : (
        <RiEyeOffLine
          size={"20px"}
          color="#70828D"
          onClick={togglePasswordVisibility}
          className="cursor-pointer"
        />
      )}
    </>
  );
};

export default PasswordField;
