.compliance-modal {
  .modal-content {
    border-radius: 12px;
    padding: 1.5rem 2rem;
    max-height: 90vh;
    overflow: hidden;
  }

  .modal-body {
    overflow-y: auto;
    padding: 10px !important;
  }

  .modal-dialog {
    @media only screen and (min-width: 992px) {
      max-width: 800px;
    }
  }

  .compliance-wrapper {
    text-align: center;

    .score-bar {
      margin-top: 1.5rem;

      .bar {
        position: relative;
        height: 16px;
        background: linear-gradient(to right, #ef4444, #facc15, #10b981);
        border-radius: 8px;
        margin-bottom: 1rem;
        width: 100%;
      }

      .indicator {
        position: absolute;
        top: -6px;
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 10px solid #111827;
      }

      .score-percent {
        font-size: 2.5rem;
        font-weight: bold;
        color: #111827;
        margin-bottom: 0.5rem;
      }

      .score-label {
        font-size: 1.1rem;
        color: #6b7280;
      }
    }

    .compliance-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 1.5rem;

      th,
      td {
        padding: 0.75rem 1rem;
        border: 1px solid #e5e7eb;
        text-align: left;
        font-size: 0.95rem;
      }

      th {
        background-color: #f9fafb;
        font-weight: 600;
        color: #374151;
      }

      td {
        color: #111827;
      }
    }
  }
}
