import { RiAddLine } from "@remixicon/react";
import { useGetReports } from "features/Reports/api";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { resetReportState } from "features/Reports/store";
import { useEffect, useRef, useState } from "react";
import { Button } from "react-bootstrap";
import { LoaderIcon } from "react-hot-toast";
import InfiniteScroll from "react-infinite-scroll-component";
import { Link, useNavigate, useParams } from "react-router-dom";
import HistoryItemAction from "./HistoryItemAction";
import "./styles.scss";
import useDeleteReport from "./useDeleteReport";

interface ReportsHistoryBoxProps {
  onCloseCanvas?: () => void;
}

const ReportsHistoryBox = ({
  onCloseCanvas = () => {},
}: ReportsHistoryBoxProps) => {
  const [page, setPage] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [historyList, setHistoryList] = useState<Record<string, any>[]>([]);
  const [openItemId, setOpenItemId] = useState<any>(null);

  const scrollableDivRef = useRef<HTMLDivElement>(null);
  const { id: reportId } = useParams();
  const navigate = useNavigate();

  const { data: { data: reportsData = [], count } = {} } = useGetReports({
    page,
    limit: 30,
  });

  useEffect(() => {
    if (reportsData?.length) {
      const element = scrollableDivRef.current;
      const previousScrollHeight = element?.scrollHeight || 0;
      const previousScrollTop = element?.scrollTop || 0;

      setHistoryList((prevList) => {
        if (page === 1) {
          return reportsData;
        } else {
          const uniqueReports = reportsData.filter(
            (newItem: any) =>
              !prevList.some((existing: any) => existing.id === newItem.id)
          );
          return [...prevList, ...uniqueReports];
        }
      });
      setTotalCount(count);

      if (element) {
        const newScrollHeight = element.scrollHeight;
        element.scrollTop =
          previousScrollTop + (newScrollHeight - previousScrollHeight);
      }
    }
  }, [reportsData, page]);

  const handleNext = () => {
    setTimeout(() => {
      setPage(page + 1);
    }, 1000);
  };

  const handleBuildReportClick = () => {
    resetReportState();
    onCloseCanvas();
    navigate(`${REPORTS_ROUTE_PATH.BUILD_REPORTS}?edit=true`);
  };

  return (
    <div className="history-box bg-white h-100">
      <div className="ps-3 mb-3 start-new-chat">
        <Button
          variant="primary"
          className="w-100 py-2 bg-blue font-light border-blue text-decoration-none fw-bold d-flex justify-content-between align-items-center"
          onClick={handleBuildReportClick}
        >
          Build New Report
          <RiAddLine size={"22px"} color="#f9f9f9" />
        </Button>
      </div>

      <div
        className="history-box-listing-wrapper position-relative d-flex flex-column overflow-auto"
        id="scrollableDiv"
        ref={scrollableDivRef}
      >
        <InfiniteScroll
          dataLength={historyList?.length}
          loader={
            <div className="d-flex align-items-center justify-content-center">
              <LoaderIcon />
            </div>
          }
          hasMore={historyList?.length < totalCount}
          next={handleNext}
          scrollableTarget={"scrollableDiv"}
          className="h-100"
        >
          <div className="history-box-listing-wrapper-data">
            <ul className="list-unstyled m-0 history-box-listing-wrapper-data-list d-flex flex-column gap-1">
              {historyList.map((item) => (
                <li
                  className={`position-relative list-item ${reportId === item?.id ? "active-chat" : ""} ${
                    item?.is_archived ? "archived-item" : ""
                  }`}
                  key={item?.id}
                >
                  <div className="list-item-wrapper overflow-hidden">
                    <Link
                      to={`${REPORTS_ROUTE_PATH.BUILD_REPORTS}/${item?.id}`}
                      className="mb-0 text-decoration-none list-unstyled m-0 list-item-wrapper-description position-relative d-block text-truncate"
                      onClick={onCloseCanvas}
                      title={item?.title}
                    >
                      {item?.title}
                    </Link>
                    <HistoryItemAction
                      itemInfo={item}
                      openItemId={openItemId}
                      setOpenItemId={setOpenItemId}
                      setHistoryList={setHistoryList}
                      setTotalCount={setTotalCount}
                      useDeleteReport={useDeleteReport}
                    />
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </InfiniteScroll>
      </div>
    </div>
  );
};

export default ReportsHistoryBox;
