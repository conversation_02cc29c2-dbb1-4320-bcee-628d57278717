import { AddCouponModal } from "components/Common";
import { SETTINGS } from "globals";
import parse from "html-react-parser";
import { useMemo, useState } from "react";
import { Button } from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { setPlanInfo } from "stores";
import "./styles.scss";

const SubscriptionCard = ({
  plan,
  currentUpcomingSubscription,
  userDetails,
}: any) => {
  const navigate = useNavigate();
  const [showCouponModal, setShowCouponModal] = useState(false);

  const onSelectPlan = (couponResult?: any) => {
    if (isMyPlan || isUpcomingPlan) return;
    const path = couponResult?.id
      ? `${ROUTE_PATH.PAYMENT}?couponId=${couponResult?.id}`
      : ROUTE_PATH.PAYMENT;
    setPlanInfo(plan);
    navigate(path);
  };

  const isMyPlan = useMemo(
    () =>
      currentUpcomingSubscription?.currentPlan?.subscription_id === plan?.id,
    [currentUpcomingSubscription, plan],
  );

  const isUpcomingPlan = useMemo(
    () =>
      currentUpcomingSubscription?.upcomingPlan?.subscription_id === plan?.id,
    [currentUpcomingSubscription, plan],
  );

  const isNumericAmount = (amount: string): boolean => {
    const numberRegex = /^[0-9]+(\.[0-9]{1,2})?$/;
    return numberRegex.test(amount);
  };

  // const RenderPlanTimespan = ({ planInfo }: any) => {
  //   if (!planInfo?.start_date || !planInfo?.expire_date) return null;
  //   return (
  //     <div className="upcoming-plan-timespan">
  //       <h6 className="m-0 p-0 fw-bold">
  //         {convertTimestampToFormat(planInfo?.start_date)} -{" "}
  //         {convertTimestampToFormat(planInfo?.expire_date)}
  //       </h6>
  //     </div>
  //   );
  // };

  const onClickSelectPlan = () => {
    setShowCouponModal(true);
  };

  const onCloseCouponModal = () => {
    setShowCouponModal(false);
  };

  return (
    <>
      <div className="subscription-card d-flex flex-column justify-content-between">
        <div>
          <p className="subscription-card-status">
            {plan?.subscription_type === "free" ? (
              <>FREE</>
            ) : (
              <>
                {isNumericAmount(plan?.amount) ? (
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      {SETTINGS.CURRENCY_INFO.GBP.symbol}
                      {plan?.amount}/
                      {userDetails?.is_subscription ? (
                        <span>month</span>
                      ) : (
                        <span
                          className="cursor-pointer dotted-underline"
                          onClick={onClickSelectPlan}
                        >
                          month
                        </span>
                      )}
                    </div>
                    {/* {isUpcomingPlan && (
                    <div className="mt-2">
                      <RenderPlanTimespan planInfo={currentUpcomingSubscription?.upcomingPlan} />
                    </div>
                  )}
                  {isMyPlan && (
                    <div className="mt-2">
                      <RenderPlanTimespan planInfo={currentUpcomingSubscription?.currentPlan} />
                    </div>
                  )} */}
                  </div>
                ) : (
                  plan?.amount
                )}
              </>
            )}
          </p>

          <hr className="p-0 border-0" />
          <p className="subscription-card-heading">{plan?.title}</p>

          <p className="subscription-card-motive-line">{plan?.heading_one}</p>

          <div className="subscription-card-key-features">
            {parse(plan?.description || "")}
          </div>
        </div>

        {plan?.subscription_type !== "free" ? (
          <div className="action-btns position-relative z-0 mb-0 w-100">
            {isNumericAmount(plan?.amount) ? (
              <Button
                variant="link"
                className={`${isMyPlan || isUpcomingPlan ? "selected-btn" : ""} text-uppercase border-brown btn-transparent text-center d-flex align-items-center justify-content-center position-relative overflow-hidden text-decoration-none mx-auto`}
                // ${(!isUpcomingPlan && currentUpcomingSubscription?.upcomingPlan && !isMyPlan && plan?.amount < currentUpcomingSubscription?.currentPlan?.payment_meta_data?.plan_price) ? 'd-none' : ''}
                onClick={onSelectPlan}
              >
                {isMyPlan
                  ? "Current Plan"
                  : isUpcomingPlan
                    ? "Upcoming Plan"
                    : "Select Plan"}
              </Button>
            ) : (
              <Link
                to={ROUTE_PATH.CONTACT_US}
                className="text-uppercase border-brown btn-transparent text-center d-flex align-items-center justify-content-center position-relative overflow-hidden text-decoration-none mx-auto"
              >
                Contact Us
              </Link>
            )}
          </div>
        ) : (
          <>
            {plan?.subscription_type === "free" && isMyPlan && (
              <div className="action-btns position-relative z-0 mb-0 w-100">
                <Button
                  variant="link"
                  className="selected-btn text-uppercase border-brown btn-transparent text-center d-flex align-items-center justify-content-center position-relative overflow-hidden text-decoration-none mx-auto"
                >
                  Current Plan
                </Button>
              </div>
            )}
          </>
        )}
      </div>
      <AddCouponModal
        show={showCouponModal}
        onClose={onCloseCouponModal}
        onSelectPlan={onSelectPlan}
      />
    </>
  );
};

export default SubscriptionCard;
