import { OrgUserRole } from "globals";
import { UserInterface } from "types";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface UserInfo {
  user: UserInterface | null;
  token: string | null;
  loginType: string | null;
  refreshToken?: string | null;
  tokenExpiresAt?: number | null;
}

interface PayloadInterface {
  userInfo: UserInfo;
  rememberMeInfo?: Record<string, string>;
}

const initialState = {
  userInfo: {
    user: {} as UserInterface,
    token: "",
    loginType: null,
    refreshToken: null,
    tokenExpiresAt: null,
  },
  rememberMeInfo: {
    email: "",
  },
  subscriptionInfo: {},
};

const userStore = (set: any) => ({
  ...initialState,
  setUserInfo: (data: UserInfo) =>
    set((state: PayloadInterface) => ({ ...state, userInfo: data })),
  setRememberMeInfo: (data: Record<string, string>) =>
    set((state: PayloadInterface) => ({ ...state, rememberMeInfo: data })),
  setOrganisation: (data: any) =>
    set((state: PayloadInterface) => {
      if (!state.userInfo.user) {
        return { userInfo: state.userInfo };
      }

      const isAdmin =
        state.userInfo.user.organizationMember?.role === OrgUserRole.ADMIN;
      const isUser =
        state.userInfo.user.organizationMember?.role === OrgUserRole.USER;
      const updatedUser = {
        ...state.userInfo.user,
        ...(isAdmin || isUser
          ? {
              organizationMember: {
                ...state.userInfo.user.organizationMember,
                organization: data,
              },
            }
          : { ownedOrganization: data }),
      };

      return {
        userInfo: {
          ...state.userInfo,
          user: updatedUser,
        },
      };
    }),
  setOrganisationMember: (data: any) =>
    set((state: PayloadInterface) => {
      if (!state.userInfo.user) {
        return { userInfo: state.userInfo };
      }
      const updatedUser = {
        ...state.userInfo.user,
        ...(state.userInfo.user.organizationMember
          ? {
              organizationMember: {
                ...state.userInfo.user.organizationMember,
                ...data,
              },
            }
          : { organizationMember: data }),
      };

      return {
        userInfo: {
          ...state.userInfo,
          user: updatedUser,
        },
      };
    }),
  setSubscriptionInfo: (data: any) =>
    set((state: PayloadInterface) => ({
      ...state,
      subscriptionInfo: data,
    })),
  resetUserState: () =>
    set((state: PayloadInterface) => ({
      ...initialState,
      rememberMeInfo: state.rememberMeInfo,
    })),
});

const useUserStore = create(
  devtools(
    persist(userStore, {
      name: "user",
    }),
  ),
);

export const getAuthToken = () => {
  // access the zustand store outside of React.
  return useUserStore.getState().userInfo.token;
};

export const getOrganisationInfo = (): any => {
  const user = useUserStore.getState().userInfo.user;
  const isOrgAdmin = user.organizationMember?.role === OrgUserRole.ADMIN;
  const isOrgUser = user.organizationMember?.role === OrgUserRole.USER;

  return isOrgAdmin || isOrgUser
    ? user.organizationMember?.organization
    : user.ownedOrganization;
};

export const {
  setUserInfo,
  resetUserState,
  setRememberMeInfo,
  setOrganisation,
  setSubscriptionInfo,
  setOrganisationMember,
} = useUserStore.getState();

export default useUserStore;
