import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals";
import { apiClient } from "./apiClient";

export const useCreatePaymentIntentQuery = ({
  isSubscriptionPaid = false,
  ...payload
}: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.post(
        API_ENDPOINTS.CREATE_PAYMENT_INTENT,
        payload,
      );
      return response?.data;
    },
    queryKey: ["payment-intent", payload?.subscription_id],
    enabled: !!payload?.subscription_id && isSubscriptionPaid === null,
    refetchOnWindowFocus: false,
  });

export const useConfigQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_PAYMENT_CONFIG);
      return response?.data;
    },
    queryKey: ["config"],
  });

export const usePaymentStatusMutation = () =>
  useMutation({
    mutationFn: async (payload: Record<string, string>) => {
      const response = await apiClient.post(
        API_ENDPOINTS.PAYMENT_STATUS,
        payload,
      );
      return response;
    },
  });

export const useWordCountPaymentStatusMutation = () =>
  useMutation({
    mutationFn: async (payload: Record<string, string>) => {
      const response = await apiClient.post(
        API_ENDPOINTS.WORD_COUNT_PAYMENT_STATUS,
        payload,
      );
      return response;
    },
  });

export const useCreatePaymentMutation = () =>
  useMutation({
    mutationFn: async (payload: Record<string, string>) => {
      const response = await apiClient.post(
        API_ENDPOINTS.CREATE_PAYMENT_INTENT,
        payload,
      );
      return response;
    },
  });

export const useGetPaymentDetails = (payload: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.post(
        API_ENDPOINTS.PAYMENT_DETAILS,
        payload,
      );
      return response?.data;
    },
    queryKey: ["payment-details", payload?.subscription_id, payload?.team_size],
    enabled: !!payload?.subscription_id,
  });

export const useAddCardMutation = () =>
  useMutation({
    mutationFn: async (payload: Record<string, string>) => {
      const response = await apiClient.post(API_ENDPOINTS.ADD_CARD, payload);
      return response;
    },
  });

export const useDeleteCardMutation = () =>
  useMutation({
    mutationFn: async (payload: Record<string, string>) => {
      const response = await apiClient.delete(
        API_ENDPOINTS.DELETE_CARD(payload.id),
      );
      return response;
    },
  });

export const useGetCards = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_CARDS);
      return response?.data;
    },
    queryKey: ["cards"],
  });

export const useSetDefaultCardMutation = () =>
  useMutation({
    mutationFn: async (payload: Record<string, string>) => {
      const response = await apiClient.put(
        API_ENDPOINTS.SET_DEFAULT_CARD(payload.id),
      );
      return response;
    },
  });

export const useUpdateUpcomingPlanMutation = () =>
  useMutation({
    mutationFn: async (payload: Record<string, string>) => {
      const response = await apiClient.post(
        API_ENDPOINTS.UPDATE_UPCOMING_PLAN,
        payload,
      );
      return response;
    },
  });

export const useGetPaymentDetailsMutation = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      const response = await apiClient.post(
        API_ENDPOINTS.PAYMENT_DETAILS,
        payload,
      );
      return response;
    },
  });

export const useGetWordCountPaymentDetails = (payload: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.post(
        API_ENDPOINTS.WORD_COUNT_PAYMENT_DETAILS,
        payload,
      );
      return response?.data;
    },
    queryKey: ["word-count-payment-details", payload?.word_limit],
  });

export const useWordCountPaymentDetailsMutation = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      const response = await apiClient.post(
        API_ENDPOINTS.WORD_COUNT_PAYMENT_DETAILS,
        payload,
      );
      return response;
    },
  });

export const useCreateWordCountPaymentIntent = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      const response = await apiClient.post(
        API_ENDPOINTS.CREATE_WORD_COUNT_PAYMENT_INTENT,
        payload,
      );
      return response;
    },
  });
