import { Mo<PERSON>, Spinner } from "react-bootstrap";

const GenerateReportModal = ({ show }: { show: boolean }) => {
  return (
    <Modal show={show} size="lg" centered>
      <Modal.Header className="border-0 pb-0 mt-2">
        <Modal.Title className="w-100 text-center">
          <h3 className="mb-0 fw-bold">Generating Your Report</h3>
        </Modal.Title>
      </Modal.Header>

      <Modal.Body>
        <div className="text-center">
          <div className="d-flex flex-column align-items-center justify-content-center py-5">
            <Spinner animation="border" role="status" />
            <div className="mt-3 text-muted">
              Your report is being generated based on current inputs.
              <br />
              Please wait...
            </div>
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer className="border-0 mb-2">
        <div className="small text-center w-100">
          *A typical report takes 3-5 minutes.
          <br />
          Please do not refresh or close the page.
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default GenerateReportModal;
