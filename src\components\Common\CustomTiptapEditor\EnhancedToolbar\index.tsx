import {
  Ri<PERSON>lignCenter,
  RiAlignJustify,
  RiAlignLeft,
  RiAlignRight,
  RiArrowGoBackFill,
  RiArrowGoForwardFill,
  RiBold,
  RiDoubleQuotesL,
  RiFormatClear,
  RiH1,
  RiH2,
  RiH3,
  RiItalic,
  RiListOrdered,
  RiListUnordered,
  RiParagraph,
  RiSeparator,
  RiStrikethrough,
  RiUnderline,
} from "@remixicon/react";
import { Editor } from "@tiptap/react";
import EditorToolbar from "features/Reports/components/ReportBuilder/SectionBlock/EditorToolbar";
import { Button, ButtonGroup, Dropdown } from "react-bootstrap";
import "./styles.scss";

interface EnhancedToolbarProps {
  editor: Editor | null;
  sectionId: string | undefined;
}

const EnhancedToolbar = ({ editor, sectionId }: EnhancedToolbarProps) => {
  if (!editor) {
    return null;
  }

  // Helper function to check if editor is truly empty
  const isEditorEmpty = () => {
    const doc = editor.state.doc;
    let hasContent = false;

    doc.descendants((node) => {
      if (node.isText && node.text && node.text.trim()) {
        hasContent = true;
        return false; // Stop traversing
      }
      if (
        node.type.name !== "doc" &&
        node.type.name !== "paragraph" &&
        node.type.name !== "text"
      ) {
        hasContent = true;
        return false; // Stop traversing
      }
      return true;
    });

    return !hasContent;
  };

  // Undo/Redo actions
  const handleUndo = () => editor.chain().focus().undo().run();
  const handleRedo = () => editor.chain().focus().redo().run();

  // Text formatting actions
  const handleBold = () => editor.chain().focus().toggleBold().run();
  const handleItalic = () => editor.chain().focus().toggleItalic().run();
  const handleUnderline = () => editor.chain().focus().toggleUnderline().run();
  const handleStrikethrough = () => editor.chain().focus().toggleStrike().run();
  const handleClearFormatting = () =>
    editor.chain().focus().clearNodes().unsetAllMarks().run();

  // Heading actions
  const handleHeading1 = () =>
    editor.chain().focus().toggleHeading({ level: 1 }).run();
  const handleHeading2 = () =>
    editor.chain().focus().toggleHeading({ level: 2 }).run();
  const handleHeading3 = () =>
    editor.chain().focus().toggleHeading({ level: 3 }).run();
  const handleParagraph = () => editor.chain().focus().setParagraph().run();

  // List actions
  const handleBulletList = () =>
    editor.chain().focus().toggleBulletList().run();
  const handleOrderedList = () =>
    editor.chain().focus().toggleOrderedList().run();

  // Block actions
  const handleBlockquote = () =>
    editor.chain().focus().toggleBlockquote().run();
  const handleHorizontalRule = () =>
    editor.chain().focus().setHorizontalRule().run();

  // Table actions
  const handleAddRowBefore = () => editor.chain().focus().addRowBefore().run();
  const handleAddRowAfter = () => editor.chain().focus().addRowAfter().run();
  const handleDeleteRow = () => editor.chain().focus().deleteRow().run();
  const handleAddColumnBefore = () =>
    editor.chain().focus().addColumnBefore().run();
  const handleAddColumnAfter = () =>
    editor.chain().focus().addColumnAfter().run();
  const handleDeleteColumn = () => editor.chain().focus().deleteColumn().run();
  const handleDeleteTable = () => editor.chain().focus().deleteTable().run();
  const handleMergeCells = () => editor.chain().focus().mergeCells().run();

  const isInTable = editor.isActive("table");

  return (
    <div className="enhanced-toolbar sticky-top bg-white border-bottom shadow-sm px-2">
      <div className="toolbar-container d-flex flex-wrap align-items-center gap-2 p-3">
        <div className="d-flex gap-2 align-items-center w-100 flex-wrap">
          <div>
            <ButtonGroup>
              <Button
                variant="outline-secondary"
                size="sm"
                onClick={handleUndo}
                title="Undo"
                disabled={!editor.can().undo() || isEditorEmpty()}
              >
                <RiArrowGoBackFill size={18} />
              </Button>

              <Button
                variant="outline-secondary"
                size="sm"
                onClick={handleRedo}
                title="Redo"
                disabled={!editor.can().redo()}
              >
                <RiArrowGoForwardFill size={18} />
              </Button>
              <Button
                variant={
                  editor.isActive("bold") ? "primary" : "outline-secondary"
                }
                size="sm"
                onClick={handleBold}
                title="Bold"
              >
                <RiBold size={16} />
              </Button>
              <Button
                variant={
                  editor.isActive("italic") ? "primary" : "outline-secondary"
                }
                size="sm"
                onClick={handleItalic}
                title="Italic"
              >
                <RiItalic size={16} />
              </Button>
              <Button
                variant={
                  editor.isActive("underline") ? "primary" : "outline-secondary"
                }
                size="sm"
                onClick={handleUnderline}
                title="Underline"
              >
                <RiUnderline size={16} />
              </Button>
              <Button
                variant={
                  editor.isActive("strike") ? "primary" : "outline-secondary"
                }
                size="sm"
                onClick={handleStrikethrough}
                title="Strikethrough"
              >
                <RiStrikethrough size={16} />
              </Button>
              <Button
                variant="outline-secondary"
                size="sm"
                onClick={handleClearFormatting}
                title="Clear Formatting"
              >
                <RiFormatClear size={16} />
              </Button>

              {/* Heading Dropdown */}
              <Dropdown as={ButtonGroup} className="toolbar-dropdown">
                <Dropdown.Toggle
                  // variant={
                  //   editor.isActive("heading", { level: 1 }) ||
                  //   editor.isActive("heading", { level: 2 }) ||
                  //   editor.isActive("heading", { level: 3 })
                  //     ? "primary"
                  //     : editor.isActive("paragraph")
                  //       ? "primary"
                  //       : "outline-secondary"
                  // }
                  variant=""
                  size="sm"
                  id="dropdown-heading"
                  title="Headings"
                >
                  {editor.isActive("heading", { level: 1 }) ? (
                    <RiH1 size={16} />
                  ) : editor.isActive("heading", { level: 2 }) ? (
                    <RiH2 size={16} />
                  ) : editor.isActive("heading", { level: 3 }) ? (
                    <RiH3 size={16} />
                  ) : (
                    <RiParagraph size={16} />
                  )}
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item
                    active={editor.isActive("paragraph")}
                    onClick={handleParagraph}
                  >
                    <RiParagraph size={16} className="me-2" />
                    Paragraph
                  </Dropdown.Item>
                  <Dropdown.Item
                    active={editor.isActive("heading", { level: 1 })}
                    onClick={handleHeading1}
                  >
                    <RiH1 size={16} className="me-2" />
                    Heading 1
                  </Dropdown.Item>
                  <Dropdown.Item
                    active={editor.isActive("heading", { level: 2 })}
                    onClick={handleHeading2}
                  >
                    <RiH2 size={16} className="me-2" />
                    Heading 2
                  </Dropdown.Item>
                  <Dropdown.Item
                    active={editor.isActive("heading", { level: 3 })}
                    onClick={handleHeading3}
                  >
                    <RiH3 size={16} className="me-2" />
                    Heading 3
                  </Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>

              {/* Alignment Dropdown */}
              <Dropdown as={ButtonGroup} className="toolbar-dropdown">
                <Dropdown.Toggle
                  variant={
                    editor.isActive({ textAlign: "left" }) ||
                    editor.isActive({ textAlign: "center" }) ||
                    editor.isActive({ textAlign: "right" }) ||
                    editor.isActive({ textAlign: "justify" })
                      ? "primary"
                      : "outline-secondary"
                  }
                  size="sm"
                  id="dropdown-align"
                  title="Alignment"
                >
                  {editor.isActive({ textAlign: "left" }) ? (
                    <RiAlignLeft size={16} />
                  ) : editor.isActive({ textAlign: "center" }) ? (
                    <RiAlignCenter size={16} />
                  ) : editor.isActive({ textAlign: "right" }) ? (
                    <RiAlignRight size={16} />
                  ) : editor.isActive({ textAlign: "justify" }) ? (
                    <RiAlignJustify size={16} />
                  ) : (
                    <RiAlignLeft size={16} />
                  )}
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item
                    active={editor.isActive({ textAlign: "left" })}
                    onClick={() =>
                      editor.chain().focus().setTextAlign("left").run()
                    }
                  >
                    <RiAlignLeft size={16} className="me-2" />
                    Align Left
                  </Dropdown.Item>
                  <Dropdown.Item
                    active={editor.isActive({ textAlign: "center" })}
                    onClick={() =>
                      editor.chain().focus().setTextAlign("center").run()
                    }
                  >
                    <RiAlignCenter size={16} className="me-2" />
                    Align Center
                  </Dropdown.Item>
                  <Dropdown.Item
                    active={editor.isActive({ textAlign: "right" })}
                    onClick={() =>
                      editor.chain().focus().setTextAlign("right").run()
                    }
                  >
                    <RiAlignRight size={16} className="me-2" />
                    Align Right
                  </Dropdown.Item>
                  <Dropdown.Item
                    active={editor.isActive({ textAlign: "justify" })}
                    onClick={() =>
                      editor.chain().focus().setTextAlign("justify").run()
                    }
                  >
                    <RiAlignJustify size={16} className="me-2" />
                    Justify
                  </Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>

              <Button
                variant={
                  editor.isActive("bulletList")
                    ? "primary"
                    : "outline-secondary"
                }
                size="sm"
                onClick={handleBulletList}
                title="Bullet List"
              >
                <RiListUnordered size={16} />
              </Button>
              <Button
                variant={
                  editor.isActive("orderedList")
                    ? "primary"
                    : "outline-secondary"
                }
                size="sm"
                onClick={handleOrderedList}
                title="Numbered List"
              >
                <RiListOrdered size={16} />
              </Button>
              <Button
                variant={
                  editor.isActive("blockquote")
                    ? "primary"
                    : "outline-secondary"
                }
                size="sm"
                onClick={handleBlockquote}
                title="Quote"
              >
                <RiDoubleQuotesL size={16} />
              </Button>
              {/* <Button
            variant={
              editor.isActive("codeBlock") ? "primary" : "outline-secondary"
            }
            size="sm"
            onClick={handleCodeBlock}
            title="Code Block"
          >
            <RiCodeBoxFill size={16} />
          </Button> */}
              <Button
                variant="outline-secondary"
                size="sm"
                onClick={handleHorizontalRule}
                title="Horizontal Rule"
              >
                <RiSeparator size={16} />
              </Button>
              <EditorToolbar editor={editor} sectionId={sectionId} />
            </ButtonGroup>
          </div>
          {/* Table Actions - Only show when in table */}
          <TableActionButton
            onClick={handleAddRowBefore}
            disabled={!isInTable}
            title="Add Row Before"
          />
          <TableActionButton
            onClick={handleAddRowAfter}
            disabled={!isInTable}
            title="Add Row After"
          />
          <TableActionButton
            onClick={handleDeleteRow}
            disabled={!isInTable}
            title="Delete Row"
          />
          <TableActionButton
            onClick={handleAddColumnBefore}
            disabled={!isInTable}
            title="Add Column Before"
          />
          <TableActionButton
            onClick={handleAddColumnAfter}
            disabled={!isInTable}
            title="Add Column After"
          />
          <TableActionButton
            onClick={handleDeleteColumn}
            disabled={!isInTable}
            title="Delete Column"
          />
          <TableActionButton
            onClick={handleDeleteTable}
            disabled={!isInTable}
            title="Delete Table"
          />
          <TableActionButton
            onClick={handleMergeCells}
            disabled={!isInTable}
            title="Merge Cells"
          />
        </div>
      </div>
    </div>
  );
};

export default EnhancedToolbar;

const TableActionButton = ({ onClick, title, disabled }: any) => (
  <Button
    style={{
      fontSize: "12px",
      padding: "0.25rem 0.5rem",
      backgroundColor: disabled ? "#e0e0e0" : "#f0f0f0",
      borderColor: disabled ? "#bbb" : "#ccc",
      color: disabled ? "#888" : "#000",
      fontWeight: "bold",
      opacity: disabled ? 0.6 : 1,
      cursor: disabled ? "not-allowed" : "pointer",
    }}
    onClick={onClick}
    disabled={disabled}
  >
    {title}
  </Button>
);
