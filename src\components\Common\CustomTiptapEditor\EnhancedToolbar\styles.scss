.enhanced-toolbar {
  z-index: 1020;
  border-bottom: 1px solid #dee2e6;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .toolbar-container {
    max-width: 100%;
    scrollbar-width: thin;
    scrollbar-color: #ad986f #f8f9fa;
    overflow-y: visible;
    position: relative;

    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f8f9fa;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #ad986f;
      border-radius: 3px;

      &:hover {
        background: #8b7355;
      }
    }

    .btn-group {
      flex-shrink: 0;

      .btn {
        border-color: #dee2e6;
        font-size: 0.875rem;
        padding: 0.375rem 0.5rem;
        min-width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        &:active {
          transform: translateY(0);
        }

        &.btn-primary {
          background-color: #ad986f;
          border-color: #ad986f;
          color: white;

          &:hover {
            background-color: #8b7355;
            border-color: #8b7355;
          }

          &:focus {
            box-shadow: 0 0 0 0.2rem rgba(173, 152, 111, 0.25);
          }
        }

        &.btn-outline-primary {
          color: #ad986f;
          border-color: #ad986f;

          &:hover {
            background-color: #ad986f;
            border-color: #ad986f;
            color: white;
          }

          &:focus {
            box-shadow: 0 0 0 0.2rem rgba(173, 152, 111, 0.25);
          }
        }

        &.btn-outline-secondary {
          color: #6c757d;
          border-color: #dee2e6;

          &:hover {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            color: #495057;
          }

          &:focus {
            box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
          }
        }

        &.btn-outline-success {
          color: #198754;
          border-color: #198754;

          &:hover {
            background-color: #198754;
            border-color: #198754;
            color: white;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;

            &:hover {
              background-color: transparent;
              color: #198754;
              transform: none;
            }
          }
        }

        &.btn-outline-info {
          color: #0dcaf0;
          border-color: #0dcaf0;

          &:hover {
            background-color: #0dcaf0;
            border-color: #0dcaf0;
            color: white;
          }
        }

        &.btn-outline-warning {
          color: #ffc107;
          border-color: #ffc107;

          &:hover {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #000;
          }
        }

        &.btn-danger {
          background-color: #dc3545;
          border-color: #dc3545;
          color: white;

          &:hover {
            background-color: #c82333;
            border-color: #bd2130;
          }
        }

        &.btn-warning {
          background-color: #ffc107;
          border-color: #ffc107;
          color: #000;

          &:hover {
            background-color: #e0a800;
            border-color: #d39e00;
          }
        }

        svg + span,
        span + svg {
          margin-left: 0.25rem;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .toolbar-container {
      padding: 0.75rem;
      gap: 0.5rem;

      .btn-group {
        .btn {
          min-width: 32px;
          height: 32px;
          padding: 0.25rem 0.375rem;
          font-size: 0.8rem;

          svg {
            width: 14px;
            height: 14px;
          }
        }
      }
    }
  }

  @media (max-width: 576px) {
    .toolbar-container {
      flex-direction: column;
      align-items: stretch;
      gap: 0.75rem;

      .btn-group {
        justify-content: center;
      }
    }
  }

  .toolbar-dropdown {
    position: static !important;
    z-index: 1050;
    .dropdown-menu {
      z-index: 2000 !important;
      position: absolute !important;

      .dropdown-item {
        &:hover {
          background-color: #ad986f;
          color: white;
        }

        &:active,
        &.active {
          background-color: #ad986f;
          color: white;
        }
      }
    }
  }
}

.section-content {
  .enhanced-toolbar {
    position: sticky;
    top: 0;
    margin: -1rem -1rem 1rem -1rem;
  }
}
