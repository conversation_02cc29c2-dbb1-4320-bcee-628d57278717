import { RiCloseLine } from "@remixicon/react";
import { useVerifyOTPMutation } from "api";
import { BackButton } from "components";
import AuthHeaderData from "pages/Auth/AuthHeaderData";
import OTPInputComponent from "pages/Auth/OTPVerify/OTPInputComponent";
import ResendOTP from "pages/Auth/OTPVerify/ResendOTP";
import { useEffect, useState } from "react";
import { Button, Modal } from "react-bootstrap";
import toast from "react-hot-toast";
import { useNavigate, useSearchParams } from "react-router-dom";
import "./styles.scss";

interface VerifyOTPModalProps {
  show: boolean;
  handleClose: () => void;
  handleShow: () => void;
}

const VerifyOTPModal: React.FC<VerifyOTPModalProps> = ({
  show,
  handleClose,
}) => {
  const [searchParams] = useSearchParams();
  const otpId = searchParams.get("id");
  const email = searchParams.get("email");
  const navigate = useNavigate();

  const [otp, setOTP] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState(null);

  const { mutateAsync: verifyOTPMutation } = useVerifyOTPMutation();

  const handleVerify = async () => {
    if (otp?.length !== 4) {
      setError("Please enter otp");
      return;
    }
    setError(null);
    const payload = {
      id: otpId,
      code: otp,
    };
    const { API_URL, REDIRECT_URL }: any = {};
    const result: any = await verifyOTPMutation({
      url: API_URL,
      payload,
    });
    if (result?.success) {
      const { resetPasswordToken } = result?.data ?? {};
      navigate(
        resetPasswordToken
          ? `${REDIRECT_URL}?resetPasswordToken=${resetPasswordToken}`
          : REDIRECT_URL,
      );
    } else {
      toast.error(result?.message);
    }
  };

  useEffect(() => {
    const keyDownHandler = (event: any) => {
      if (event.key === "Enter") {
        handleVerify();
      }
    };

    document.addEventListener("keydown", keyDownHandler);

    return () => {
      document.removeEventListener("keydown", keyDownHandler);
    };
  }, [otp]);

  const headerData = {
    heading: "Enter OTP",
    description: `A 4 digit code has been sent to your registered email <br class="d-lg-block d-none" /> ID ${email}`,
  };

  return (
    <Modal
      show={show}
      onHide={handleClose}
      backdrop="static"
      keyboard={false}
      centered
    >
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={handleClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>

        <div
          className="auth-form w-100 d-flex justify-content-center align-items-center flex-column m-0"
          style={{ gap: "30px" }}
        >
          <AuthHeaderData
            heading={headerData.heading}
            description={headerData.description}
          />

          <div className="website-form w-100">
            <form className="d-flex flex-column" style={{ gap: "30px" }}>
              <OTPInputComponent
                otp={otp}
                setOTP={setOTP}
                message={message}
                error={error}
              />

              <div
                className="action-btns d-flex flex-column"
                style={{ gap: "30px" }}
              >
                <Button
                  type="button"
                  className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                  onClick={handleVerify}
                >
                  Submit
                </Button>

                <BackButton />
              </div>

              <div className="interaction-btns d-flex align-items-center font-gray">
                <ResendOTP
                  otpId={otpId}
                  setMessage={setMessage}
                  setOTP={setOTP}
                />
              </div>
            </form>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default VerifyOTPModal;
