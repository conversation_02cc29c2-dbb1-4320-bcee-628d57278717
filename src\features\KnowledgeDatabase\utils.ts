export function getAllParentFolders(
  data: Item[],
  startId: string,
): { id: string; name: string }[] {
  // 1) grab the full chain
  const full = getParent<PERSON>hain(data, startId);

  // 2) filter to folders only, then drop the starting file if any
  const folders = full.filter((x) => x.type === "folder");

  // 3) reverse so root comes first
  return folders.reverse().map((f) => ({ id: f.id, name: f.name }));
}

export type Base = {
  id: string;
  name: string;
  uploaded_at: string;
  permission: string;
};

export type Folder = Base & {
  type: "folder";
  parent_id?: string;
};

export type File = Base & {
  type: "file";
  parent_id: string;
};

export type Item = Folder | File;

/**
 * Returns an array of the given item + all its ancestor folders,
 * in order from the item itself up to the top-level folder.
 */
export function getParentChain(data: Item[], startId: string): Item[] {
  const chain: Item[] = [];
  const seen = new Set<string>();

  // Look up the starting item (could be a file or folder)
  let current = data.find((x) => x.id === startId);
  while (current) {
    chain.push(current);

    // If no parent, we’re at the top
    const pid = current.parent_id;
    if (!pid || seen.has(pid)) break;

    seen.add(pid);
    // Move up: find the folder whose id === pid
    const next = data.find((x) => x.id === pid && x.type === "folder");
    if (!next) break;
    current = next;
  }

  return chain;
}
