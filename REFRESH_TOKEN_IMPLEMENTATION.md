# Auth0 Refresh Token Rotation Implementation

This document describes the implementation of Auth0 refresh token rotation in the ChatRedact application.

## Overview

The application now supports automatic token refresh for both SSO (Social Sign-On) and embedded login methods. Tokens are automatically refreshed 5 minutes before expiration, and failed API requests due to expired tokens will trigger an automatic refresh attempt.

## Key Components

### 1. Token Refresh Service (`src/services/tokenRefreshService.ts`)

A centralized service that handles:
- Token expiration detection
- Automatic token refresh for both SSO and embedded login
- Background token refresh scheduling
- Token refresh queue management to prevent multiple simultaneous refresh attempts

### 2. Enhanced API Client (`src/api/apiClient.tsx`)

Updated with response interceptors that:
- Detect 401 (Unauthorized) responses
- Automatically attempt token refresh before showing session expired modal
- Queue failed requests during token refresh and retry them with new tokens
- Handle refresh failures gracefully

### 3. Updated User Store (`src/stores/user.ts`)

Enhanced to store:
- `refreshToken`: The refresh token for embedded login
- `tokenExpiresAt`: Token expiration timestamp for better tracking

### 4. Auth0 Provider Configuration (`src/providers/Auth0SSOProvider.tsx`)

Updated with:
- `useRefreshTokens={true}`: Enable refresh token support
- `cacheLocation="localstorage"`: Store tokens in localStorage
- `useRefreshTokensFallback={true}`: Fallback for refresh token support

### 5. Token Refresh Hook (`src/hooks/useTokenRefresh.ts`)

A React hook that provides:
- Manual token refresh functionality
- Token expiration checking
- Token information retrieval
- Integration with the token refresh service

## Configuration Changes

### Environment Variables

Updated `.env` file:
```
VITE_AUTH0_SCOPE="openid email profile offline_access https://chatredactai/session_id"
VITE_AUTH0_LOGIN_RESPONSE_TYPE="code"
```

Key changes:
- `offline_access` scope is required for refresh tokens
- Response type changed to `"code"` for embedded login to support refresh tokens

### Auth0 Dashboard Configuration

Ensure your Auth0 application has:
1. **Refresh Token Rotation** enabled
2. **Absolute Expiration** configured (recommended: 30 days)
3. **Inactivity Expiration** configured (recommended: 7 days)
4. **Allow Offline Access** enabled

## How It Works

### SSO Login Flow
1. User clicks "Sign in with SSO"
2. Auth0 React SDK handles authentication
3. `getAccessTokenSilently()` automatically manages token refresh
4. Token refresh service schedules background refresh

### Embedded Login Flow
1. User enters credentials
2. Auth0 WebAuth performs login with `responseType: "code"`
3. Authorization code is exchanged for access token + refresh token
4. Both tokens are stored in user store
5. Token refresh service schedules background refresh using stored refresh token

### Automatic Token Refresh
1. Token refresh service schedules refresh 5 minutes before expiration
2. When refresh time arrives, appropriate refresh method is called:
   - SSO: `getAccessTokenSilently()` with cache bypass
   - Embedded: `webAuth.renewAuth()` with refresh token
3. New token is stored and new refresh is scheduled

### API Request Handling
1. API request fails with 401 status
2. Response interceptor catches the error
3. Token refresh is attempted automatically
4. If successful, original request is retried with new token
5. If refresh fails, user sees session expired modal

## Testing the Implementation

### Debug Component

A debug component (`src/components/TokenRefreshDebug.tsx`) is available for testing:

```tsx
import TokenRefreshDebug from "components/TokenRefreshDebug";

// Add to any page for debugging
<TokenRefreshDebug />
```

### Manual Testing Steps

1. **Login Test**: Verify both SSO and embedded login work
2. **Token Storage**: Check that refresh tokens are stored (embedded login only)
3. **Background Refresh**: Wait for automatic refresh (check console logs)
4. **API Retry**: Make API calls with expired tokens to test retry mechanism
5. **Session Persistence**: Refresh browser to ensure session persists

### Console Logging

The implementation includes console logs for debugging:
- `"Automatically refreshing token..."` - Background refresh triggered
- `"SSO token refresh failed:"` - SSO refresh error
- `"Embedded token refresh failed:"` - Embedded refresh error

## Security Considerations

1. **Refresh Token Storage**: Refresh tokens are stored in localStorage (encrypted by Auth0)
2. **Token Rotation**: Each refresh generates a new refresh token (if enabled in Auth0)
3. **Automatic Cleanup**: Tokens are cleared on logout or authentication errors
4. **Request Queuing**: Multiple simultaneous requests don't cause multiple refresh attempts

## Troubleshooting

### Common Issues

1. **Refresh Token Not Available**
   - Ensure `offline_access` scope is included
   - Check Auth0 application settings allow offline access
   - Verify response type is `"code"` for embedded login

2. **Automatic Refresh Not Working**
   - Check console for error messages
   - Verify token expiration time is correctly parsed
   - Ensure Auth0 context is properly set for SSO

3. **API Requests Still Failing**
   - Check if refresh token is expired
   - Verify Auth0 application configuration
   - Check network connectivity

### Debug Steps

1. Enable browser developer tools
2. Check localStorage for stored tokens
3. Monitor network requests for refresh attempts
4. Use the TokenRefreshDebug component
5. Check console logs for error messages

## Future Enhancements

1. **Token Refresh Notifications**: Show user-friendly messages during refresh
2. **Offline Support**: Handle token refresh when offline
3. **Multiple Tab Synchronization**: Coordinate token refresh across browser tabs
4. **Refresh Token Encryption**: Additional client-side encryption for refresh tokens
5. **Metrics and Monitoring**: Track refresh success rates and failures

## Dependencies

- `@auth0/auth0-react`: ^2.2.4
- `auth0-js`: ^9.26.1
- `axios`: ^1.9.0
- `zustand`: ^4.5.4

## Support

For issues related to this implementation, check:
1. Auth0 documentation on refresh tokens
2. Console logs for specific error messages
3. Network tab for failed refresh requests
4. Auth0 dashboard logs for authentication events
