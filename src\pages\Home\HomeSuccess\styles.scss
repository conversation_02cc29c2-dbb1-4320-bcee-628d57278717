@use "/src/styles/mixins/mixins.scss" as mixins;

.home-success-section {
  padding: 30px;
  gap: 30px;
  overflow: hidden;
  position: relative;
  z-index: 5;
  height: calc(100vh - 195px);
  border-radius: 12px;
  box-shadow:
    0px 65px 47px 0px rgba(26, 26, 26, 0.04),
    0px 100px 80px 0px rgba(26, 26, 26, 0.05);

  @media (min-height: 500px) and (max-height: 1070px) {
    overflow-x: hidden;
    overflow-y: auto;
    @include mixins.slim-scrollbar;
  }

  @media only screen and (max-width: 991px) {
    height: auto;
  }

  @media only screen and (max-width: 576px) {
    padding: 15px 15px 30px 15px;
  }

  @include mixins.breadcrumb-commom-style;

  &-container {
    .auth-form {
      @media only screen and (max-width: 576px) {
        padding: 0px 0px 30px 0px;
      }

      &-heading {
        @media only screen and (max-width: 768px) {
          font-size: 20px;
        }
      }

      &-description {
        @media only screen and (min-width: 1024px) {
          width: 60%;
          margin: 0 auto;
        }
      }

      .website-form .form-control {
        @media only screen and (max-width: 576px) {
          font-size: 16px;
        }
      }

      &-card {
        padding: 30px 60px;
        border-radius: 20px;
        border: 5px solid #fcfcfc;
        box-shadow:
          0px 65px 47px 0px rgba(26, 26, 26, 0.04),
          0px 100px 80px 0px rgba(26, 26, 26, 0.05);

        @media only screen and (min-width: 992px) and (max-width: 1199px) {
          padding: 30px 40px;

          .card-body {
            padding: 0px;
          }
        }

        @media only screen and (max-width: 768px) {
          padding: 10px 0px;
        }
      }
    }
  }
}
