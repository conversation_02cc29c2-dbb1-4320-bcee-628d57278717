import { useGetCards } from "api";
import {
  AddNewCardModal,
  BackBreadcrumb,
  CardTileSkeleton,
  PaymentToolbar,
} from "components";
import React, { useEffect, useState } from "react";
import CardTile from "./CardTile";
import "./styles.scss";

const PaymentListing: React.FC = () => {
  const [showAddNewCardModal, setShowAddNewCardModal] = useState(false);

  const handleAddNewCardClose = () => setShowAddNewCardModal(false);
  const handleAddNewCardShow = () => setShowAddNewCardModal(true);

  const {
    data: { cardData = [], defaultCardId } = {},
    isLoading,
    isSuccess,
  } = useGetCards();

  const [filteredCards, setFilteredCards] = useState([]);
  const [defaultCard, setDefaultCard] = useState(null);

  useEffect(() => {
    if (isSuccess && cardData.length) {
      setFilteredCards(cardData);
    }
  }, [cardData, isSuccess]);

  useEffect(() => {
    if (defaultCardId) {
      setDefaultCard(defaultCardId);
    }
  }, [defaultCardId]);

  return (
    <>
      <main className="profile-section d-flex bg-white flex-column align-items-stretch w-100">
        <BackBreadcrumb />

        <div className="payment-listing-container h-100 p-0 m-0 d-flex flex-column justify-content-start align-items-stretch overflow-hidden">
          <div
            className="payment-listing-container-left p-0 m-0 d-flex flex-column h-100"
            style={{ gap: "30px" }}
          >
            <PaymentToolbar onAdButtonClick={handleAddNewCardShow} />

            <ul className="linked-card-list list-unstyled mb-0 d-flex flex-column h-100">
              {isLoading && (
                <>
                  {Array.from({ length: 3 }).map((_, idx) => (
                    <CardTileSkeleton key={idx} />
                  ))}
                </>
              )}

              {!isLoading && isSuccess && !filteredCards.length && (
                <p className="d-flex justify-content-center align-items-center text-center fw-bold text-danger h-100">
                  No Cards Found
                </p>
              )}

              {filteredCards.length > 0 &&
                filteredCards.map((card: any) => (
                  <CardTile
                    key={card.id}
                    cardInfo={card}
                    setFilteredCards={setFilteredCards}
                    defaultCard={defaultCard}
                    setDefaultCard={setDefaultCard}
                    isMultipleCards={filteredCards.length > 1}
                  />
                ))}
            </ul>
          </div>
        </div>
      </main>

      <AddNewCardModal
        show={showAddNewCardModal}
        onClose={handleAddNewCardClose}
      />
    </>
  );
};

export default PaymentListing;
