import { useRef, useState } from "react";
import { Crop, PixelCrop } from "react-image-crop";

export const useImageCropper = () => {
  const [showCropModal, setShowCropModal] = useState(false);
  const [imgSrc, setImgSrc] = useState("");
  const [crop, setCrop] = useState<Crop | undefined>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop | undefined>();
  const [fieldKey, setFieldKey] = useState<string | null>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const [scale, setScale] = useState(1);
  const rotate = 0;
  const aspect = 1;

  const openCropper = (fieldName: string, file: File) => {
    setCrop(undefined);
    setScale(1);
    setFieldKey(fieldName);
    const reader = new FileReader();
    reader.addEventListener("load", () =>
      setImgSrc(reader.result?.toString() || ""),
    );
    reader.readAsDataURL(file);
    setShowCropModal(true);
  };

  const saveCroppedImage = (
    onSave: (fieldName: string, croppedFile: File) => void,
    isCircularCrop: boolean,
  ) => {
    if (!fieldKey || !imgRef.current || !completedCrop) {
      console.log("Missing fieldKey, image, or crop data");
      return;
    }

    const image = imgRef.current;

    const style = window.getComputedStyle(image);
    let offscreen = new OffscreenCanvas(image.width, image.height);

    if (isCircularCrop) {
      const paddingLeft = parseFloat(style.paddingLeft) || 0;
      const paddingTop = parseFloat(style.paddingTop) || 0;
      const borderLeft = parseFloat(style.borderLeftWidth) || 0;
      const borderTop = parseFloat(style.borderTopWidth) || 0;

      const contentWidth = image.clientWidth - paddingLeft - borderLeft;
      const contentHeight = image.clientHeight - paddingTop - borderTop;

      const scaleX = image.naturalWidth / contentWidth;
      const scaleY = image.naturalHeight / contentHeight;

      const cropCenterX = completedCrop.x + completedCrop.width / 2;
      const cropCenterY = completedCrop.y + completedCrop.height / 2;

      const sourceX = (cropCenterX - completedCrop.width / 2) * scaleX;
      const sourceY = (cropCenterY - completedCrop.height / 2) * scaleY;
      const sourceWidth = completedCrop.width * scaleX;
      const sourceHeight = completedCrop.height * scaleY;

      const minDimension = Math.min(sourceWidth, sourceHeight);
      const adjustedSourceX = sourceX + (sourceWidth - minDimension) / 2;
      const adjustedSourceY = sourceY + (sourceHeight - minDimension) / 2;
      const adjustedSourceWidth = minDimension;
      const adjustedSourceHeight = minDimension;

      const canvasWidth = Math.ceil(adjustedSourceWidth);
      const canvasHeight = Math.ceil(adjustedSourceHeight);
      offscreen = new OffscreenCanvas(canvasWidth, canvasHeight);
      const ctx = offscreen.getContext("2d");
      if (!ctx) {
        console.log("Failed to get 2D context");
        return;
      }

      const radius = Math.min(canvasWidth, canvasHeight) / 2;
      ctx.beginPath();
      ctx.arc(canvasWidth / 2, canvasHeight / 2, radius, 0, Math.PI * 2, false);
      ctx.closePath();
      ctx.clip();

      ctx.drawImage(
        image,
        adjustedSourceX,
        adjustedSourceY,
        adjustedSourceWidth,
        adjustedSourceHeight,
        0,
        0,
        canvasWidth,
        canvasHeight,
      );
    } else {
      const cropScale = scale || 1;

      const naturalWidth = image.naturalWidth;
      const naturalHeight = image.naturalHeight;
      const renderedWidth = image.width;
      const renderedHeight = image.height;

      const scaledWidth = renderedWidth * cropScale;
      const scaledHeight = renderedHeight * cropScale;

      const offsetX = (scaledWidth - renderedWidth) / 2;
      const offsetY = (scaledHeight - renderedHeight) / 2;

      const scaleX = naturalWidth / scaledWidth;
      const scaleY = naturalHeight / scaledHeight;

      const sourceX = (completedCrop.x + offsetX) * scaleX;
      const sourceY = (completedCrop.y + offsetY) * scaleY;
      const sourceWidth = completedCrop.width * scaleX;
      const sourceHeight = completedCrop.height * scaleY;

      let targetWidth = sourceWidth;
      let targetHeight = sourceHeight;

      if (targetHeight > 200) {
        const scaleFactor = 200 / targetHeight;
        targetWidth *= scaleFactor;
        targetHeight = 200;
      }

      offscreen = new OffscreenCanvas(targetWidth, targetHeight);
      const ctx = offscreen.getContext("2d");

      if (!ctx) {
        console.log("No 2D context");
        return;
      }

      ctx.fillStyle = "white";
      ctx.fillRect(0, 0, targetWidth, targetHeight);

      ctx.drawImage(
        image,
        sourceX,
        sourceY,
        sourceWidth,
        sourceHeight,
        0,
        0,
        targetWidth,
        targetHeight,
      );
    }

    offscreen
      .convertToBlob({ type: "image/png" })
      .then((blob) => {
        if (blob) {
          const croppedFile = new File([blob], "cropped_image.png", {
            type: "image/png",
          });
          setShowCropModal(false);
          setImgSrc("");
          setCompletedCrop(undefined);
          onSave(fieldKey, croppedFile);
        } else {
          console.log("No blob created");
        }
      })
      .catch((error) => {
        console.error("Error converting to blob:", error);
      });
  };

  return {
    showCropModal,
    setShowCropModal,
    setCrop,
    setImgSrc,
    config: {
      crop,
      scale,
      rotate,
      aspect,
      setScale,
    },
    completedCrop,
    imgRef,
    setCompletedCrop,
    imgSrc,
    openCropper,
    saveCroppedImage,
    fieldKey,
  };
};
