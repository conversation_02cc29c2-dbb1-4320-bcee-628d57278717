import { Ri<PERSON>heckL<PERSON>, Ri<PERSON>loseLine, RiDeleteBin7Line } from "@remixicon/react";
import { CustomDropdown } from "components";
import {
  useDeleteSavedSection,
  useGetSavedSections,
} from "features/Reports/api";
import { addReportBlockAtIndex } from "features/Reports/store";
import React, { useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON>, <PERSON>dal, Spinner } from "react-bootstrap";
import "./styles.scss";

interface InsertSectionModalProps {
  show: boolean;
  onClose: () => void;
  index: number;
}

const InsertSectionModal: React.FC<InsertSectionModalProps> = ({
  show,
  onClose,
  index,
}) => {
  const { data: { data: reportBlocks = [] } = {}, isLoading }: any =
    useGetSavedSections({
      enabled: show,
    });
  const { mutateAsync: deleteSavedSection } = useDeleteSavedSection();

  const [pendingDelete, setPendingDelete] = useState<string | null>(null);
  const [localBlocks, setLocalBlocks] = useState<any[]>([]);

  useEffect(() => {
    setLocalBlocks(reportBlocks);
  }, [reportBlocks]);

  const onDeleteSection = async (e: any, id: string) => {
    e.stopPropagation();
    const result: any = await deleteSavedSection({ id });
    if (result?.success) {
      setLocalBlocks((prev: any) => prev.filter((item: any) => item.id !== id));
      setPendingDelete(null);
    }
  };

  const savedBlocks = useMemo(() => {
    return localBlocks.map((block: any) => {
      const blockValue = JSON.stringify(block);
      const isPending = pendingDelete === blockValue;

      return {
        value: blockValue,
        label: (
          <div className="d-flex justify-content-between align-items-center w-100">
            <span className="text-truncate me-2">{`${block.title} - ${block.report_title}`}</span>
            {isPending ? (
              <div className="d-flex gap-2">
                <button
                  className="btn btn-sm p-0 border-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    setPendingDelete(null);
                  }}
                  title="Cancel"
                >
                  <RiCloseLine />
                </button>
                <button
                  className="btn btn-sm p-0 border-0"
                  onClick={(e) => onDeleteSection(e, block.id)}
                  title="Confirm Delete"
                >
                  <RiCheckLine />
                </button>
              </div>
            ) : (
              <button
                className="btn btn-sm p-0 border-0"
                onClick={(e) => {
                  e.stopPropagation();
                  setPendingDelete(blockValue);
                }}
                title="Delete"
              >
                <RiDeleteBin7Line color="red" />
              </button>
            )}
          </div>
        ),
      };
    });
  }, [localBlocks, pendingDelete]);

  const handleClose = () => {
    onClose();
  };

  const handleSelectItem = (value: string) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { id, ...parsedValue } = JSON.parse(value);
    addReportBlockAtIndex(parsedValue, index);
    handleClose();
  };

  const handleAddSection = () => {
    const newSection = {
      title: "New Section",
      content: {
        type: "html",
        content: "",
      },
      position: 0,
      is_locked: false,
      show_section_title: true,
    };
    addReportBlockAtIndex(newSection, index);
    handleClose();
  };

  return (
    <Modal
      show={show}
      onHide={handleClose}
      keyboard={false}
      centered
      className="insert-section-modal"
    >
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={handleClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>

        <div
          className="auth-form d-flex justify-content-center align-items-stretch flex-column"
          style={{ gap: "20px" }}
        >
          <div className="d-flex flex-column" style={{ gap: "15px" }}>
            <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
              Insert Section
            </h1>
          </div>

          {isLoading && (
            <div className="d-flex justify-content-center align-items-center">
              <Spinner />
            </div>
          )}

          {!isLoading && (
            <div className="d-flex flex-column gap-3">
              <Button
                variant=""
                className="w-100 border-blue fw-bold insert-btn"
                onClick={handleAddSection}
              >
                Insert Blank Section
              </Button>
              {savedBlocks?.length > 0 && (
                <CustomDropdown
                  title="Insert Saved Section"
                  items={savedBlocks}
                  className="insert-saved-section"
                  onSelect={handleSelectItem}
                  preserveTitle
                />
              )}
            </div>
          )}
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default InsertSectionModal;
