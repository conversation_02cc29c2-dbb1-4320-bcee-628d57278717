.wave-icon {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 70px;

  span {
    display: block;
    width: 5px;
    background: #60a799;
    animation: waveMiddle ease-in-out infinite;
    transform-origin: center;
    animation-play-state: paused;
  }

  span:nth-child(1) {
    height: 22px;
    animation-duration: 1.1s;
    animation-delay: 0.2s;
  }
  span:nth-child(2) {
    height: 32px;
    animation-duration: 0.9s;
    animation-delay: 0.5s;
  }
  span:nth-child(3) {
    height: 44px;
    animation-duration: 1.3s;
    animation-delay: 0.1s;
  }
  span:nth-child(4) {
    height: 56px;
    animation-duration: 1s;
    animation-delay: 0.4s;
  }
  span:nth-child(5) {
    height: 64px;
    animation-duration: 1.2s;
    animation-delay: 0.3s;
  }
  span:nth-child(6) {
    height: 56px;
    animation-duration: 1.05s;
    animation-delay: 0.15s;
  }
  span:nth-child(7) {
    height: 44px;
    animation-duration: 0.95s;
    animation-delay: 0.45s;
  }
  span:nth-child(8) {
    height: 32px;
    animation-duration: 1.25s;
    animation-delay: 0.25s;
  }
  span:nth-child(9) {
    height: 22px;
    animation-duration: 1.15s;
    animation-delay: 0.35s;
  }

  &.active span {
    animation-play-state: running;
  }
}

@keyframes waveMiddle {
  0%,
  100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(1.3);
  }
}
