import {
  DocSettings,
  InviteNew,
  Invoices,
  MySubscriptions,
  OrganisationalProfile,
  OrganisationManager,
  Payment,
  PaymentListing,
  PaymentStatus,
  Subscriptions,
} from "pages";
import { Route } from "react-router-dom";
import { ROUTE_PATH } from "./routePath";

export function AdminRoutes({
  isOrganisationEnabled,
  IsOfflineAccount,
}: {
  isOrganisationEnabled: boolean;
  IsOfflineAccount: boolean;
}) {
  return (
    <>
      {!IsOfflineAccount && (
        <>
          <Route path={ROUTE_PATH.SUBSCRIPTIONS} element={<Subscriptions />} />
          <Route path={ROUTE_PATH.PAYMENT_CARDS} element={<PaymentListing />} />
          <Route
            path={ROUTE_PATH.MY_SUBSCRIPTIONS}
            element={<MySubscriptions />}
          />
        </>
      )}
      <Route path={ROUTE_PATH.INVOICES} element={<Invoices />} />
      <Route path={ROUTE_PATH.PAYMENT} element={<Payment />} />
      <Route path={ROUTE_PATH.PAYMENT_STATUS} element={<PaymentStatus />} />
      <Route path={ROUTE_PATH.DOC_SETTINGS} element={<DocSettings />} />
      {isOrganisationEnabled && (
        <>
          <Route
            path={ROUTE_PATH.ORGANISATION_MANAGER}
            element={<OrganisationManager />}
          />
          <Route path={ROUTE_PATH.INVITE_NEW} element={<InviteNew />} />
          <Route
            path={ROUTE_PATH.ORGANISATIONAL_PROFILE}
            element={<OrganisationalProfile />}
          />
        </>
      )}
    </>
  );
}
