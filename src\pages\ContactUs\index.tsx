import { useContactUsMutation } from "api";
import { BackBreadcrumb } from "components";
import { ErrorMessage, Field, Form, Formik } from "formik";
import { ContactUsInitialValues } from "formSchema/initialValues";
import { ContactUsValidations } from "formSchema/schemaValidations";
import { <PERSON><PERSON>, Card, Col, Container, Row, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";
import useUserStore from "stores/user";

const ContactUs = () => {
  const { mutateAsync: contactUs } = useContactUsMutation();
  const user = useUserStore((state) => state.userInfo.user);

  const handleSubmit = async (
    values: any,
    { setSubmitting, resetForm }: any,
  ) => {
    try {
      resetForm();
      const response: any = await contactUs(values);
      if (response?.success) {
        toast.success(response?.message || "Inquiry submitted successfully");
        resetForm();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <main className="profile-section d-flex bg-white flex-column align-items-stretch w-100">
      <BackBreadcrumb />
      <div className="profile-section-form-container">
        <Container fluid>
          <Row className="justify-content-center align-items-center">
            <Col lg="9" xxl="7">
              <div className="auth-form w-100 m-0">
                <Card className="auth-form-card justify-content-center aligh-items-center">
                  <Card.Body className="d-flex gap-4 flex-column align-items-center justify-content-center">
                    <div className="d-flex flex-column" style={{ gap: "10px" }}>
                      <h1 className="mb-0 text-center auth-form-heading text-uppercase fw-bold">
                        contact us
                      </h1>
                    </div>

                    <div className="website-form w-100">
                      <Formik
                        initialValues={{
                          ...ContactUsInitialValues,
                          full_name: user?.full_name || "",
                          email: user?.email || "",
                        }}
                        enableReinitialize
                        validationSchema={ContactUsValidations}
                        onSubmit={handleSubmit}
                      >
                        {({ isSubmitting }: any) => (
                          <Form className="d-flex flex-column gap-4">
                            <div className="form-group position-relative">
                              <label className="form-label">Full Name</label>

                              <Field
                                name="full_name"
                                className="form-control"
                                placeholder="Enter full name"
                                type="text"
                              />
                              <ErrorMessage component="span" name="full_name" />
                            </div>

                            <div className="form-group position-relative">
                              <div className="d-flex justify-content-between align-items-center">
                                <label className="form-label">Email</label>
                              </div>

                              <Field
                                name="email"
                                className="form-control"
                                placeholder="Enter email"
                                type="email"
                              />
                              <ErrorMessage component="span" name="email" />
                            </div>

                            <div className="form-group position-relative">
                              <div className="d-flex justify-content-between align-items-center">
                                <label className="form-label">
                                  Phone Number
                                </label>
                              </div>

                              <Field
                                name="phone_number"
                                className="form-control"
                                placeholder="Enter phone number"
                                type="text"
                              />
                              <ErrorMessage
                                component="span"
                                name="phone_number"
                              />
                            </div>

                            <div className="form-group position-relative">
                              <label className="form-label">Company Name</label>

                              <Field
                                name="company_name"
                                className="form-control"
                                placeholder="Enter company name"
                                type="text"
                              />
                            </div>

                            <div className="form-group position-relative">
                              <label className="form-label">Description</label>

                              <Field
                                name="description"
                                className="form-control pt-1"
                                placeholder="Enter description"
                                type="text"
                                as="textarea"
                              />
                              <ErrorMessage
                                component="span"
                                name="description"
                              />
                            </div>

                            <div className="action-btns mt-3">
                              <Button
                                type="submit"
                                className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                                disabled={isSubmitting}
                              >
                                {isSubmitting ? <Spinner /> : "Submit"}
                              </Button>
                            </div>
                          </Form>
                        )}
                      </Formik>
                    </div>
                  </Card.Body>
                </Card>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </main>
  );
};

export default ContactUs;
