import { CustomDropdown } from "components";
import { Col, Row } from "react-bootstrap";

const FONTS_CONFIG = [
  { label: "Times New Roman", value: "Times New Roman" },
  { label: "Urbanist", value: "Urbanist" },
  { label: "Aptos", value: "Aptos" },
  { label: "<PERSON><PERSON><PERSON>", value: "<PERSON><PERSON><PERSON>" },
];

export default function FontSelection({
  values,
  setFieldValue,
}: {
  values: any;
  setFieldValue: any;
}) {
  return (
    <div className="font-selection">
      <Row>
        <Col xs={12} className="p-0">
          <CustomDropdown
            title="Select Font"
            items={FONTS_CONFIG}
            onSelect={(value) => setFieldValue("font", value)}
            value={values.font}
          />
        </Col>
      </Row>
    </div>
  );
}
