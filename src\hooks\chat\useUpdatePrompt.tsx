import { useDeletePromptMutation, useUpdatePromptMutation } from "api";
import useInvalidateQuery from "hooks/useInvalidateQuery";
import toast from "react-hot-toast";
import { setConfirmModalConfig } from "stores";
import { convertObjToFormData } from "utils";

const useUpdatePrompt = ({ onCloseModal, onOpenModal }: any) => {
  const { mutateAsync: deletePrompt } = useDeletePromptMutation();
  const { mutateAsync: updatePrompt } = useUpdatePromptMutation();

  const [invalidateQueries] = useInvalidateQuery();

  const handleReset = async (prompt_id: number) => {
    try {
      const response: any = await deletePrompt(prompt_id);
      if (response?.success) {
        toast.success(response?.message);
        invalidateQueries(["custom-prompts"]);
        onCloseModal();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onClickSubmit = async (values: any) => {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { metadata, ...restValues } = values || {};
      const payload = convertObjToFormData(restValues);
      const response: any = await updatePrompt(payload);
      if (response?.success) {
        toast.success(response?.message);
        invalidateQueries(["custom-prompts"]);
        onCloseModal();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onClickReset = ({ evt, prompt_id }: any) => {
    evt.stopPropagation();
    onCloseModal();
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleReset(prompt_id),
        onClose: () => onOpenModal(),
        content: {
          heading: "Reset Prompt?",
          description: "Are you sure you want to reset this prompt?",
        },
      },
    });
  };

  return { onClickReset, onClickSubmit };
};

export default useUpdatePrompt;
