import "./styles.scss";

interface WarningBarProps {
  message: string;
  additionalClasses?: string;
}

export default function WarningBar({
  message,
  additionalClasses,
}: WarningBarProps) {
  return (
    <div
      className={`warning-bar d-flex align-items-center ${additionalClasses}`}
    >
      <p className="warning-bar-message">
        <strong>Warning:</strong> {message}
      </p>
    </div>
  );
}
