@use "/src/styles/mixins/mixins.scss";

.notification-wrapper {
  padding: 30px;
  overflow: hidden;
  position: relative;
  height: calc(100vh - 195px);
  border-radius: 12px;
  box-shadow:
    0px 65px 47px 0px rgba(26, 26, 26, 0.04),
    0px 100px 80px 0px rgba(26, 26, 26, 0.05);

  @media only screen and (max-width: 991px) {
    height: auto;
  }

  @include mixins.slim-scrollbar;

  .custom-table {
    thead {
      th {
        min-width: 250px;

        @media only screen and (max-width: 991px) {
          min-width: 200px;

          &:nth-child(2) {
            min-width: 400px;
          }
        }

        &:last-child {
          text-align: center;
        }
      }
    }
  }
}
