import { OrgUserRole } from "globals";
import { useMemo } from "react";
import useUserStore from "stores/user";

export default function useUserRoles(): OrgUserRole {
  const user = useUserStore((state) => state.userInfo.user);

  return useMemo<OrgUserRole>(() => {
    if (
      !user ||
      user.ownedOrganization ||
      user.organizationMember?.role === OrgUserRole.ADMIN
    ) {
      return OrgUserRole.ADMIN;
    }

    return (user.organizationMember?.role as OrgUserRole) || OrgUserRole.ADMIN;
  }, [user]);
}
