import { Ri<PERSON><PERSON>utBoxRLine } from "@remixicon/react";
import axios, { AxiosResponse, InternalAxiosRequestConfig, AxiosError } from "axios";
import toast from "react-hot-toast";
import { ROUTE_PATH } from "routes";
import {
  resetEntityConfiguration,
  resetPlanState,
  setConfirmModalConfig,
} from "stores";
import { getAuthToken, resetUserState } from "stores/user";
import { getAppOriginURL } from "utils";
import { tokenRefreshService } from "services/tokenRefreshService";

export const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
});

const authRequestInterceptor = (config: InternalAxiosRequestConfig) => {
  const token = getAuthToken();
  if (config.headers) {
    config.headers.Accept = "application/json";
  }
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
};

apiClient.interceptors.request.use(authRequestInterceptor);

// Track failed requests for retry
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (error?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    if (!response?.data?.success) {
      response?.data?.message
        ? toast.error(response?.data?.message, {
            id: "generic-error",
          })
        : null;
    }
    return response.data;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

    if (error.response && error.response.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // If we're already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then(token => {
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${token}`;
          }
          return apiClient(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        // Get current user info from store
        const userStore = JSON.parse(localStorage.getItem('user') || '{}');
        const userInfo = userStore.state?.userInfo;

        if (userInfo?.loginType && userInfo?.token) {
          const refreshResult = await tokenRefreshService.refreshToken(
            userInfo.loginType,
            userInfo.refreshToken
          );

          if (refreshResult.success && refreshResult.token) {
            // Update the authorization header for the original request
            if (originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${refreshResult.token}`;
            }

            processQueue(null, refreshResult.token);

            // Retry the original request
            return apiClient(originalRequest);
          } else {
            // Refresh failed, handle as 401
            processQueue(error, null);
            handle401(error);
            return Promise.reject(error);
          }
        } else {
          // No login info available, handle as 401
          processQueue(error, null);
          handle401(error);
          return Promise.reject(error);
        }
      } catch (refreshError) {
        processQueue(refreshError, null);
        handle401(error);
        return Promise.reject(error);
      } finally {
        isRefreshing = false;
      }
    }

    // Handle other error cases
    if (typeof error.response === "undefined") {
      toast.error(
        "A network error occurred. Please try again after some time.",
        {
          id: "network-error",
        }
      );
      return Promise.reject(error);
    }

    if ((error?.response?.data as any)?.message === "USAGE_LIMIT_REACHED") {
      toast.error(
        <div>
          This submission will exceed your monthly allowances. Please review
          your remaining allowances{" "}
          <a href={`${getAppOriginURL()}${ROUTE_PATH.SETTINGS}`}>here</a>.
          Alternatively, you can upgrade your allowances{" "}
          <a href={`${getAppOriginURL()}${ROUTE_PATH.SUBSCRIPTIONS}`}>here</a>.
        </div>,
        {
          id: "generic-error",
          duration: 15000,
        }
      );
      return Promise.reject(error);
    }

    toast.error(
      (error?.response?.data as any)?.message || "An error occurred, please try again.",
      {
        id: "generic-error",
        duration: 10000,
      }
    );
    return Promise.reject(error);
  }
);

const handle401 = (error: any) => {
  const onSubmit = () => {
    resetUserState();
    resetPlanState();
    resetEntityConfiguration();
    window.location.href = getAppOriginURL();
  };
  setConfirmModalConfig({
    visible: true,
    data: {
      onSubmit: () => onSubmit(),
      icon: RiLogoutBoxRLine,
      iconColor: "#ad986f",
      content: {
        heading: "Session Expired",
        description:
          error?.response?.data?.message ||
          "Your session has timed out. Please log in again to continue.",
      },
      showCloseIcon: false,
      buttonText: "Log in again",
    },
  });
};
