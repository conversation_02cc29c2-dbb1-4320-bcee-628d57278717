import { EntityConfigurationInterface } from "./chat";

export interface UserInterface {
  id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string;
  email_verified: boolean;
  status: string;
  profile_photo: string;
  redact_setting: EntityConfigurationInterface;
  email_verified_at: string;
  has_unread_notification: boolean;
  otp_data?: Record<string, any>;
  stripe_customer_id?: string;
  is_subscription?: boolean;
  ownedOrganization?: OrganizationInterface;
  organizationMember?: OrganizationMemberInterface;
  is_offline_account?: boolean;
  terms_sheet_url?: string;
  superadmin_offline_access?: boolean;
}

export interface OrganizationInterface {
  id: number;
  user_id: number;
  title: string;
  logo: string;
  settings: string;
  created_at: string;
  updated_at: string;
}

export interface OrganizationMemberInterface {
  id: number;
  organization_id: number;
  member_id: number;
  role: string;
  status: string;
  created_at: string;
  updated_at: string;
  activation_token: string;
  organization: OrganizationInterface;
}
