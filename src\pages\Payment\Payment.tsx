import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import {
  useConfigQuery,
  useCreatePaymentIntentQuery,
  useGetPaymentDetails,
} from "api";
import { BackBreadcrumb } from "components";
import { HomeSuccess } from "pages/Home";
import { useEffect, useState } from "react";
import { Card, Col, Container, Row } from "react-bootstrap";
import { useNavigate, useSearchParams } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { setUserInfo } from "stores";
import usePlanStore from "stores/plan";
import useUserStore from "stores/user";
import PaymentForm from "./PaymentForm";
import "./styles.scss";
import UpdateSubscription from "./UpdateSubscription";

const Payment = () => {
  const [stripePromise, setStripePromise] = useState<any>(null);
  const [isPaymentLoading, setIsPaymentLoading] = useState(false);

  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const couponId = searchParams.get("couponId");
  const isSubscriptionPaid = searchParams.get("is_subscription_paid");
  const team_size_raw = searchParams.get("team_size");
  const team_size =
    team_size_raw && !isNaN(Number(team_size_raw)) && Number(team_size_raw) > 0
      ? Number(team_size_raw)
      : undefined;

  const { planInfo, myPlan } = usePlanStore((state: any) => state);
  const userInfo = useUserStore((state: any) => state.userInfo);

  const {
    data: intentData = {},
    isLoading,
    isSuccess,
  } = useCreatePaymentIntentQuery({
    subscription_id: myPlan?.currentPlan?.subscription_id
      ? undefined
      : planInfo?.id,
    stripe_coupon_id: couponId ?? undefined,
    isSubscriptionPaid: team_size ? false : isSubscriptionPaid,
  });

  const [intentInfo, setIntentInfo] = useState<any>(null);
  const [paymentInfo, setPaymentInfo] = useState<any>(null);

  const { data: { publishableKey = null } = {} }: any = useConfigQuery();

  const {
    data: paymentData = {},
    isLoading: isPaymentInfoLoading,
    isSuccess: isPaymentInfoSuccess,
  } = useGetPaymentDetails({
    subscription_id: team_size
      ? myPlan?.currentPlan?.subscription_id
      : myPlan?.currentPlan?.subscription_id
        ? planInfo?.id
        : undefined,
    team_size,
  });

  const { client_secret } = intentInfo ?? {};

  useEffect(() => {
    if (isSuccess && intentData) {
      setIntentInfo(intentData);
    }
    setIsPaymentLoading(isLoading);
  }, [isSuccess, intentData, isLoading]);

  useEffect(() => {
    if (isPaymentInfoSuccess && paymentData) {
      setPaymentInfo(paymentData);
    }
    setIsPaymentLoading(isPaymentInfoLoading);
  }, [isPaymentInfoSuccess, paymentData, isPaymentInfoLoading]);

  useEffect(() => {
    if (planInfo?.id && !isSubscriptionPaid) {
      setIntentInfo(null);
      setPaymentInfo(null);
      setIsPaymentLoading(true);
    }
  }, [planInfo?.id]);

  useEffect(() => {
    if (publishableKey) {
      setStripePromise(loadStripe(publishableKey));
    }
  }, [publishableKey]);

  useEffect(() => {
    if ("navigation" in window) {
      const currentNavEntries: any = (window as any).navigation?.entries();
      if (
        currentNavEntries.length > 0 &&
        currentNavEntries.at(-1).url?.includes(ROUTE_PATH.PAYMENT_STATUS)
      ) {
        navigate(ROUTE_PATH.SUBSCRIPTIONS);
      }
    }
  }, []);

  useEffect(() => {
    if (intentInfo?.is_paid && couponId) {
      setSearchParams({
        is_subscription_paid: "true",
      });
    }
  }, [intentInfo?.is_paid, couponId]);

  useEffect(() => {
    if (isSubscriptionPaid) {
      setUserInfo({
        ...userInfo,
        user: {
          ...userInfo.user,
          is_subscription: true,
        },
      });
    }
  }, [isSubscriptionPaid]);

  return (
    <>
      {isPaymentLoading ? (
        <HomeSuccess type="PLEASE_WAIT" />
      ) : (
        <>
          {(intentInfo?.is_paid && couponId) || isSubscriptionPaid ? (
            <HomeSuccess type="FULL_DISCOUNT" />
          ) : (
            <main className="payment-section d-flex bg-white flex-column align-items-stretch w-100 h-100">
              <BackBreadcrumb />
              <div className="payment-section-form-container">
                <Container fluid>
                  <Row className="justify-content-center align-items-center">
                    <Col lg="9" xxl="7">
                      <div className="auth-form w-100 m-0">
                        <Card className="auth-form-card justify-content-center align-items-center">
                          <Card.Body className="d-flex gap-4 flex-column justify-content-center">
                            {paymentInfo?.subscription_id ? (
                              <Elements stripe={stripePromise}>
                                <UpdateSubscription
                                  planInfo={paymentInfo}
                                  team_size={team_size}
                                />
                              </Elements>
                            ) : (
                              <PaymentForm
                                stripePromise={stripePromise}
                                clientSecret={client_secret}
                                intentInfo={intentInfo}
                              />
                            )}
                          </Card.Body>
                        </Card>
                      </div>
                    </Col>
                  </Row>
                </Container>
              </div>
            </main>
          )}
        </>
      )}
    </>
  );
};

export default Payment;
