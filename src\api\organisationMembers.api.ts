import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals";
import { apiClient } from "./apiClient";
import { VerifyAccountTypeEnum } from "../types";

export const useOfflineAccountTerms = ({
  token,
}: Record<string, string | null>) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        API_ENDPOINTS.OFFLINE_ACCOUNT_TERMS(token),
      );
      return response?.data;
    },
    queryKey: ["offline-account-terms", token],
    enabled: !!token,
  });

export const useAcceptOfflineAccountTerms = () =>
  useMutation({
    mutationFn: async ({ payload }: any) => {
      const response = await apiClient.post(
        API_ENDPOINTS.ACCEPT_OFFLINE_ACCOUNT_TERMS,
        payload,
      );
      return response;
    },
  });

export const useVerifyOfflineAccount = () =>
  useMutation({
    mutationFn: (payload: any) => {
      return apiClient.post(API_ENDPOINTS.VERIFY_OFFLINE_ACCOUNT, payload);
    },
  });

export const useVerifyAccountToken = ({ token, type }: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        type === VerifyAccountTypeEnum.MEMBER
          ? API_ENDPOINTS.VERIFY_MEMBER_TOKEN(token)
          : API_ENDPOINTS.VERIFY_OFFLINE_ACCOUNT_TOKEN(token),
      );
      return response?.data;
    },
    queryKey: ["verify-account-token", token],
    enabled: !!token,
  });

export const useVerifyMemberAccount = () =>
  useMutation({
    mutationFn: (payload: any) => {
      return apiClient.post(API_ENDPOINTS.VERIFY_MEMBER_ACCOUNT, payload);
    },
  });

export const useUpdateMemberRole = () => {
  return useMutation({
    mutationFn: ({ org_id, member_id, payload }: any) => {
      return apiClient.put(
        API_ENDPOINTS.UPDATE_MEMBER_ROLE(org_id, member_id),
        payload,
      );
    },
  });
};

export const useUpdateMemberStatus = () => {
  return useMutation({
    mutationFn: ({ org_id, member_id, payload }: any) => {
      return apiClient.put(
        API_ENDPOINTS.UPDATE_MEMBER_STATUS(org_id, member_id),
        payload,
      );
    },
  });
};

export const useReSendInvite = () =>
  useMutation({
    mutationFn: async ({ member_id, org_id }: any) => {
      const response = await apiClient.get(
        API_ENDPOINTS.RESEND_INVITE({ member_id, org_id }),
      );
      return response;
    },
  });
