import { Form } from "react-bootstrap";
import { Link } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import useUserStore from "stores/user";
import "./styles.scss";

const NotFound = () => {
  const token = useUserStore((state) => state.userInfo.token);
  return (
    <div
      className="auth-form d-flex justify-content-center align-items-center flex-column login-form̥"
      style={{ gap: "30px" }}
    >
      <div className="d-flex flex-column" style={{ gap: "23px" }}>
        <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
          <span className="font-brown">Oops!</span> Page Not Found
        </h1>

        <p className="mb-0 auth-form-description font-gray text-center">
          We're sorry, but the page you're looking for doesn't exist.
          <br className="d-lg-block d-none" />
          It might have been removed, had its name changed, or is temporarily
          unavailable.
        </p>
      </div>

      <div className="website-form">
        <Form className="d-flex flex-column" style={{ gap: "30px" }}>
          <div
            className="action-btns d-flex flex-column"
            style={{ gap: "30px" }}
          >
            <Link
              to={ROUTE_PATH.HOME}
              className="d-flex justify-content-center align-items-center text-decoration-none submit-btn position-relative w-100 border-brown bg-transparent text-uppercase font-primary"
            >
              Go Back to {token ? "Home" : "Login"}
            </Link>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default NotFound;
