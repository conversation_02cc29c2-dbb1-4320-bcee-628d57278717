const TogglePrivateAI = ({
  setLocalConfiguration,
  localConfiguration,
}: any) => {
  const handleTogglePrivacy = (value: boolean) => {
    setLocalConfiguration({
      ...localConfiguration,
      enable_privacy: value,
    });
  };

  return (
    <div className="ai-toggler d-flex justify-content-between align-items-center">
      <span
        className={`ai-toggler-button d-block text-center text-capitalize ${localConfiguration?.enable_privacy ? "active" : ""}`}
        onClick={() => handleTogglePrivacy(true)}
      >
        On
      </span>
      <span
        className={`ai-toggler-button d-block text-center text-capitalize ${localConfiguration?.enable_privacy ? "" : "active"}`}
        onClick={() => handleTogglePrivacy(false)}
      >
        Off
      </span>
    </div>
  );
};

export default TogglePrivateAI;
