import { ROUTE_PATH } from "routes";
import {
  getEntityConfiguration,
  getMessageHistory,
  setIsDocDeleted,
  setMessageHistory,
  setTempNewChat,
} from "stores";
import { v4 as uuidv4 } from "uuid";

export const MAX_RECENT_HISTORY_COUNT = -20;

export const groupDataByDate = (data: any) => {
  if (!data?.length) return {};
  const now = new Date();
  const startOfToday = new Date(now.setHours(0, 0, 0, 0));
  const startOfYesterday = new Date(
    startOfToday.getTime() - 24 * 60 * 60 * 1000,
  );
  const startOf7DaysAgo = new Date(
    startOfToday.getTime() - 8 * 24 * 60 * 60 * 1000,
  );
  const startOf30DaysAgo = new Date(
    startOf7DaysAgo.getTime() - 31 * 24 * 60 * 60 * 1000,
  );

  const groups: any = {
    Today: [],
    Yesterday: [],
    "Previous 7 days": [],
    "Previous 30 days": [],
  };

  const addMonthGroup = (date: any) => {
    const month = date.toLocaleString("default", { month: "long" });
    const year = date.getFullYear();
    const label = `${month} ${year}`;
    if (!groups[label]) {
      groups[label] = [];
    }
    return label;
  };

  data.forEach((item: any) => {
    const createdAt = new Date(item.created_at);

    if (createdAt >= startOfToday) {
      groups["Today"].push(item);
      return;
    } else if (createdAt >= startOfYesterday) {
      groups["Yesterday"].push(item);
      return;
    } else if (createdAt >= startOf7DaysAgo) {
      groups["Previous 7 days"].push(item);
      return;
    } else if (createdAt >= startOf30DaysAgo) {
      groups["Previous 30 days"].push(item);
      return;
    }

    const monthGroupLabel = addMonthGroup(createdAt);
    groups[monthGroupLabel].push(item);
  });

  return groups;
};

export const createTempChat = ({ inputValue, newChatId, navigate }: any) => {
  setTempNewChat({
    chat_id: newChatId,
    created_at: new Date().toISOString(),
    text: inputValue,
  });
  navigate(`${ROUTE_PATH.HOME}${newChatId}`);
};

export const getRecentChatHistory = (messageHistory: any) => {
  const recentHistory = messageHistory?.slice(MAX_RECENT_HISTORY_COUNT);
  return recentHistory ?? [];
};

export const formatEntityConfiguration = (entityConfiguration: any) => {
  const isCustomAvailable = entityConfiguration?.preset?.some((item: any) => {
    return item?.type?.includes("custom");
  });

  if (isCustomAvailable) {
    const customEntities = entityConfiguration?.preset
      .map((item: any) => item.entity_values)
      .flat();
    return {
      enable_privacy: entityConfiguration?.enable_privacy,
      preset_type: "custom",
      entity_values: [...new Set(customEntities)],
    };
  }

  return {
    enable_privacy: entityConfiguration?.enable_privacy,
    preset_type: entityConfiguration?.preset[0]?.type,
    entity_values: entityConfiguration?.preset[0]?.entity_values,
  };
};

export const handleSendTextMessage = async ({
  inputValue,
  chat_id,
  navigate,
  sendMessage,
  prompt,
  metadata,
  prompt_id,
}: any) => {
  try {
    setIsDocDeleted(false);
    const messageHistory = getMessageHistory();
    const newChatId = chat_id ?? uuidv4();
    const entityConfiguration = getEntityConfiguration();
    const privateAIConfig = formatEntityConfiguration(entityConfiguration);
    const payload: any = {
      ...privateAIConfig,
      text: inputValue,
      prompt,
      chat_id: newChatId,
      message_type: "text",
      chat_history: getRecentChatHistory(messageHistory),
      metadata,
    };

    if (prompt_id) {
      payload.prompt_id = prompt_id;
    }

    if (!chat_id) {
      createTempChat({ inputValue, newChatId, navigate });
    }

    setMessageHistory((prev: any) => {
      return [
        ...prev,
        {
          text: payload?.text,
          reid_text: "",
          message_type: payload?.message_type,
          ...(prompt_id && { prompt_id }),
        },
      ];
    });

    const result: any = await sendMessage(payload);

    if (result?.success) {
      if (result?.data?.reid_text) {
        const url = window.location.pathname;
        const parts = url.split("/");
        const currentChatId = parts[parts.length - 1];

        setMessageHistory((prev: any) =>
          prev.map((item: any, idx: any) => {
            if (
              idx === prev.length - 1 &&
              result?.data?.chat_id === currentChatId
            ) {
              return result?.data;
            }
            return item;
          }),
        );
      }
    } else {
      setTimeout(() => {
        setMessageHistory((prev: any) => {
          prev.pop();
          return prev;
        });
        navigate(ROUTE_PATH.HOME);
        setTempNewChat(null);
      }, 1500);
    }
  } catch (err: any) {
    setTimeout(() => {
      setMessageHistory((prev: any) => {
        prev.pop();
        return prev;
      });

      if (!chat_id) {
        navigate(ROUTE_PATH.HOME);
        setTempNewChat(null);
      }
    }, 1500);
  }
};

export const getFileExtension = (fileName: string) => {
  const parts = fileName.split(".");
  return parts[parts.length - 1];
};
