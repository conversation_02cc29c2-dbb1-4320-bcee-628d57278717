import { RiUserFollowLine } from "@remixicon/react";
import { useUpdateMemberRole } from "api";
import { useInvalidateQuery } from "hooks";
import toast from "react-hot-toast";
import { setConfirmModalConfig } from "stores";

const useHandleMemberPermission = ({ queryKeys = [] }: any) => {
  const { mutateAsync: updateMember } = useUpdateMemberRole();
  const [invalidateQueries] = useInvalidateQuery();

  const onClickItem = ({ row, organization, permission }: any) => {
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () =>
          handlePermissionChange({ row, organization, permission }),
        content: {
          heading: "Update Permission",
          description: `Are you sure you want to change permission for this user?`,
        },
        icon: RiUserFollowLine,
        iconColor: "#ad986f",
      },
    });
  };

  const handlePermissionChange = async ({
    row,
    organization,
    permission,
  }: any) => {
    try {
      const params = {
        org_id: organization?.id,
        member_id: row?.id,
        payload: {
          role: permission,
        },
      };
      const result: any = await updateMember(params);
      if (result?.success) {
        toast.success(result?.message || "Permission Updated Successfully!");
        invalidateQueries(queryKeys);
      }
    } catch (err: any) {
      console.log(err);
    }
  };

  return { onClickItem };
};

export default useHandleMemberPermission;
