import { PlanInterface } from "types";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface PayloadInterface {
  planInfo: PlanInterface;
  myPlan: PlanInterface;
}

const initialState = {
  planInfo: {},
  myPlan: {},
};

const planStore = (set: any) => ({
  ...initialState,
  setPlanInfo: (data: PlanInterface) =>
    set((state: PayloadInterface) => ({ ...state, planInfo: data })),
  setMyPlan: (data: PlanInterface) =>
    set((state: PayloadInterface) => ({ ...state, myPlan: data })),
  resetPlanState: () => set(() => initialState),
});

const usePlanStore: any = create(
  devtools(
    persist(planStore, {
      name: "plan",
    }),
  ),
);

export const { setPlanInfo, setMyPlan, resetPlanState } =
  usePlanStore.getState();

export default usePlanStore;
