@use "/src/styles/mixins/mixins.scss" as mixins;

.outgoing {
  max-width: 55%;
  margin-left: auto;

  @media only screen and (max-width: 1599px) {
    max-width: 75%;
  }

  @media only screen and (max-width: 767px) {
    max-width: 90%;
  }

  @media only screen and (max-width: 576px) {
    max-width: 100%;
    font-size: 14px;
  }

  .outgoing-data {
    &-details {
      gap: 10px;
    }

    .user {
      @include mixins.user-chat-message;

      &-message-text {
        @include mixins.slim-scrollbar;

        pre {
          @include mixins.slim-scrollbar;
        }

        @media only screen and (max-width: 576px) {
          max-width: 250px;
        }
      }
    }
    .remove-btn {
      position: absolute;
      top: 3px;
      right: 5px;
      background-color: #fff;
      transition: all 0.4s ease;

      @media only screen and (min-width: 992px) {
        opacity: 0;
      }
    }
  }
}
