import { Node, mergeAttributes } from "@tiptap/core";

export interface AiInstructionLabelOptions {
  HTMLAttributes: Record<string, any>;
  labelText: string;
}

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    aiInstructionLabel: {
      /** Insert AI instruction label inline */
      insertAiInstructionLabel: () => ReturnType;
      /** Remove any AI instruction labels within the current selection and just before it */
      removeAiInstructionLabelsInSelection: () => ReturnType;
    };
  }
}

export const AiInstructionLabel = Node.create<AiInstructionLabelOptions>({
  name: "aiInstructionLabel",

  inline: true,
  group: "inline",
  atom: true,
  selectable: false,
  draggable: false,

  addOptions() {
    return {
      HTMLAttributes: {},
      labelText: "Instruction to AI: ",
    };
  },

  addAttributes() {
    return {
      labelText: {
        default: this.options.labelText,
        parseHTML: (element) => element.getAttribute("data-label-text") || this.options.labelText,
        renderHTML: (attributes) => ({
          "data-label-text": attributes.labelText,
        }),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-ai-instruction-label="true"]',
      },
    ];
  },

  renderHTML({ node, HTMLAttributes }) {
    const { labelText } = node.attrs as { labelText: string };
    return [
      "span",
      mergeAttributes(
        {
          "data-ai-instruction-label": "true",
          class: "ai-instruction-label",
          contenteditable: "false",
        },
        this.options.HTMLAttributes,
        HTMLAttributes,
      ),
      labelText,
    ];
  },

  addCommands() {
    return {
      insertAiInstructionLabel:
        () =>
        ({ state, tr, dispatch }) => {
          const { schema, selection } = state;
          const type = schema.nodes.aiInstructionLabel;
          if (!type) return false;
          const node = type.create();
          const pos = selection.from;
          tr.insert(pos, node);
          if (dispatch) dispatch(tr);
          return true;
        },
      removeAiInstructionLabelsInSelection:
        () =>
        ({ state, tr, dispatch }) => {
          const { selection, doc, schema } = state;
          const type = schema.nodes.aiInstructionLabel;
          if (!type) return false;
          const from = selection.from;
          const to = selection.to;
          // Remove labels within selection
          const positionsToDelete: Array<{ from: number; to: number }> = [];
          doc.nodesBetween(from, to, (node, pos) => {
            if (node.type === type) {
              positionsToDelete.push({ from: pos, to: pos + node.nodeSize });
            }
            return true;
          });
          // Also remove label immediately before selection if present
          const $from = doc.resolve(from);
          const nodeBefore = $from.nodeBefore;
          if (nodeBefore && nodeBefore.type === type) {
            const beforePos = from - nodeBefore.nodeSize;
            positionsToDelete.push({ from: beforePos, to: from });
          }
          // Apply deletions from end to start to keep positions valid
          positionsToDelete
            .sort((a, b) => b.from - a.from)
            .forEach(({ from, to }) => {
              tr.delete(from, to);
            });
          if (positionsToDelete.length > 0 && dispatch) dispatch(tr);
          return positionsToDelete.length > 0;
        },
    };
  },
});

export default AiInstructionLabel;

