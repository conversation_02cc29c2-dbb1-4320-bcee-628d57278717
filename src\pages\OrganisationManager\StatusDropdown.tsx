import { USER_ROLE } from "globals";
import { useHandleMemberPermission } from "hooks";
import { Dropdown, DropdownButton } from "react-bootstrap";

const StatusDropdown = ({
  row,
  organization,
  queryKeys = [],
  isOpen,
  onToggle,
  disabled = false,
}: any) => {
  const { onClickItem } = useHandleMemberPermission({ queryKeys });

  return (
    <DropdownButton
      className="status-dropdown active"
      title={row?.role}
      onClick={(e: any) => e.stopPropagation()}
      variant=""
      show={isOpen}
      onToggle={onToggle}
      disabled={disabled}
    >
      {USER_ROLE.filter((item: any) => item.value !== row?.role).map(
        (item: any) => {
          const Icon = item.icon;
          return (
            <Dropdown.Item
              className="text-capitalize d-flex align-items-center gap-2"
              key={item.value}
              onClick={() =>
                onClickItem({ row, organization, permission: item.value })
              }
            >
              <Icon className="align-middle" />
              {item.label}
            </Dropdown.Item>
          );
        },
      )}
    </DropdownButton>
  );
};

export default StatusDropdown;
