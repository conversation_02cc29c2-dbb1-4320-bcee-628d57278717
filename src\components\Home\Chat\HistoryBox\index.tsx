import { useChatHistoryQuery } from "api";
import { useEffect, useMemo, useRef, useState } from "react";
import { LoaderIcon } from "react-hot-toast";
import InfiniteScroll from "react-infinite-scroll-component";
import useChatStore from "stores/chat";
import { groupDataByDate } from "utils";
import HistoryItem from "./HistoryItem";
import NewChatButton from "./NewChatButton";
import "./styles.scss";

const HistoryBox = ({ onCloseCanvas }: any) => {
  const [page, setPage] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [historyList, setHistoryList] = useState<Record<string, any>[]>([]);
  const [openItemId, setOpenItemId] = useState<any>(null);

  const scrollableDivRef = useRef<HTMLDivElement>(null);
  const tempNewChat = useChatStore((state) => state.tempNewChat);

  const { data: { data: chatHistory = [], count } = {}, refetch } =
    useChatHistoryQuery({
      page,
      limit: 30,
    });

  useEffect(() => {
    if (chatHistory?.length) {
      const element = scrollableDivRef.current;
      const previousScrollHeight = element?.scrollHeight || 0;
      const previousScrollTop = element?.scrollTop || 0;

      setHistoryList((prevList) => {
        if (page === 1) {
          return chatHistory;
        } else {
          const uniqueChats = chatHistory.filter(
            (newChat: any) =>
              !prevList.some(
                (existingChat: any) => existingChat.chat_id === newChat.chat_id,
              ),
          );
          return [...prevList, ...uniqueChats];
        }
      });
      setTotalCount(count);

      if (element) {
        const newScrollHeight = element.scrollHeight;
        element.scrollTop =
          previousScrollTop + (newScrollHeight - previousScrollHeight);
      }
    }
  }, [chatHistory, page]);

  useEffect(() => {
    refetch();
  }, [page]);

  useEffect(() => {
    if (tempNewChat?.chat_id) {
      setHistoryList((prevList) => {
        const chatExists = prevList.some(
          (chat) => chat.chat_id === tempNewChat.chat_id,
        );
        if (chatExists) {
          return prevList.map((chat) =>
            chat.chat_id === tempNewChat.chat_id ? tempNewChat : chat,
          );
        }
        return [tempNewChat, ...prevList];
      });
    }
  }, [tempNewChat?.chat_id]);

  const groupedData = useMemo(
    () => groupDataByDate(historyList),
    [historyList],
  );

  const handleNext = () => {
    setTimeout(() => {
      setPage(page + 1);
    }, 1000);
  };

  return (
    <div className="history-box bg-white h-100">
      <NewChatButton onCloseCanvas={onCloseCanvas} />

      <div
        className="history-box-listing-wrapper position-relative d-flex flex-column overflow-auto"
        id="scrollableDiv"
        ref={scrollableDivRef}
      >
        <InfiniteScroll
          dataLength={historyList?.length}
          loader={
            <div className="d-flex align-items-center justify-content-center">
              <LoaderIcon />
            </div>
          }
          hasMore={historyList?.length < totalCount}
          next={handleNext}
          scrollableTarget={"scrollableDiv"}
          className="h-100"
        >
          {groupedData &&
            Object.keys(groupedData)?.length > 0 &&
            Object.keys(groupedData).map((groupItem, idx) => {
              if (!groupedData?.[groupItem]?.length) return;
              return (
                <div className="history-box-listing-wrapper-data" key={idx}>
                  <p className="history-box-listing-wrapper-data-time mb-1 text-capitalize fw-bold position-sticky top-0">
                    {groupItem}
                  </p>

                  <ul className="list-unstyled m-0 history-box-listing-wrapper-data-list d-flex flex-column gap-1">
                    {groupedData?.[groupItem].map((item: any, itemIdx: any) => (
                      <HistoryItem
                        key={itemIdx}
                        itemInfo={item}
                        setHistoryList={setHistoryList}
                        setTotalCount={setTotalCount}
                        openItemId={openItemId}
                        setOpenItemId={setOpenItemId}
                      />
                    ))}
                  </ul>
                </div>
              );
            })}
        </InfiniteScroll>
      </div>
    </div>
  );
};

export default HistoryBox;
