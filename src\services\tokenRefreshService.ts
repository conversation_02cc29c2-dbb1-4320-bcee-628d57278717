import { Auth0ContextInterface } from "@auth0/auth0-react";
import auth0, { Auth0Error, Auth0DecodedHash } from "auth0-js";
import { AUTH0_INFO, LOGIN_TYPE } from "globals";
import { setUserInfo, resetUserState } from "stores/user";
import { getAppOriginURL } from "utils";

interface TokenRefreshResult {
  success: boolean;
  token?: string;
  error?: string;
}

interface TokenInfo {
  token: string;
  expiresAt: number;
  refreshToken?: string;
}

class TokenRefreshService {
  private refreshPromise: Promise<TokenRefreshResult> | null = null;
  private refreshTimer: NodeJS.Timeout | null = null;
  private auth0Context: Auth0ContextInterface | null = null;

  // Set Auth0 context for SSO token refresh
  setAuth0Context(context: Auth0ContextInterface) {
    this.auth0Context = context;
  }

  // Parse JWT token to get expiration time
  private parseJWT(token: string): { exp?: number } {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error parsing JWT:', error);
      return {};
    }
  }

  // Check if token is expired or will expire soon (within 5 minutes)
  isTokenExpired(token: string, bufferMinutes: number = 5): boolean {
    const decoded = this.parseJWT(token);
    if (!decoded.exp) return true;
    
    const now = Math.floor(Date.now() / 1000);
    const bufferSeconds = bufferMinutes * 60;
    return decoded.exp <= (now + bufferSeconds);
  }

  // Get token expiration time
  getTokenExpiration(token: string): number | null {
    const decoded = this.parseJWT(token);
    return decoded.exp ? decoded.exp * 1000 : null;
  }

  // Refresh token using Auth0 React SDK (for SSO login)
  async refreshTokenSSO(): Promise<TokenRefreshResult> {
    if (!this.auth0Context) {
      return { success: false, error: 'Auth0 context not available' };
    }

    try {
      const token = await this.auth0Context.getAccessTokenSilently({
        authorizationParams: {
          audience: AUTH0_INFO.AUTH0_AUDIENCE,
        },
        cacheMode: 'off', // Force refresh
      });

      return { success: true, token };
    } catch (error: any) {
      console.error('SSO token refresh failed:', error);
      return { success: false, error: error.message || 'Token refresh failed' };
    }
  }

  // Refresh token using Auth0 WebAuth (for embedded login)
  async refreshTokenEmbedded(refreshToken: string): Promise<TokenRefreshResult> {
    return new Promise((resolve) => {
      const { AUTH0_DOMAIN, AUTH0_CLIENT_ID, AUTH0_SCOPE } = AUTH0_INFO;
      
      const webAuth = new auth0.WebAuth({
        domain: AUTH0_DOMAIN,
        clientID: AUTH0_CLIENT_ID,
        scope: AUTH0_SCOPE,
      });

      webAuth.renewAuth(
        {
          refreshToken,
          audience: AUTH0_INFO.AUTH0_AUDIENCE,
          scope: AUTH0_SCOPE,
        },
        (err: Auth0Error | null, result: Auth0DecodedHash | null) => {
          if (err) {
            console.error('Embedded token refresh failed:', err);
            resolve({ success: false, error: err.description || 'Token refresh failed' });
            return;
          }

          if (result?.accessToken) {
            resolve({ success: true, token: result.accessToken });
          } else {
            resolve({ success: false, error: 'No access token received' });
          }
        }
      );
    });
  }

  // Main refresh method that handles both login types
  async refreshToken(loginType: string, refreshToken?: string): Promise<TokenRefreshResult> {
    // Prevent multiple simultaneous refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this.performTokenRefresh(loginType, refreshToken);
    
    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.refreshPromise = null;
    }
  }

  private async performTokenRefresh(loginType: string, refreshToken?: string): Promise<TokenRefreshResult> {
    try {
      let result: TokenRefreshResult;

      if (loginType === LOGIN_TYPE.SSO) {
        result = await this.refreshTokenSSO();
      } else if (loginType === LOGIN_TYPE.EMBEDDED && refreshToken) {
        result = await this.refreshTokenEmbedded(refreshToken);
      } else {
        return { success: false, error: 'Invalid login type or missing refresh token' };
      }

      if (result.success && result.token) {
        // Update user store with new token
        const userInfo = JSON.parse(localStorage.getItem('user') || '{}');
        if (userInfo.state?.userInfo) {
          setUserInfo({
            ...userInfo.state.userInfo,
            token: result.token,
          });
        }

        // Schedule next refresh
        this.scheduleTokenRefresh(result.token, loginType, refreshToken);
      }

      return result;
    } catch (error: any) {
      console.error('Token refresh error:', error);
      return { success: false, error: error.message || 'Unknown error' };
    }
  }

  // Schedule automatic token refresh before expiration
  scheduleTokenRefresh(token: string, loginType: string, refreshToken?: string) {
    // Clear existing timer
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    const expirationTime = this.getTokenExpiration(token);
    if (!expirationTime) return;

    // Schedule refresh 5 minutes before expiration
    const refreshTime = expirationTime - Date.now() - (5 * 60 * 1000);
    
    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(async () => {
        console.log('Automatically refreshing token...');
        const result = await this.refreshToken(loginType, refreshToken);
        
        if (!result.success) {
          console.error('Automatic token refresh failed:', result.error);
          // Don't reset user state here, let the API interceptor handle it
        }
      }, refreshTime);
    }
  }

  // Initialize token refresh for current session
  initializeTokenRefresh(token: string, loginType: string, refreshToken?: string) {
    if (!token || this.isTokenExpired(token, 0)) {
      // Token is already expired, don't schedule refresh
      return;
    }

    this.scheduleTokenRefresh(token, loginType, refreshToken);
  }

  // Clean up timers
  cleanup() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
    this.refreshPromise = null;
  }

  // Handle logout
  logout() {
    this.cleanup();
    resetUserState();
    window.location.href = getAppOriginURL();
  }
}

// Export singleton instance
export const tokenRefreshService = new TokenRefreshService();
export default tokenRefreshService;
