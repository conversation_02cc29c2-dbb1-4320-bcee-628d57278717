@use "/src/styles/mixins/mixins.scss";

.pricing-tiers-modal {
  .modal-dialog {
    @media only screen and (min-width: 992px) {
      width: 625px;
      max-width: 700px;
    }
  }

  .pricing-list {
    @include mixins.slim-scrollbar;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;

    .pricing-item {
      display: flex;
      width: 90%;
      max-width: 450px;
      padding: 5px 0;
      justify-content: space-between;
      position: relative;

      &::after {
        content: "";
        display: block;
        width: 100%;
        height: 2px;
        position: absolute;
        bottom: 0;
        right: 10px;
      }
    }

    .pricing-column {
      flex: 1;
      text-align: left;

      &.left {
        padding-left: 10px;
      }

      &.right {
        padding-left: 20px;
      }
    }

    .highlight-item {
      font-weight: bold;

      &::after {
        background: linear-gradient(to right, #1fc5a8, #576182);
      }
    }
  }
}
