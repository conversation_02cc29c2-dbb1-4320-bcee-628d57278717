import { RiCloseCircleLine, RiFileChart2Fill } from "@remixicon/react";
import { useGenerateInvoice, useGetMySubscription } from "api";
import {
  BackBreadcrumb,
  DataGridTable,
  HoverTooltip,
  TableToolbar,
} from "components";
import { IMAGE_PATH, SETTINGS } from "globals";
import { useCancelSubscription } from "hooks";
import { useEffect, useState } from "react";
import { Button, Image } from "react-bootstrap";
import { LoaderIcon } from "react-hot-toast";
import { formatUnixTimestamp } from "utils";
import "./styles.scss";

const MySubscriptions = () => {
  const [paginationConfig, setPaginationConfig] = useState<any>({
    page: 1,
    limit: 25,
  });
  const [isGenerating, setIsGenerating] = useState<any>({});

  const {
    data: { subsList = {} } = {},
    refetch,
    isLoading,
  } = useGetMySubscription({
    params: { ...paginationConfig },
  });
  const { data: mySubscriptions = [], count: totalCount } = subsList;

  const { mutateAsync: generateInvoice } = useGenerateInvoice();

  const { onClickCancel } = useCancelSubscription({ refetch });

  const onClickExport = async (e: any, id: any, invoice_id: any) => {
    e.stopPropagation();
    setIsGenerating({ [invoice_id]: true });
    try {
      const result: any = await generateInvoice({
        subscription_id: id,
        invoice_id,
      });
      if (result?.success && result?.data?.invoice?.invoice_pdf) {
        window.open(result?.data?.invoice?.invoice_pdf, "_blank");
      }
    } catch (err) {
      console.log(err);
    } finally {
      setIsGenerating({ [id]: false });
    }
  };

  const columns: any = [
    {
      field: "plan_name",
      headerName: "Name",
      renderCell: (row: any) => (
        <p className="mb-0 fw-bold">{row?.plan_name ?? "NA"}</p>
      ),
    },
    {
      field: "billing_cycle_anchor",
      headerName: "Start Date",
      renderCell: (row: any) => (
        <p className="mb-0 fw-bold">
          {formatUnixTimestamp(row?.billing_cycle_anchor) ?? "NA"}
        </p>
      ),
    },
    {
      field: "cancel_at",
      headerName: "End Date",
      renderCell: (row: any) => (
        <p className="mb-0 fw-bold">
          {row?.cancel_at ? (
            formatUnixTimestamp(row?.cancel_at)
          ) : (
            <span className="text-danger ms-4">N/A</span>
          )}
        </p>
      ),
    },
    {
      field: "canceled_at",
      headerName: "Canceled At",
      renderCell: (row: any) => (
        <p className="mb-0 fw-bold">
          {row?.canceled_at ? (
            formatUnixTimestamp(row?.canceled_at)
          ) : (
            <span className="text-danger ms-4">N/A</span>
          )}
        </p>
      ),
    },
    {
      field: "amount",
      headerName: "Amount",
      renderCell: (row: any) => {
        const originalPrice = SETTINGS.CURRENCY_INFO.GBP.stripeConversion(
          row?.amount,
        );
        const discountedPrice = SETTINGS.CURRENCY_INFO.GBP.stripeConversion(
          row?.discount_amount,
        );
        const hasDiscount = row?.has_discount;

        return (
          <div className="price-container">
            {hasDiscount ? (
              <>
                <span className="fw-bold d-block">{discountedPrice}</span>
                <span className="text-muted text-decoration-line-through me-2">
                  {originalPrice}
                </span>
              </>
            ) : (
              <span className="m-0 fw-bold">{originalPrice}</span>
            )}
          </div>
        );
      },
    },
    {
      field: "status",
      headerName: "Status",
      renderCell: (row: any) => (
        <p
          className={`mb-0 rounded-pill text-center font-light fw-bold px-1 py-1 text-capitalize status-pill ${row?.status}`}
        >
          {row?.status}
        </p>
      ),
    },
    {
      field: "actions",
      headerName: "Actions",
      renderCell: (row: any) => (
        <>
          <HoverTooltip title="Download Invoice" customClass="fw-bold">
            <Button
              className="bg-blue border-blue p-2 lh-1 me-3 action-item-btn"
              onClick={(e: any) =>
                onClickExport(e, row.id, row?.latest_invoice)
              }
              disabled={!row?.latest_invoice}
            >
              {isGenerating?.[row.latest_invoice] ? (
                <LoaderIcon className="loader-icon" key={row.latest_invoice} />
              ) : (
                <Image
                  src={IMAGE_PATH.pdfFileIcon}
                  className="w-100 h-100 object-fit-contain"
                  style={{ objectPosition: "center center" }}
                />
              )}
            </Button>
          </HoverTooltip>
          {!row?.canceled_at && (
            <HoverTooltip title="Cancel Subscription" customClass="fw-bold">
              <Button
                className="bg-blue border-blue p-2 lh-1 action-item-btn"
                onClick={(e: any) => onClickCancel(e, row.id)}
              >
                <RiCloseCircleLine />
              </Button>
            </HoverTooltip>
          )}
        </>
      ),
    },
  ];

  useEffect(() => {
    refetch();
  }, [paginationConfig?.limit, paginationConfig?.page, refetch]);

  return (
    <main className="my-subscriptions-wrapper bg-white">
      <BackBreadcrumb />
      <TableToolbar
        title="My Subscriptions"
        description="View the status of your subscriptions, plus manage or download details for your records."
        icon={RiFileChart2Fill}
      />
      <DataGridTable
        columns={columns}
        rows={mySubscriptions}
        loading={isLoading}
        paginationProps={{
          paginationConfig,
          setPaginationConfig,
          totalCount,
        }}
      />
    </main>
  );
};

export default MySubscriptions;
