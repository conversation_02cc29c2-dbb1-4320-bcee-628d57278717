import { Ri<PERSON>ore2Fill } from "@remixicon/react";
import { useGetOrganisation } from "api";
import { CustomDropdown, DataGridTable } from "components";
import {
  KnowledgeBaseToolbar,
  PermissionGroup,
} from "features/KnowledgeDatabase/components";
import { FILE_TYPE_INFO, IMAGE_PATH } from "globals";
import { useDebounce, useUserStatus } from "hooks";
import CreateOrganisation from "pages/OrganisationManager/CreateOrganisation";
import { useState } from "react";
import { Image } from "react-bootstrap";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { getFileExtension, getFormattedDate } from "utils";
import { useGetKnowledgeBase } from "../api";
import { OWNER_TYPES } from "../globals";
import useDeleteFileOrFolder from "../hooks/useDeleteFileOrFolder";
import useHideOrShowDocs from "../hooks/useHideOrShowDocs";
import { KNOWLEDGE_BASE_ROUTE_PATH } from "../routePath";
import "./styles.scss";

export interface FilterInterface {
  search?: string | undefined;
  owner_type?: string | undefined;
  sort?: string;
}

export default function KnowledgeBase() {
  const [paginationConfig, setPaginationConfig] = useState<any>({
    page: 1,
    limit: 25,
  });
  const [filters, setFilters] = useState<FilterInterface>({
    search: undefined,
    owner_type: OWNER_TYPES.ORGANIZATION,
    sort: "asc",
  });
  const [openDropdownId, setOpenDropdownId] = useState<{
    rowId: string;
    type: "actions" | "permissions";
  } | null>(null);

  const { parent_id }: { parent_id?: string } = useParams();

  const debouncedQry = useDebounce(filters.search, 500);
  const [searchParams] = useSearchParams();
  const owner_type = searchParams.get("owner_type") || OWNER_TYPES.ORGANIZATION;

  const navigate = useNavigate();
  const { onClickDelete } = useDeleteFileOrFolder();
  const { onClickHideOrShow } = useHideOrShowDocs();
  const { data: { organization = {} } = {}, isLoading: isOrgLoading } =
    useGetOrganisation({
      staleTime: 0,
    });

  const { isOrgAdmin, isOrgUser, isIndividualUser } = useUserStatus();

  const isOrganizationExist =
    organization?.id && owner_type === OWNER_TYPES.ORGANIZATION;
  const isActionsEnabled =
    isOrganizationExist || owner_type === OWNER_TYPES.USER;
  const isUserActionsEnabled = isOrgUser || isIndividualUser;

  const {
    data: { files = [], count: totalCount, breadcrumb: breadcrumbs = [] } = {},
    isLoading,
  }: any = useGetKnowledgeBase(
    {
      ...paginationConfig,
      owner_type,
      search: debouncedQry,
      parent_id,
    },
    {
      enabled: !!isActionsEnabled,
    }
  );

  const handleDropdownToggle = (
    rowId: string,
    type: "actions" | "permissions"
  ) => {
    setOpenDropdownId((prevId) =>
      prevId?.rowId === rowId && prevId?.type === type ? null : { rowId, type }
    );
  };

  const columns = [
    {
      field: "name",
      headerName: "File Name",
      renderCell: (row: any) => {
        const extension = getFileExtension(row?.name);
        const fileInfo = FILE_TYPE_INFO[extension]
          ? FILE_TYPE_INFO[extension]
          : {
              icon:
                row?.type === "file"
                  ? IMAGE_PATH.colorInfoDoc
                  : IMAGE_PATH.folderIcon,
            };

        return (
          <div className="table-user-card d-flex justify-content-start align-items-center">
            <Image
              src={fileInfo.icon}
              alt={`${fileInfo.type || "File"} Icon`}
              className="d-block table-user-image object-fit-cover"
              style={{ width: "30px", height: "30px" }}
            />

            <div className="table-user-card-data">
              <h3 className="mb-0 name">{row?.name}</h3>
            </div>
          </div>
        );
      },
    },
    {
      field: "created_at",
      headerName: "Created Date",
      renderCell: (row: any) => (
        <p
          className="mb-0 d-flex justify-content-start align-items-center"
          style={{ gap: "10px" }}
        >
          {getFormattedDate(row?.created_at) ?? "NA"}
        </p>
      ),
    },
    owner_type === OWNER_TYPES.ORGANIZATION && {
      field: "role",
      headerName: "Permission Group",
      renderCell: (row: any) => (
        <PermissionGroup
          row={row}
          disabled={!!parent_id || !isOrgAdmin}
          isOpen={
            openDropdownId?.rowId === row?.id &&
            openDropdownId?.type === "permissions"
          }
          onToggle={() => handleDropdownToggle(row?.id, "permissions")}
        />
      ),
    },
    isOrgAdmin || (isUserActionsEnabled && !isOrganizationExist)
      ? {
          field: "actions",
          headerName: "Actions",
          renderCell: (row: any) => {
            const onSelect = (value: string) => {
              if (value === "delete") {
                onClickDelete({ type: row?.type, itemId: row?.id });
              }

              if (value === "hide" || value === "show") {
                onClickHideOrShow({ itemInfo: row });
              }
            };
            return (
              <div
                className="d-flex gap-2"
                onClick={(e) => e.stopPropagation()}
                key={row?.id}
              >
                <CustomDropdown
                  title={<RiMore2Fill size={18} />}
                  items={[{ label: "Delete", value: "delete" }].concat(
                    parent_id ||
                      isUserActionsEnabled ||
                      (isOrgAdmin && !isOrganizationExist)
                      ? []
                      : [
                          row?.access_control?.visible
                            ? { label: "Hide", value: "hide" }
                            : { label: "Show", value: "show" },
                        ]
                  )}
                  className="table-actions"
                  onSelect={onSelect}
                  preserveTitle
                  isOpen={
                    openDropdownId?.rowId === row?.id &&
                    openDropdownId?.type === "actions"
                  }
                  onToggle={() => handleDropdownToggle(row?.id, "actions")}
                />
              </div>
            );
          },
        }
      : null,
  ].filter(Boolean);

  return (
    <div className="knowledge-base-wrapper">
      <KnowledgeBaseToolbar
        setFilters={setFilters}
        filters={filters}
        breadcrumbs={breadcrumbs}
        owner_type={owner_type}
        isActionsEnabled={isActionsEnabled}
        isUserActionsEnabled={isUserActionsEnabled}
        isOrganizationExist={isOrganizationExist}
        isOrgAdmin={isOrgAdmin}
      />
      {isActionsEnabled ? (
        <DataGridTable
          columns={columns}
          rows={files}
          paginationProps={{
            paginationConfig,
            setPaginationConfig,
            totalCount,
          }}
          sortDirection={filters.sort}
          onSort={() => {
            setFilters({
              ...filters,
              sort: filters.sort === "asc" ? "desc" : "asc",
            });
          }}
          onRowClick={(row: any) => {
            if (row?.type === "folder") {
              navigate(
                `${KNOWLEDGE_BASE_ROUTE_PATH.KNOWLEDGE_BASE}/${row?.id}?owner_type=${owner_type}`
              );
            }
          }}
          loading={isLoading}
        />
      ) : isOrgLoading ? null : (
        <CreateOrganisation />
      )}
    </div>
  );
}
