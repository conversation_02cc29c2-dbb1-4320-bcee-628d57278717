import { useCurrentUpcomingSubscription, useGetSubscriptionPlans } from "api";
import { SubscriptionCard } from "components";
import { SubscriptionCardSkeleton } from "components/Common/Skeletons";
import { IMAGE_PATH } from "globals";
import { useEffect } from "react";
import { Col, Container, Image, Row } from "react-bootstrap";
import { setMyPlan } from "stores";
import "./styles.scss";
import useUserStore from "stores/user";

const Subscriptions = () => {
  const { data: plans } = useGetSubscriptionPlans();
  const { data: currentUpcomingSubscription = {} } =
    useCurrentUpcomingSubscription();
  const userDetails = useUserStore((state) => state.userInfo.user);

  useEffect(() => {
    setMyPlan(currentUpcomingSubscription);
  }, [currentUpcomingSubscription]);

  return (
    <main className="subscriptions d-flex bg-white flex-column align-items-stretch w-100">
      <div className="breadcrumb-wrapper d-flex flex-lg-row align-items-center justify-content-between">
        <div className="page-details d-flex align-items-lg-center justify-content-start">
          <Image
            src={IMAGE_PATH.settingPageImg}
            alt="settings"
            className="page-details-img object-fit-contain mt-sm-0 mt-2"
          />

          <div className="page-details-page-name">
            <h3 className="mb-lg-2 mb-1 fw-bold">Subscriptions</h3>
            <p className="mb-0">Upgrade Your Plan</p>
          </div>
        </div>
      </div>

      <hr className="m-0 border-0" />

      <Container fluid>
        <Row className="gy-4">
          {plans?.length > 0 ? (
            <>
              {plans.map((plan: any, index: number) => (
                <Col lg="6" md="6" xl="6" xxl="3" key={index}>
                  <SubscriptionCard
                    plan={plan}
                    currentUpcomingSubscription={currentUpcomingSubscription}
                    userDetails={userDetails}
                  />
                </Col>
              ))}
            </>
          ) : (
            <>
              {Array.from({ length: 4 }).map((_: any, idx: number) => (
                <Col lg="6" md="6" xl="6" xxl="3" key={idx}>
                  <SubscriptionCardSkeleton />
                </Col>
              ))}
            </>
          )}
        </Row>
      </Container>
    </main>
  );
};

export default Subscriptions;
