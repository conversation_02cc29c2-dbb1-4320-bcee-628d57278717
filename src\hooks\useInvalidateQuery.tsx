import { useQueryClient } from "@tanstack/react-query";

const useInvalidateQuery = () => {
  const queryClient = useQueryClient();

  const invalidateQueries = async (queryKeys: any) => {
    if (!queryKeys.length) return;

    await queryClient.invalidateQueries({
      predicate: (query) => queryKeys?.includes(query?.queryKey[0] as string),
    });
  };

  return [invalidateQueries];
};

export default useInvalidateQuery;
