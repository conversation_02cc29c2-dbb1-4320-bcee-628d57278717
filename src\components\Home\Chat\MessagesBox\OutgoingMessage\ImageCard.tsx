import { RiDeleteBin5Fill } from "@remixicon/react";
import { useDeleteDoc } from "hooks";
import { useEffect, useState } from "react";
import { Button } from "react-bootstrap";
import { LoaderIcon } from "react-hot-toast";

const ImageCard = ({ metadata, msgId }: any) => {
  const { doc_id, preview_url } = metadata ?? {};
  const [isLoading, setIsLoading] = useState(true);

  const { onClickDelete } = useDeleteDoc({
    metadata,
    msgId,
  });

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  useEffect(() => {
    if (preview_url) {
      setIsLoading(true);
    }
  }, [preview_url]);

  return (
    <div className="user-message-image ms-auto position-relative">
      {doc_id && (
        <Button
          variant="link"
          className="remove-btn p-0"
          onClick={onClickDelete}
        >
          <RiDeleteBin5Fill size={"20px"} color="red" />
        </Button>
      )}
      {isLoading && (
        <div className="image-loader">
          <LoaderIcon />
        </div>
      )}
      <div
        style={{
          backgroundImage: `url(${preview_url})`,
        }}
        className={`uploaded-image ${isLoading ? "loading" : ""}`}
      >
        <img
          src={preview_url}
          alt="Uploaded preview"
          onLoad={handleImageLoad}
          style={{ display: "none" }}
        />
      </div>
    </div>
  );
};

export default ImageCard;
