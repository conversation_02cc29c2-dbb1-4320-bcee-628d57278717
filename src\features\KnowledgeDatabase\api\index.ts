import { useMutation, useQuery } from "@tanstack/react-query";
import { apiClient } from "api";
import { API_ENDPOINTS } from "./endpoints";

export const useGetKnowledgeBase = (params = {}, options: any = {}) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_KNOWLEDGE_BASE, {
        params,
      });
      return response?.data;
    },
    queryKey: ["knowledge-base", ...Object.values(params)],
    ...options,
  });

export const useAddKnowledgeBase = () =>
  useMutation({
    mutationFn: async (payload: FormData) => {
      return apiClient.post(API_ENDPOINTS.ADD_KNOWLEDGE_BASE, payload);
    },
  });

export const useDeleteFile = () =>
  useMutation({
    mutationFn: async ({ id }: any) => {
      return apiClient.delete(API_ENDPOINTS.DELETE_FILE(id));
    },
  });

export const useDeleteFolder = () =>
  useMutation({
    mutationFn: async ({ id }: any) => {
      return apiClient.delete(API_ENDPOINTS.DELETE_FOLDER(id));
    },
  });

export const useUpdatePermissionGroup = () =>
  useMutation({
    mutationFn: async ({ id, access_control }: any) => {
      return apiClient.put(API_ENDPOINTS.UPDATE_PERMISSION_GROUP(id), {
        ...access_control,
      });
    },
  });

export const useGetFAQPrompts = (params = {}) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_FAQ_PROMPTS, {
        params,
      });
      return response?.data;
    },
    queryKey: ["faq-prompts", ...Object.values(params)],
  });

export const useAddFAQPrompt = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      return apiClient.post(API_ENDPOINTS.ADD_FAQ_PROMPT, payload);
    },
  });

export const useDeleteFAQPrompt = () =>
  useMutation({
    mutationFn: async ({ id }: any) => {
      return apiClient.delete(API_ENDPOINTS.DELETE_FAQ_PROMPT(id));
    },
  });

export const useUpdateFAQPrompt = () =>
  useMutation({
    mutationFn: async ({ id, ...payload }: any) => {
      return apiClient.put(API_ENDPOINTS.UPDATE_FAQ_PROMPT(id), payload);
    },
  });
