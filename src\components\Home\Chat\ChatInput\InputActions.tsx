import { Ri<PERSON>ameraFill } from "@remixicon/react";
import { isValidFileType } from "utils";
import FAQDocPreview from "./FAQDocPreview";
import FilePreview from "./FilePreview";
import FileUpload from "./FileUpload";
import SpeechToText from "./SpeechToText";
import { PromptTypes } from "types";

const InputActions = ({
  selectedFile,
  handleRemoveFile,
  selectedDocID,
  uploadDocChatId,
  handleFileChange,
  fileRef,
  uploadProgress,
  textareaRef,
  transcriptionConfig,
  selectedFAQPrompt,
}: any) => {
  const handleImageChange = (file: any) => {
    if (!isValidFileType(file)) {
      return;
    }
    handleFileChange(file);
  };
  return (
    <>
      {selectedFAQPrompt?.type === PromptTypes.FAQ_PROMPT ? (
        <FAQDocPreview selectedFAQPrompt={selectedFAQPrompt} />
      ) : (
        <></>
      )}
      <FilePreview
        selectedFile={selectedFile}
        handleRemoveFile={handleRemoveFile}
        selectedDocID={selectedDocID}
        uploadDocChatId={uploadDocChatId}
        uploadProgress={uploadProgress}
      />
      <FileUpload
        handleFileChange={handleFileChange}
        fileRef={fileRef}
        controlId="fileInput"
      />

      <FileUpload
        handleFileChange={handleImageChange}
        fileRef={fileRef}
        controlId="camera"
        icon={RiCameraFill}
        customClass="camera"
        accept="image/jpeg, image/png, image/jpg"
      />

      <SpeechToText
        textareaRef={textareaRef}
        transcriptionConfig={transcriptionConfig}
      />
    </>
  );
};

export default InputActions;
