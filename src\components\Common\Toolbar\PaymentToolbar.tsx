import { RiAddBoxFill } from "@remixicon/react";
import { IMAGE_PATH } from "globals";
import { Button, Image } from "react-bootstrap";
import "./styles.scss";

interface PaymentToolbarProps {
  onAdButtonClick: () => void;
}

const PaymentToolbar: React.FC<PaymentToolbarProps> = ({ onAdButtonClick }) => {
  return (
    <div className="toolbar-wrapper d-flex flex-sm-row flex-column justify-content-sm-between justify-content-center align-items-stretch">
      <div className="toolbar-wrapper-left">
        <div className="icon d-inline-block lh-1 align-middle">
          <Image
            src={IMAGE_PATH.paymentCardIcon}
            className="toolbar-icon object-fit-contain"
          />
        </div>

        <p className="title mb-0 ms-3 d-inline-block lh-1 align-middle">
          Payment Card(s)
        </p>

        <p className="mb-0 mt-1 description">Manage your payment options</p>
      </div>

      <div className="toolbar-wrapper-right mt-lg-0 mt-2">
        <Button
          className="lh-1 toolbar-btn-blue bg-blue border-blue text-center"
          onClick={onAdButtonClick}
        >
          <RiAddBoxFill size={25} className="align-middle me-2" />
          Add New Card
        </Button>
      </div>
    </div>
  );
};

export default PaymentToolbar;
