import { RiCloseLine, RiDeleteBin5Line } from "@remixicon/react";
import AuthHeaderData from "pages/Auth/AuthHeaderData";
import { Button, Modal } from "react-bootstrap";
import useUtilStore, { setConfirmModalConfig } from "stores/util";
import "./styles.scss";
import { ConfirmModalConfig } from "types";

const ConfirmModal: React.FC = () => {
  const modalConfig = useUtilStore((state) => state.confirmModalConfig);

  const handleClose = () => {
    setConfirmModalConfig({
      ...modalConfig,
      visible: false,
    });
  };

  const defaultContent = {
    heading: "Delete Item?",
    description: "Are you sure you want to delete this item?",
  };

  const {
    visible,
    data: {
      onSubmit = () => {},
      onClose = () => {},
      content = defaultContent,
      icon = RiDeleteBin5Line,
      iconColor = "red",
      showCloseIcon = true,
      buttonText = "Confirm",
      showModalIcon = true,
      customStyling = {},
      showSubmitButton = true,
    },
  }: ConfirmModalConfig = modalConfig;

  const ModalIcon = icon;

  return (
    <Modal
      show={visible}
      onHide={() => {
        if (showCloseIcon) {
          if (onClose) {
            onClose();
          }
          handleClose();
        }
      }}
      keyboard={false}
      centered
    >
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        {showCloseIcon && (
          <Button
            variant="link"
            className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
            onClick={() => {
              if (onClose) {
                onClose();
              }
              handleClose();
            }}
          >
            <RiCloseLine size={"40px"} color="#f9f9f9" />
          </Button>
        )}

        <div
          className="auth-form w-100 d-flex justify-content-center align-items-center flex-column m-0"
          style={{ gap: "30px" }}
        >
          {showModalIcon && <ModalIcon size={"100px"} color={iconColor} />}
          <AuthHeaderData
            heading={content?.heading}
            description={content?.description}
            customStyling={customStyling}
          />

          {showSubmitButton && (
            <div className="website-form w-100">
              <form className="d-flex flex-column" style={{ gap: "30px" }}>
                <div
                  className="action-btns d-flex flex-column"
                  style={{ gap: "30px" }}
                >
                  <Button
                    type="button"
                    className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                    onClick={() => {
                      onSubmit();
                      handleClose();
                    }}
                  >
                    {buttonText}
                  </Button>
                </div>
              </form>
            </div>
          )}
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default ConfirmModal;
