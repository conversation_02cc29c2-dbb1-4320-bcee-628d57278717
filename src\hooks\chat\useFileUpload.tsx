import {
  useGetS3PresignedUrlMutation,
  useUploadDocMutation,
  useUploadFileToS3Mutation,
} from "api";
import { useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { getFileExtension, isValidDocType, isValidFileSize } from "utils";
import { v4 as uuidv4 } from "uuid";

const useFileUpload = ({ setLoading }: any) => {
  const { id: chat_id } = useParams();
  const fileRef = useRef<any>(null);

  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const [selectedDocID, setSelectedDocID] = useState<any>(null);
  const [uploadDocChatId, setUploadDocChatId] = useState<any>(null);

  const { mutateAsync: uploadDoc } = useUploadDocMutation();
  const { mutateAsync: getS3PresignedUrl } = useGetS3PresignedUrlMutation();
  const { mutateAsync: uploadFileToS3 } = useUploadFileToS3Mutation();

  const handleFileUpload = async (file: File, currentChatId: any) => {
    const formData = new FormData();
    formData.append("file", file);
    try {
      const result = await uploadDoc({
        chat_id: currentChatId,
        payload: formData,
        setUploadProgress,
      });
      return result;
    } catch (err: any) {
      handleRemoveFile();
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = async (file: any) => {
    setLoading(true);
    if (!isValidDocType(file) || !isValidFileSize(file)) {
      return;
    }
    if (file) {
      const newChatId = uuidv4();
      const currentChatId = chat_id ?? newChatId;
      setSelectedFile(file);
      try {
        if (file?.type.startsWith("image/")) {
          uploadImage(file, currentChatId);
        } else {
          uploadDocument(file, currentChatId);
        }
      } catch (err: any) {
        handleRemoveFile();
      }
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setSelectedDocID(null);
    setUploadDocChatId(null);
    if (fileRef.current) {
      fileRef.current.value = null;
    }
  };

  const uploadDocument = async (file: any, currentChatId: any) => {
    const fileResponse: any = await handleFileUpload(file, currentChatId);
    if (fileResponse?.success) {
      setSelectedDocID(fileResponse?.data?.doc_id);
      setUploadDocChatId(currentChatId);
    }
  };

  const uploadImage = async (file: any, currentChatId: any) => {
    const doc_id = uuidv4();
    const payload = {
      content_type: file?.type,
      path: `chatredact/${currentChatId}/${doc_id}.${getFileExtension(
        file?.name,
      )}`,
      file_name: file?.name,
    };
    try {
      const s3Response: any = await getS3PresignedUrl(payload);
      if (s3Response?.success) {
        const response = await uploadFileToS3({
          url: s3Response?.data?.url,
          fields: { ...s3Response?.data?.fields, file },
          setUploadProgress,
        });
        if (response?.status === 204) {
          setSelectedDocID(doc_id);
          setUploadDocChatId(currentChatId);
        }
      }
    } catch (err: any) {
      handleRemoveFile();
    } finally {
      setLoading(false);
    }
  };

  return {
    handleFileChange,
    fileRef,
    uploadProgress,
    handleRemoveFile,
    selectedFile,
    selectedDocID,
    uploadDocChatId,
  };
};

export default useFileUpload;
