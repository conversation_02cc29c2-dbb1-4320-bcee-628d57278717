import {
  RiExportFill,
  RiFileTextLine,
  RiSparkling2Line,
} from "@remixicon/react";
import { CustomDropdown } from "components";
import { useExportReportMutation } from "features/Reports/api";
import useReportStore from "features/Reports/store/report";
import { useState } from "react";
import { Spinner } from "react-bootstrap";
import toast from "react-hot-toast";
import { downloadBlob } from "utils";

const ExportReportButton = () => {
  const [isExporting, setIsExporting] = useState(false);
  const { mutateAsync: exportReportMutation } = useExportReportMutation();
  const reportInfo: any = useReportStore((state) => state.reportInfo);

  const handleExport: any = async (value: string) => {
    if (!reportInfo.title) {
      toast.error("Please enter report title");
      return;
    }
    setIsExporting(true);

    try {
      const payload = {
        title: reportInfo?.title || "",
        sections: reportInfo?.sections || [],
        doc_type: value,
        show_report_title: reportInfo?.show_report_title,
      };
      const blob = await exportReportMutation({ payload });
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const baseName = reportInfo.title || "report";
      const extension = value === "pdf" ? "pdf" : "docx";
      const filename = `${baseName}_${timestamp}.${extension}`;
      downloadBlob(blob, filename);
    } catch (error: any) {
      console.error("Export failed:", error.message);
    } finally {
      setIsExporting(false);
    }
  };
  return (
    <CustomDropdown
      title={
        isExporting ? (
          <Spinner size="sm" style={{ margin: "0.25rem" }} />
        ) : (
          <RiExportFill />
        )
      }
      items={[
        {
          label: (
            <span className="d-inline-flex align-items-center gap-2">
              <RiSparkling2Line size={18} />
              Export As PDF
            </span>
          ),
          value: "pdf",
        },
        {
          label: (
            <span className="d-inline-flex align-items-center gap-2">
              <RiFileTextLine size={18} />
              Export As Word
            </span>
          ),
          value: "word",
        },
      ]}
      onSelect={handleExport}
      preserveTitle
      className="export-report-button"
      hoverTitle="Export Report"
    />
  );
};

export default ExportReportButton;
