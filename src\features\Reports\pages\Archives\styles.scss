@use "/src/styles/mixins/mixins.scss";

.report-archives-wrapper {
  padding: 30px;
  overflow: hidden;
  position: relative;
  height: calc(100vh - 195px);
  border-radius: 12px;
  box-shadow:
    0px 65px 47px 0px rgba(26, 26, 26, 0.04),
    0px 100px 80px 0px rgba(26, 26, 26, 0.05);

  @media only screen and (max-width: 991px) {
    height: auto;
  }

  @include mixins.slim-scrollbar;

  .custom-table {
    thead {
      th {
        min-width: 200px;

        @media only screen and (max-width: 991px) {
          min-width: 200px;

          &:nth-child(2) {
            min-width: 400px;
          }
        }

        &:last-child {
          text-align: center;
        }
      }
    }
  }

  .action-item-btn {
    width: 42px;
    height: 42px;
  }

  .status-pill {
    width: 100px;
    background-color: #60a799;

    &.active {
      background-color: #60a799;
    }

    &.canceled {
      background-color: red;
    }
  }

  .loader-icon {
    width: 25px;
    height: 25px;
  }
}
