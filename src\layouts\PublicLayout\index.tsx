import { useAuth0 } from "@auth0/auth0-react";
import { useCheckSubscription, useMyProfileMutation } from "api";
import { LoadingOverlay } from "components";
import { AUTH0_INFO, LOGIN_TYPE } from "globals";
import { useEffect, useState } from "react";
import { Outlet, useLocation } from "react-router-dom";
import { setEntityConfiguration } from "stores";
import useUserStore, {
  resetUserState,
  setSubscriptionInfo,
  setUserInfo,
} from "stores/user";
import { processHash } from "utils";
import { tokenRefreshService } from "services/tokenRefreshService";

const PublicLayout = () => {
  const { isAuthenticated, isLoading, getAccessTokenSilently } = useAuth0();
  const { token, loginType, refreshToken } = useUserStore((state) => state.userInfo);
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const { mutateAsync: myProfile } = useMyProfileMutation();
  const { mutateAsync: checkSubscription } = useCheckSubscription();

  const fetchUserProfile = async (token: string, loginType: string, refreshToken?: string) => {
    try {
      const [userResult, subscriptionResult]: any = await Promise.all([
        myProfile(token),
        checkSubscription(token),
      ]);
      if (userResult?.success) {
        // Parse token to get expiration time
        const parseJWT = (token: string) => {
          try {
            const base64Url = token.split('.')[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(
              atob(base64)
                .split('')
                .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
                .join('')
            );
            return JSON.parse(jsonPayload);
          } catch (error) {
            return {};
          }
        };

        const decoded = parseJWT(token);
        const tokenExpiresAt = decoded.exp ? decoded.exp * 1000 : null;

        setUserInfo({
          token,
          loginType,
          refreshToken,
          tokenExpiresAt,
          user: {
            ...userResult?.data,
            is_subscription: subscriptionResult?.data?.is_subscription,
          },
        });
        setSubscriptionInfo(subscriptionResult?.data?.subscription ?? {});
        if (userResult?.data?.redact_setting) {
          setEntityConfiguration({
            ...userResult?.data?.redact_setting,
            enable_privacy:
              userResult?.data?.redact_setting?.enable_privacy ?? true,
          });
        }
      }
    } catch (err: any) {
      resetUserState();
    } finally {
      setLoading(false);
    }
  };

  // Initialize token refresh service with Auth0 context
  useEffect(() => {
    if (isAuthenticated) {
      tokenRefreshService.setAuth0Context({
        isAuthenticated,
        isLoading,
        getAccessTokenSilently
      } as any);
    }
  }, [isAuthenticated, isLoading, getAccessTokenSilently]);

  // Initialize token refresh for current session
  useEffect(() => {
    if (token && loginType) {
      tokenRefreshService.initializeTokenRefresh(token, loginType, refreshToken || undefined);
    }

    // Cleanup on unmount
    return () => {
      tokenRefreshService.cleanup();
    };
  }, [token, loginType, refreshToken]);

  useEffect(() => {
    let auth0Token = null;
    (async () => {
      if (loginType === LOGIN_TYPE.SSO && !token) {
        try {
          setLoading(true);
          auth0Token = await getAccessTokenSilently({
            authorizationParams: {
              audience: AUTH0_INFO.AUTH0_AUDIENCE,
            },
          });
          if (isAuthenticated && !isLoading && auth0Token) {
            fetchUserProfile(auth0Token, LOGIN_TYPE.SSO);
          }
        } catch (err: any) {
          resetUserState();
        } finally {
          setLoading(false);
        }
      }
    })();
  }, [isAuthenticated, isLoading, loading, setLoading]);

  useEffect(() => {
    if (location.hash) {
      processHash(location.hash, setLoading, fetchUserProfile);
    }
  }, [location.hash, token, loginType]);

  if (loading) {
    return <LoadingOverlay show={true} />;
  }

  return <Outlet />;
};

export default PublicLayout;
