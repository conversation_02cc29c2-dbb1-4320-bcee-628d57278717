@use "/src/styles/mixins/mixins.scss";

.report-builder-section {
  .header {
    padding-bottom: 0.5rem;

    &-title {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      background: #f9f9f9;
      border: 1px solid #ccc;
      padding-right: 2.5rem;
      border-radius: 6px;

      .report-title-input {
        flex: 1;
        padding: 0.75rem 2.5rem 0.75rem 1rem;
        border: none;
        background: transparent;
        font-size: 1rem;
        outline: none;
      }

      .edit-title-btn-wrapper {
        position: absolute;
        right: 0.75rem;

        .action-btn {
          background: none;
          border: none;
          cursor: pointer;
          padding: 0;
          display: flex;
          align-items: center;
          height: 100%;

          svg {
            width: 1.2rem;
            height: 1.2rem;
          }
        }
      }
    }

    &-actions {
      .action-btn {
        background-color: #0d3149;
        border-color: #0d3149;
        color: white;
        width: 45px !important;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;

        svg {
          width: 20px;
          height: 20px;
        }
      }
    }

    .export-report-button {
      .dropdown-toggle {
        background-color: #0d3149;
        border-color: #0d3149;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        padding: 0.63rem;

        &::after {
          content: "";
          display: none;
        }
      }

      .dropdown-menu {
        z-index: 9999;
        min-width: 180px;
      }

      @media (max-width: 576px) {
        .dropdown-toggle {
          min-width: 85px;
        }
      }
    }
  }
}

.preview-section {
  .section-content {
    img {
      width: 100%;
    }

    p {
      margin-bottom: 8px;
    }
  }
}

.insert-btn {
  font-size: 18px;
  color: #0d3149;
  padding: 13px 20px;
}

.modal-body {
  padding: 40px !important;
}

.report-content {
  width: calc(100% - 285px) !important;
}
.action-btns {
  flex-wrap: wrap;
}

@media (max-width: 991px) {
  .report-builder-section .header {
    flex-direction: column;
  }

  .section-block {
    padding: 16px 0px !important;
  }

  .modal-body {
    padding: 20px;
  }
  .report-content {
    height: 100vh !important;
    overflow-y: auto !important;
    width: 100% !important;
  }
  .mobile-navigation-btn {
    border: 1px solid #0d3149;
    color: #0d3149;
    padding: 10px;
    font-size: 16px;
    font-weight: 600;
    background: transparent;
    border-radius: 6px;
    svg {
      width: 24px;
      height: 24px;
    }
  }
  .back-btn {
    font-size: 14px;
    background: transparent;
    border: none;
    padding: 0px;
    color: #0d3149;
    font-weight: 600;
  }
}

@media (max-width: 576px) {
  .enhanced-toolbar .toolbar-container .btn-group {
    flex-wrap: wrap;
  }

  .section-header {
    flex-direction: column;
    gap: 10px;
  }
}
