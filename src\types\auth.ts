export interface LoginInterface {
  email: string;
  password: string;
}

export interface SignupInterface {
  full_name: string;
  email: string;
  password: string;
  confirmPassword?: string;
  agreeTerms?: boolean;
}

export interface ForgotInterface {
  email: string;
}

export interface ResponseInteface {
  message: string;
  success: boolean;
  data: any;
}

export interface ResetPasswordPayloadInterface {
  newPassword: string;
  reset_password_token: string | null;
}

export interface ChangePasswordInterface {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}
