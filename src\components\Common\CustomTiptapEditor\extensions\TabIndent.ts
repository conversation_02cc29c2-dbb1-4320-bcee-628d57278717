import { Extension } from "@tiptap/core";

const INDENT = "    "; // or use "\t" for tab character

const TabIndent = Extension.create({
  name: "tabIndent",

  addKeyboardShortcuts() {
    return {
      Tab: () => {
        const { state, commands } = this.editor;
        const { $from } = state.selection;

        // Let list items use default tab behavior (they have built-in indentation)
        if ($from.parent.type.name === "listItem") {
          return false;
        }

        // Insert spaces at the current position
        commands.insertContent(INDENT);
        return true;
      },

      "Shift-Tab": () => {
        const { state, commands } = this.editor;
        const { from, to } = state.selection;

        // Look at text before the cursor to remove indentation
        const indentSize = INDENT.length;
        const textBefore = state.doc.textBetween(
          from - indentSize,
          from,
          "\0",
          "\0"
        );

        if (textBefore === INDENT) {
          commands.deleteRange({ from: from - indentSize, to: from });
        } else if (textBefore.endsWith("\t")) {
          commands.deleteRange({ from: from - 1, to: from });
        }

        return true;
      },
    };
  },
});

export default TabIndent;
