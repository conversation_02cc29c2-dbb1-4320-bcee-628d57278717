.bouncing-loader {
  display: flex;
  justify-content: start;
  margin: 0px;
  padding: 15px 0px;
}

.bouncing-loader > div {
  width: 10px;
  height: 10px;
  margin: 0px 6px;
  border-radius: 50%;
  background-color: #0d3149;
  opacity: 1;
  animation: bouncing-loader 0.6s infinite alternate;
}

.bouncing-loader > div:nth-child(2) {
  background-color: #ad986f;
}

@keyframes bouncing-loader {
  to {
    opacity: 0.1;
    transform: translateY(-3px);
  }
}

.bouncing-loader > div:nth-child(2) {
  animation-delay: 0.2s;
}

.bouncing-loader > div:nth-child(3) {
  animation-delay: 0.4s;
}
