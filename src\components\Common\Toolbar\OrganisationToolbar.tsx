import {
  Ri<PERSON>dd<PERSON>ox<PERSON>ill,
  RiArrowUpBoxFill,
  RiCameraLine,
  RiInformation2Fill,
} from "@remixicon/react";
import { useOrganisationTeamPlan, useSubscriptionTiers } from "api";
import ProfilePicture from "pages/Profile/MyProfile/ProfilePicture";
import { FC, useMemo, useState } from "react";
import { Button } from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import usePlanStore from "stores/plan";
import HoverTooltip from "../HoverTooltip";
import { PricingTiersModal, UpgradeTeamModal } from "../Modals";
import "./styles.scss";
import TableSearch from "./TableSearch";

interface OrganisationToolbarProps {
  title: string;
  description: string;
  icon: any;
  filters: any;
  setFilters: any;
  organisationInfo: any;
  isOfflineAccount: boolean;
  wordCountConfig: any;
}

const OrganisationToolbar: FC<OrganisationToolbarProps> = ({
  title,
  description,
  icon,
  filters,
  setFilters,
  organisationInfo,
  isOfflineAccount,
}) => {
  const ToolbarIcon = icon;
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [showUpgradeTeamModal, setShowUpgradeTeamModal] = useState(false);
  const navigate = useNavigate();

  const { data: organisationTeamPlan = {} } = useOrganisationTeamPlan(
    organisationInfo?.id,
  );

  const myPlan = usePlanStore((state: any) => state.myPlan);
  const { data: { tier: subscriptionTiers = [] } = {} } = useSubscriptionTiers(
    myPlan?.currentPlan?.subscription_id,
  );

  const handleUpgradeTier = () => {
    setShowUpgradeTeamModal(true);
  };

  const TIER_CONFIG = useMemo(() => {
    return {
      ...organisationTeamPlan,
      isUpgradeAvailable: true,
      isInviteAvailable:
        organisationTeamPlan?.team_count < organisationTeamPlan?.team_size,
    };
  }, [organisationTeamPlan?.team_count, organisationTeamPlan?.team_size]);

  const selectedTier = useMemo(() => {
    if (!subscriptionTiers?.length || !organisationTeamPlan?.team_size)
      return null;

    return subscriptionTiers.find(
      (tier: any) =>
        organisationTeamPlan.team_size >= tier.min_users &&
        organisationTeamPlan.team_size <= tier.max_users,
    );
  }, [subscriptionTiers, organisationTeamPlan?.team_size]);

  const handleInviteNew = () => {
    if (!TIER_CONFIG.isInviteAvailable) return;
    navigate(ROUTE_PATH.INVITE_NEW);
  };

  const featureFlags = {
    enableManageLicence: !isOfflineAccount,
    enablePricingTier: !isOfflineAccount,
  };

  return (
    <div>
      <div className="toolbar-wrapper d-flex flex-sm-row flex-column justify-content-sm-between justify-content-center align-items-stretch my-4">
        <div className="toolbar-wrapper-left">
          <div className="icon d-inline-block lh-1 align-middle position-relative">
            <ProfilePicture
              userPicture={organisationInfo?.logo}
              title={organisationInfo?.title || ""}
              name="logo"
              imageProps={{
                width: 60,
                height: 60,
              }}
              defaultProfileSize="medium"
            />
            <Link
              to={ROUTE_PATH.ORGANISATIONAL_PROFILE}
              className="camera-icon bg-blue rounded-circle p-1 d-inline-block position-absolute border-2 border border-white"
            >
              <RiCameraLine size={"15px"} color="#f9f9f9" />
            </Link>
          </div>

          <p className="title mb-0 ms-4 d-inline-block lh-1 align-middle">
            {organisationInfo?.title}
          </p>
        </div>
        <div className="toolbar-wrapper-right mt-lg-0 mt-2">
          {featureFlags.enablePricingTier ? (
            <>
              <span className="fw-bold">Pricing Tier:</span>{" "}
              {selectedTier && (
                <>
                  <span>
                    {selectedTier?.min_users} - {selectedTier?.max_users} users{" "}
                    <sup
                      onClick={() => setShowPricingModal(true)}
                      className="cursor-pointer"
                    >
                      <RiInformation2Fill size={15} />
                    </sup>
                  </span>
                  <div className="text-left w-100 fw-bold">
                    <span>
                      Active Licences:{" "}
                      <span className="fw-normal">
                        {TIER_CONFIG.team_count || 0} of{" "}
                        {TIER_CONFIG.team_size || 0}
                      </span>
                    </span>
                  </div>
                </>
              )}
            </>
          ) : (
            <div className="text-left w-100 fw-bold">
              <span>
                Active Licences:{" "}
                <span className="fw-normal">
                  {TIER_CONFIG.team_count || 0} of {TIER_CONFIG.team_size || 0}
                </span>
              </span>
            </div>
          )}
        </div>
      </div>
      <hr className="mt-0 mb-2 p-0 border-0 w-100 d-block" />
      <div className="toolbar-wrapper d-flex flex-sm-row flex-column justify-content-sm-between justify-content-center align-items-stretch my-4">
        <div className="toolbar-wrapper-left">
          <div className="icon d-inline-block lh-1 align-middle">
            <ToolbarIcon className="toolbar-icon object-fit-contain" />
          </div>

          <p className="title mb-0 ms-2 d-inline-block lh-1 align-middle">
            {title}
          </p>

          <p className="mb-0 mt-1 description">{description}</p>
        </div>
        <div className="toolbar-wrapper-right mt-lg-0 d-flex flex-md-row flex-column justify-content-xl-end align-items-lg-center align-items-stretch mt-xl-0 mt-3 gap-4">
          <TableSearch filters={filters} setFilters={setFilters} />
          {featureFlags.enableManageLicence && (
            <Button
              className="d-flex align-items-center justify-content-center lh-1 toolbar-btn-blue bg-blue border-blue text-center fw-bold fs-6 w-100 px-3"
              onClick={handleUpgradeTier}
              disabled={!TIER_CONFIG?.isUpgradeAvailable}
              style={{
                minWidth: "200px",
                display: "flex",
                gap: "10px",
                whiteSpace: "nowrap",
              }}
            >
              <span>Manage Licences</span>
              <RiArrowUpBoxFill size={22} className="align-middle" />
            </Button>
          )}

          <HoverTooltip
            title="You need to upgrade your team size to invite more users"
            customClass="fw-bold"
            isDisabled={TIER_CONFIG?.isInviteAvailable}
          >
            <Button
              className="d-flex align-items-center justify-content-center lh-1 toolbar-btn-blue bg-blue border-blue text-center fw-bold fs-6 w-100 px-3"
              style={{
                whiteSpace: "nowrap",
                minWidth: "160px",
                display: "flex",
                gap: "10px",
              }}
              onClick={handleInviteNew}
            >
              <span className="text-truncate">Invite New</span>
              <RiAddBoxFill size={22} className="align-middle" />
            </Button>
          </HoverTooltip>
        </div>
      </div>

      {showPricingModal && (
        <PricingTiersModal
          show={showPricingModal}
          onClose={() => setShowPricingModal(false)}
          tierConfig={TIER_CONFIG}
          subscriptionTiers={subscriptionTiers}
        />
      )}
      {showUpgradeTeamModal && (
        <UpgradeTeamModal
          show={showUpgradeTeamModal}
          onClose={() => setShowUpgradeTeamModal(false)}
          tierConfig={TIER_CONFIG}
        />
      )}
    </div>
  );
};

export default OrganisationToolbar;
