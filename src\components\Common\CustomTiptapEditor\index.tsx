import { EditorContent } from "@tiptap/react";
import { Editor } from "@tiptap/react";
import "./styles.scss";

interface CustomTiptapEditorProps {
  editor: Editor | null;
}

const CustomTiptapEditor = ({ editor }: CustomTiptapEditorProps) => {
  if (!editor) {
    return null;
  }

  return (
    <div className="custom-editor">
      <EditorContent editor={editor} className="editor-content" />
    </div>
  );
};

export default CustomTiptapEditor;
