import { RiCloseLine } from "@remixicon/react";
import { useApplyCoupon } from "api";
import { useFormik } from "formik";
import { CouponValidations } from "formSchema/schemaValidations";
import React, { useState } from "react";
import { But<PERSON>, Form, Modal, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";

interface AddCouponModalProps {
  show: boolean;
  onClose: () => void;
  onSelectPlan: (couponResult?: any) => void;
}

const AddCouponModal: React.FC<AddCouponModalProps> = ({
  show,
  onClose,
  onSelectPlan,
}) => {
  const [resultMessage, setResultMessage] = useState("");
  const [couponResult, setCouponResult] = useState<any>(null);

  const { mutateAsync: applyCoupon } = useApplyCoupon();

  const formik: any = useFormik({
    initialValues: {
      coupon_code: "",
    },
    validationSchema: CouponValidations,
    onSubmit: async (values) => {
      if (resultMessage) return;
      const result: any = await applyCoupon(values);
      if (result?.success) {
        toast.success(result?.message, { duration: 10000 });
        setResultMessage(result?.message);
        setCouponResult(result?.data?.coupon);
      }
    },
  });

  const renderError = (field: string) => {
    if (formik.touched[field] && formik.errors[field]) {
      return <span className="mb-2">{formik.errors[field]}</span>;
    }
  };

  const handleClose = () => {
    formik.resetForm();
    setResultMessage("");
    onClose();
  };

  const handleSkip = () => {
    handleClose();
    onSelectPlan();
  };
  const handleCouponInput = (value: string) => {
    const uppercasedValue = value.toUpperCase();
    formik.setFieldValue("coupon_code", uppercasedValue);
    setCouponResult(null);
    setResultMessage("");
  };

  const onChangeCoupon = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleCouponInput(e.target.value);
  };

  const onPasteCoupon = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData("text");
    handleCouponInput(pastedText);
  };

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={handleClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>
        <div
          className="auth-form d-flex justify-content-center align-items-stretch flex-column"
          style={{ gap: "20px" }}
        >
          <div className="d-flex flex-column" style={{ gap: "15px" }}>
            <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
              Partner Pricing
            </h1>

            <p className="mb-0 auth-form-description font-gray text-center">
              Please enter your unique partner code.
            </p>
          </div>

          <div className="website-form w-100">
            <Form
              className="d-flex flex-column gap-3"
              onSubmit={formik.handleSubmit}
            >
              <Form.Group className="m-0 p-0 position-relative w-100">
                <Form.Control
                  type="text"
                  placeholder="Enter coupon code"
                  {...formik.getFieldProps("coupon_code")}
                  onChange={onChangeCoupon}
                  onPaste={onPasteCoupon}
                />
                <div className="d-flex flex-column mt-2">
                  {renderError("coupon_code")}
                </div>
              </Form.Group>

              <div className="action-btns mt-3 d-flex flex-row gap-4">
                <Button
                  type="button"
                  className="submit-btn delete-btn w-100 bg-red border-brown text-uppercase font-light"
                  onClick={handleSkip}
                >
                  Skip
                </Button>
                {resultMessage ? (
                  <Button
                    className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                    onClick={() => onSelectPlan(couponResult)}
                  >
                    Pay Now
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                    disabled={formik.isSubmitting}
                  >
                    {formik.isSubmitting ? <Spinner /> : "Apply Coupon"}
                  </Button>
                )}
              </div>
            </Form>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default AddCouponModal;
