import { RiCloseLine } from "@remixicon/react";
import { useReportCheckComplianceMutation } from "features/Reports/api";
import { Badge, Button, Modal, Spinner } from "react-bootstrap";
import { useEffect, useState } from "react";
import "./styles.scss";

const ComplianceModal = ({
  show,
  onClose,
  payload,
}: {
  show: boolean;
  onClose?: () => void;
  payload: any;
}) => {
  const [complianceData, setComplianceData] = useState<any>(null);

  const {
    mutateAsync: checkCompliance,
    isPending: isLoading,
    isError,
  } = useReportCheckComplianceMutation();

  useEffect(() => {
    if (show && payload) {
      setComplianceData(null);
      checkCompliance(payload)
        .then((response: any) => {
          setComplianceData(response?.data || {});
        })
        .catch(() => {
          setComplianceData(null);
        });
    }
  }, [show, checkCompliance]); // Removed payload from dependencies to prevent re-triggering

  const handleClose = () => {
    setComplianceData(null);
    onClose?.();
  };

  return (
    <Modal show={show} size="lg" centered className="compliance-modal">
      <Modal.Header className="border-0 pb-0">
        <Modal.Title className="w-100 text-center">
          <h3 className="mb-0 fw-bold">
            Suitability Report <br />
            Compliance Checker
          </h3>
          <Badge
            bg="warning"
            className="position-absolute top-0 start-0 m-3 shadow text-dark"
          >
            Beta
          </Badge>
        </Modal.Title>
        <Button
          variant="link"
          className={`text-decoration-none position-absolute end-0 top-0 mt-2 me-2 ${
            isLoading ? "text-muted" : "text-dark"
          }`}
          onClick={isLoading ? undefined : handleClose}
          disabled={isLoading}
          style={{
            cursor: isLoading ? "not-allowed" : "pointer",
            opacity: isLoading ? 0.5 : 1,
          }}
        >
          <RiCloseLine size={24} />
        </Button>
      </Modal.Header>

      <Modal.Body>
        <div className="compliance-wrapper">
          {isLoading && (
            <div className="d-flex flex-column align-items-center justify-content-center py-5">
              <Spinner animation="border" role="status" />
              <div className="mt-3 text-muted">
                Checking compliance, please wait...
              </div>
            </div>
          )}

          {isError && (
            <div className="d-flex flex-column align-items-center justify-content-center py-5">
              <div className="mt-3 text-muted">
                Error checking compliance. Please try again later.
              </div>
            </div>
          )}

          {!isLoading &&
            !isError &&
            complianceData &&
            Object.keys(complianceData)?.length > 0 && (
              <>
                <div className="score-bar">
                  <div className="bar">
                    <div
                      className="indicator"
                      style={{
                        left: `${complianceData?.overall_score || 0}%`,
                        top: "20px",
                      }}
                    />
                  </div>
                  <div className="score-percent">
                    {complianceData?.overall_score || 0}%
                  </div>
                  <div className="score-label">
                    {complianceData?.overall_result || "No result"}
                  </div>
                </div>

                <div className="mt-4">
                  <table className="compliance-table">
                    <thead>
                      <tr>
                        <th>Compliance Area</th>
                        <th>Score</th>
                        <th>Status</th>
                        <th>Comments</th>
                      </tr>
                    </thead>
                    <tbody>
                      {complianceData?.compliance_summary?.map(
                        (item: any, idx: number) => (
                          <tr key={idx}>
                            <td>{item.category}</td>
                            <td>{item.score}</td>
                            <td>{item.status}</td>
                            <td>{item.comment}</td>
                          </tr>
                        )
                      )}
                    </tbody>
                  </table>
                </div>
              </>
            )}

          {!isLoading &&
            !isError &&
            complianceData &&
            Object.keys(complianceData)?.length === 0 && (
              <div className="d-flex flex-column align-items-center justify-content-center py-5">
                <div className="mt-3 text-muted">
                  No compliance data available.
                </div>
              </div>
            )}
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default ComplianceModal;
