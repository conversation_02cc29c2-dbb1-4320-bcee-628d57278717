import React from "react";

interface AuthHeaderDataProps {
  heading: string;
  description: string;
  customStyling?: any;
}

const AuthHeaderData: React.FC<AuthHeaderDataProps> = ({
  heading,
  description,
  customStyling,
}) => {
  return (
    <div className="d-flex flex-column" style={{ gap: "23px" }}>
      <h1
        className="auth-form-heading text-uppercase mb-0 text-center lh-1"
        style={{ ...customStyling?.heading }}
      >
        {heading}
      </h1>
      <p
        className="mb-0 auth-form-description font-gray text-center lh-sm"
        dangerouslySetInnerHTML={{ __html: description }}
      ></p>
    </div>
  );
};

export default AuthHeaderData;
