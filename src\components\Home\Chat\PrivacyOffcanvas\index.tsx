import { RiInformation2Fill } from "@remixicon/react";
import { HoverTooltip } from "components/Common";
import { usePrivacyFilterConfig } from "hooks/usePrivacyFilterConfig";
import { Accordion, Button, Offcanvas } from "react-bootstrap";
import { DEFAULT_PRESET, setOrganisation } from "stores";
import EntityItem from "./EntityItem";
import PresetItem from "./PresetItem";
import "./styles.scss";
import TogglePrivateAI from "./TogglePrivateAI";
import { useGetOrganisation } from "api";
import { useEffect } from "react";

const ENTITY_TOOLTIP_INFO = {
  title:
    "Control what leaves the system, whilst letting the full power of AI in.",
  description: `<ul>
                <li>Select which entities you would like to redact. We will identify them, redact them and synthesise them before sending to the AI model. This keeps your data safe and secure.</li>
                <li>Choose from our presets.</li>
                <li>Create and save your own custom preset.</li>
              </ul>`,
};

interface PrivacyOffcanvasProps {
  show: boolean;
  handleClose: () => void;
}

const PrivacyOffcanvas: React.FC<PrivacyOffcanvasProps> = ({
  show,
  handleClose,
}) => {
  const {
    entities,
    localConfiguration,
    setLocalConfiguration,
    entityConfiguration,
    activePreset,
    setActivePreset,
    ACTION_CONFIG,
    isCustomAvailable,
    handleSave,
  } = usePrivacyFilterConfig();
  const { data: { organization: orgInfo = {} } = {} } = useGetOrganisation({
    staleTime: 0,
  });

  useEffect(() => {
    if (orgInfo?.id) {
      setOrganisation(orgInfo);
    }
  }, [orgInfo?.id]);

  return (
    <Offcanvas
      show={show}
      onHide={() => {
        handleClose();
        setLocalConfiguration(entityConfiguration);
        setActivePreset(DEFAULT_PRESET);
      }}
      placement="end"
      scroll={true}
      className="privacy-offcanvas border-0"
    >
      <Offcanvas.Header closeButton className="privacy-offcanvas-header">
        <Offcanvas.Title>
          <p className="mb-0 privacy-offcanvas-title">
            Privacy Filters
            <HoverTooltip
              customClass="tooltip-custom-width"
              title={ENTITY_TOOLTIP_INFO.title}
              description={ENTITY_TOOLTIP_INFO.description}
            >
              <span>
                <RiInformation2Fill className="mx-2" />
              </span>
            </HoverTooltip>
          </p>
        </Offcanvas.Title>
      </Offcanvas.Header>

      <Offcanvas.Body className="privacy-offcanvas-body pt-0">
        <hr className="mt-0" />
        {ACTION_CONFIG.show_ai_toggler && (
          <TogglePrivateAI
            setLocalConfiguration={setLocalConfiguration}
            localConfiguration={localConfiguration}
          />
        )}
        <Accordion>
          {ACTION_CONFIG.show_preset && (
            <PresetItem
              setLocalConfiguration={setLocalConfiguration}
              localConfiguration={localConfiguration}
              activePreset={activePreset}
              setActivePreset={setActivePreset}
              isCustomAvailable={isCustomAvailable}
            />
          )}
          {entities?.length > 0 &&
            entities.map((entityItem: any, idx: any) => {
              return (
                <EntityItem
                  entityItem={entityItem}
                  entityItemIdx={idx}
                  key={idx}
                  setLocalConfiguration={setLocalConfiguration}
                  localConfiguration={localConfiguration}
                  activePreset={activePreset}
                  setActivePreset={setActivePreset}
                  isGlobalDisabled={ACTION_CONFIG.all_entities_disabled}
                  isBasicDisabled={ACTION_CONFIG.basic_filters_disabled}
                />
              );
            })}
        </Accordion>

        {ACTION_CONFIG.show_save && (
          <div className="action-btns d-flex flex-column mt-3 pt-1">
            <Button
              className="submit-btn w-100 bg-blue border-blue text-uppercase font-light"
              onClick={handleSave}
            >
              Save
            </Button>
          </div>
        )}
      </Offcanvas.Body>
    </Offcanvas>
  );
};

export default PrivacyOffcanvas;
