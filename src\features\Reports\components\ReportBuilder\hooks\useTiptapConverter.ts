import { generateHTML, generateJ<PERSON><PERSON> } from "@tiptap/html";
// TEMPORARY: Imports for JSON conversion (will be removed later)
import Highlight from "@tiptap/extension-highlight";
import Image from "@tiptap/extension-image";
import Table from "@tiptap/extension-table";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import StarterKit from "@tiptap/starter-kit";
import ContentLock from "components/Common/CustomTiptapEditor/extensions/ContentLock";
import GraphPlaceholder from "components/Common/CustomTiptapEditor/extensions/GraphPlaceholder";
import { ImageResize } from "components/Common/CustomTiptapEditor/extensions/ImageResize";
import AiInstruction from "components/Common/CustomTiptapEditor/extensions/AiInstruction";
import AiInstructionLabel from "components/Common/CustomTiptapEditor/extensions/AiInstructionLabel";
type ContentType = "html" | "json";

interface TiptapConverter {
  toJSON: (html: string) => any;
  toHTML: (json: any) => string;
  convertSections: (sections: any[], direction: ContentType) => any[];
}

// TEMPORARY: Extensions for JSON conversion (will be removed later)
const extensions = [
  StarterKit,
  Highlight,
  Underline,
  Table.configure({
    resizable: true,
  }),
  TableRow,
  TableHeader,
  TableCell,
  GraphPlaceholder,
  ContentLock,
  AiInstruction,
  AiInstructionLabel,
  TextAlign.configure({
    types: ["heading", "paragraph"],
  }),
  Image,
  ImageResize,
];

export const useTiptapConverter = (): TiptapConverter => {
  const toJSON = (html: string) => {
    try {
      return generateJSON(html, extensions);
    } catch (error) {
      console.error("Error converting HTML to JSON:", error);
      return null;
    }
  };

  const toHTML = (json: any) => {
    try {
      return generateHTML(json, extensions);
    } catch (error) {
      console.error("Error converting JSON to HTML:", error);
      return "";
    }
  };

  const convertSections = (sections: any[], direction: ContentType): any[] => {
    if (sections?.length === 0) {
      console.error("Invalid sections array:", sections);
      return [];
    }

    return sections.map((section) => {
      if (section.content && typeof section.content === "object") {
        try {
          if (direction === "json" && section.content.type === "html") {
            const jsonContent = toJSON(section.content.content);
            return {
              ...section,
              content: jsonContent,
            };
          } else if (direction === "html" && section.content.type !== "html") {
            const htmlContent = toHTML(section.content);
            return {
              ...section,
              content: {
                type: "html",
                content: htmlContent,
              },
            };
          }
        } catch (err) {
          console.error(
            `Error converting section content (${direction}):`,
            err
          );
        }
      }
      return section;
    });
  };

  return { toJSON, toHTML, convertSections };
};
