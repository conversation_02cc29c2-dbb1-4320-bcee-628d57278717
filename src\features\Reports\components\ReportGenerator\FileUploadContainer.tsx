import { RiAddLine, RiCloseLine } from "@remixicon/react";
import { DOC_TYPE_INFO } from "globals";
import { useCallback, useState } from "react";
import { Button, Image } from "react-bootstrap";
import toast from "react-hot-toast";
import DictateModal from "components/Common/Modals/DictateModal";

const MAX_FILES = 5;

const ALLOWED_MIME_TYPES = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "application/vnd.ms-powerpoint",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  "text/plain",
];

interface FileUploadContainerProps {
  onChange?: (files: File[]) => void;
}

const FileUploadContainer = ({ onChange }: FileUploadContainerProps) => {
  const [files, setFiles] = useState<File[]>([]);
  const [showDictate, setShowDictate] = useState(false);

  const isAllowedFileType = (file: File) => {
    return ALLOWED_MIME_TYPES.some(
      (type) => file.type.startsWith(type) || file.type === type
    );
  };

  const handleNewFiles = (newFiles: FileList | File[]) => {
    const filesArray = Array.from(newFiles);

    const filteredNewFiles = filesArray.filter((file) => {
      if (!isAllowedFileType(file)) {
        toast.error(`File "${file.name}" is not an allowed type.`);
        return false;
      }
      return true;
    });

    const remainingSlots = MAX_FILES - files.length;
    if (filteredNewFiles.length > remainingSlots) {
      toast.error(`You can only upload up to ${MAX_FILES} files.`);
      return;
    }

    const updatedFiles = [...files, ...filteredNewFiles];
    setFiles(updatedFiles);
    onChange?.(updatedFiles);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      handleNewFiles(event.target.files);
    }
  };

  const handleRemoveFile = (fileToRemove: File) => {
    const updatedFiles = files.filter((file) => file !== fileToRemove);
    setFiles(updatedFiles);
    onChange?.(updatedFiles);
  };

  const isImageFile = (file: File) => file.type.startsWith("image/");

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      if (e.dataTransfer.files) {
        handleNewFiles(e.dataTransfer.files);
      }
    },
    [files]
  );

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const renderFilePreviews = () =>
    files.map((file, index) => (
      <div key={index} className="file-preview-wrapper">
        <div className="file-image-wrapper position-relative">
          {isImageFile(file) ? (
            <Image
              src={URL.createObjectURL(file)}
              thumbnail
              className="file-thumbnail"
            />
          ) : (
            <div
              className="file-placeholder d-flex justify-content-center align-items-center"
              title={file.name}
            >
              <Image
                src={DOC_TYPE_INFO?.[file?.type]?.icon}
                className="object-fit-contain"
                alt={file.name}
                width={40}
                height={40}
              />
            </div>
          )}
          <Button
            variant=""
            size="sm"
            className="bg-brown border-brown text-white position-absolute top-0 end-0 rounded"
            onClick={() => handleRemoveFile(file)}
          >
            <RiCloseLine size={16} />
          </Button>
        </div>
      </div>
    ));

  const handleDictateFinish = (text: string) => {
    setShowDictate(false);
    const trimmed = (text || "").trim();
    if (!trimmed) return;
    try {
      const blob = new Blob([trimmed], { type: "text/plain" });
      const dateStr = new Date()
        .toISOString()
        .replace(/[:.]/g, "-")
        .slice(0, 19);
      const file = new File([blob], `dictation-${dateStr}.txt`, {
        type: "text/plain",
      });
      handleNewFiles([file]);
    } catch (e) {
      toast.error("Failed to attach dictation text");
    }
  };

  return (
    <>
      <div
        className={`file-upload-container d-flex flex-wrap gap-3 p-3 border border-dashed rounded ${
          files.length === 0 ? "align-items-center justify-content-center" : ""
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
      >
        {renderFilePreviews()}

        {files.length < MAX_FILES && (
          <>
            <label htmlFor="fileInput" className="upload-label text-center">
              <div className="icon-wrapper position-relative">
                <RiAddLine size={24} />
              </div>
              <div className="upload-text">Upload Files</div>
            </label>
            <input
              type="file"
              id="fileInput"
              onChange={handleFileChange}
              className="d-none"
              multiple
              accept={ALLOWED_MIME_TYPES.join(",")}
            />

            <button
              type="button"
              className="upload-label text-center bg-transparent"
              onClick={() => setShowDictate(true)}
            >
              <div className="icon-wrapper position-relative">
                <RiAddLine size={24} />
              </div>
              <div className="upload-text">Dictate</div>
            </button>
          </>
        )}
      </div>

      <DictateModal
        show={showDictate}
        onClose={() => setShowDictate(false)}
        onFinish={handleDictateFinish}
      />
    </>
  );
};

export default FileUploadContainer;
