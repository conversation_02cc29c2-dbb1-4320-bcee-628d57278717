import { Mark, mergeAttributes } from "@tiptap/core";

export interface AiInstructionOptions {
  HTMLAttributes: Record<string, any>;
}

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    aiInstruction: {
      /** Set AI instruction mark */
      setAiInstruction: () => ReturnType;
      /** Toggle AI instruction mark */
      toggleAiInstruction: () => ReturnType;
      /** Unset AI instruction mark */
      unsetAiInstruction: () => ReturnType;
    };
  }
}

export const AiInstruction = Mark.create<AiInstructionOptions>({
  name: "aiInstruction",

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  addAttributes() {
    return {
      instruction: {
        default: true,
        parseHTML: (element) => element.getAttribute("data-ai-instruction") === "true",
        renderHTML: (attributes) => {
          if (!attributes.instruction) {
            return {};
          }
          return {
            "data-ai-instruction": attributes.instruction,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-ai-instruction="true"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "span",
      mergeAttributes(
        {
          "data-ai-instruction": "true",
          class: "ai-instruction",
        },
        this.options.HTMLAttributes,
        HTMLAttributes,
      ),
      0,
    ];
  },

  addCommands() {
    return {
      setAiInstruction:
        () =>
        ({ commands }) => {
          return commands.setMark(this.name, { instruction: true });
        },
      toggleAiInstruction:
        () =>
        ({ commands }) => {
          return commands.toggleMark(this.name, { instruction: true });
        },
      unsetAiInstruction:
        () =>
        ({ commands }) => {
          return commands.unsetMark(this.name);
        },
    };
  },

  addKeyboardShortcuts() {
    return {
      "Mod-Shift-i": () => this.editor.commands.toggleAiInstruction(),
    };
  },
});

export default AiInstruction;

