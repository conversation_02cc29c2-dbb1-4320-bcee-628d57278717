import { useEffect, useRef } from "react";
import useReportStore from "features/Reports/store/report";

interface UseAutoSaveOptions {
  intervalSeconds?: number;
  onAutoSave: (args?: any) => void;
  autoSaveArgs?: any;
}

const useAutoSave = ({
  intervalSeconds = 30,
  onAutoSave,
  autoSaveArgs = { saveSilently: true },
}: UseAutoSaveOptions) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      if (useReportStore.getState().isDirty) {
        onAutoSave(autoSaveArgs);
        useReportStore.getState().setIsDirty(false);
      }
    }, intervalSeconds * 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [intervalSeconds, onAutoSave, autoSaveArgs]);
};

export default useAutoSave;
