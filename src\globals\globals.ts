import { ROUTE_PATH } from "routes";
import { SuccessPageContent } from "types";
import { IMAGE_PATH } from "./imagePath";
import { RiUserAddLine, RiUserLine } from "@remixicon/react";
export * from "./endpoints";
export * from "./imagePath";

export const AUTH0_INFO = {
  AUTH0_AUDIENCE: `https://${import.meta.env.VITE_AUTH0_DOMAIN}/api/v2/`,
  AUTH0_CLIENT_ID: import.meta.env.VITE_AUTH0_CLIENT_ID,
  AUTH0_DOMAIN: import.meta.env.VITE_AUTH0_DOMAIN,
  AUTH0_LOGIN_REDIRECT_URI: import.meta.env.VITE_AUTH0_LOGIN_REDIRECT_URI,
  AUTH0_LOGIN_RESPONSE_TYPE: import.meta.env.VITE_AUTH0_LOGIN_RESPONSE_TYPE,
  AUTH0_REALM: import.meta.env.VITE_AUTH0_REALM,
  AUTH0_SCOPE: import.meta.env.VITE_AUTH0_SCOPE,
};

export const CHAT_UPLOAD_MIME_TYPES = {
  CSV: "text/csv",
  // DOC: "application/msword",
  DOCX: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  PDF: "application/pdf",
  TXT: "text/plain",
  XLS: "application/vnd.ms-excel",
  XLSX: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  // PPT: "application/vnd.ms-powerpoint",
  PPTX: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  PNG: "image/png",
  JPEG: "image/jpeg",
};

export const DOC_TYPE_INFO: any = {
  [CHAT_UPLOAD_MIME_TYPES.CSV]: {
    icon: IMAGE_PATH.excelFileIcon,
    type: "CSV",
  },
  // [CHAT_UPLOAD_MIME_TYPES.DOC]: {
  //   icon: IMAGE_PATH.wordFileIcon,
  //   type: "DOC",
  // },
  [CHAT_UPLOAD_MIME_TYPES.DOCX]: {
    icon: IMAGE_PATH.wordFileIcon,
    type: "DOCX",
  },
  [CHAT_UPLOAD_MIME_TYPES.PDF]: {
    icon: IMAGE_PATH.pdfFileIcon,
    type: "PDF",
  },
  [CHAT_UPLOAD_MIME_TYPES.TXT]: {
    icon: IMAGE_PATH.colorInfoDoc,
    type: "TXT",
  },
  [CHAT_UPLOAD_MIME_TYPES.XLS]: {
    icon: IMAGE_PATH.excelFileIcon,
    type: "XLS",
  },
  [CHAT_UPLOAD_MIME_TYPES.XLSX]: {
    icon: IMAGE_PATH.excelFileIcon,
    type: "XLSX",
  },
  // [CHAT_UPLOAD_MIME_TYPES.PPT]: {
  //   icon: IMAGE_PATH.pptIcon,
  //   type: "PPT",
  // },
  [CHAT_UPLOAD_MIME_TYPES.PPTX]: {
    icon: IMAGE_PATH.pptIcon,
    type: "PPTX",
  },
};

export const LOGIN_TYPE = {
  EMBEDDED: "embedded",
  SSO: "sso",
};

export const SETTINGS = {
  APP_NAME: "Chat. Redact",
  CURRENCY_INFO: {
    USD: {
      symbol: "$",
      stripeConversion: function (cents: number | undefined) {
        if (!cents) return `${this.symbol}0`;
        return `${this.symbol}${(cents / 100).toFixed(2)}`;
      },
    },
    GBP: {
      symbol: "£",
      stripeConversion: function (pence: number | undefined) {
        if (!pence) return `${this.symbol}0`;
        return `${this.symbol}${(pence / 100).toFixed(2)}`;
      },
    },
  },
  REFERRAL_REWARD_AMOUNT: 75,
  REFERRAL_REWARD_LINK: "http://www.chatredact.ai/referral",
};

export const SUCCESS_PAGE_CONTENT: SuccessPageContent = {
  LOADING: {
    description:
      "Please do not refresh or click the back button while we confirm your payment.",
    heading: "Processing your payment...",
    imagePath: IMAGE_PATH.redErrorIcon,
    subheading: "Almost there!",
    useIcon: false,
  },
  PAYMENT_FAILED: {
    btnText: "Please try again!",
    description: `Unfortunately, your payment could not be processed. Please check your payment details or try again later.`,
    heading: `Payment failed`,
    imagePath: IMAGE_PATH.redErrorIcon,
    redirectURL: ROUTE_PATH.PAYMENT,
    subheading: "",
    useIcon: false,
  },
  PAYMENT_SUCCESS: {
    description: `Your payment was successfully processed. You can now enjoy the benefits of your subscription.`,
    heading: `Payment Successful`,
    imagePath: IMAGE_PATH.redErrorIcon,
    subheading: `Thank You`,
    useIcon: true,
  },
  RESET_PASSWORD: {
    description: `You're all set! Login with your new password.`,
    heading: `Password Changed`,
    imagePath: IMAGE_PATH.redErrorIcon,
    subheading: `Successfully`,
    useIcon: true,
  },
  VERIFY_ACCOUNT: {
    description: "You have successfully verified the account.",
    heading: "Account Verified",
    imagePath: IMAGE_PATH.redErrorIcon,
    subheading: "",
    useIcon: true,
  },
  FULL_DISCOUNT: {
    description:
      "Your payment was successfully processed. You can now enjoy the benefits of your subscription. For the auto-renewal please add your card by clicking the button below.",
    heading: "Payment Successful",
    imagePath: IMAGE_PATH.redErrorIcon,
    redirectURL: ROUTE_PATH.PAYMENT_CARDS,
    btnText: "Add Card",
    subheading: "",
    useIcon: true,
  },
  PLEASE_WAIT: {
    description: "",
    heading: "Please wait,",
    imagePath: IMAGE_PATH.redErrorIcon,
    subheading: "Almost there...",
    useIcon: false,
    hideBtn: true,
  },
};

export const paymentCardsConfig: any = {
  amex: IMAGE_PATH.amexCard,
  unionpay: IMAGE_PATH.unionPayCard,
  jcb: IMAGE_PATH.jcbCard,
  diners: IMAGE_PATH.dinersCard,
  discover: IMAGE_PATH.discoverCard,
  mastercard: IMAGE_PATH.masterCard,
  visa: IMAGE_PATH.visaCard,
};

export enum OrgUserRole {
  USER = "user",
  ADMIN = "admin",
  INDIVIDUAL = "individual",
}

export enum OrgUserStatus {
  PENDING = "pending",
  ACTIVE = "active",
  CANCEL = "cancel",
  BLOCK = "blocked",
}

export const USER_ROLE: any = [
  { label: "Admin", value: OrgUserRole.ADMIN, icon: RiUserAddLine },
  { label: "User", value: OrgUserRole.USER, icon: RiUserLine },
];

export const STATUS_LABEL: any = {
  [OrgUserStatus.PENDING]: "Pending",
  [OrgUserStatus.ACTIVE]: "Active",
  [OrgUserStatus.CANCEL]: "Cancelled",
  [OrgUserStatus.BLOCK]: "Blocked",
};

export const FILE_TYPE_INFO: any = {
  "csv": {
    icon: IMAGE_PATH.excelFileIcon,
    type: "CSV",
  },
  "docx": {
    icon: IMAGE_PATH.wordFileIcon,
    type: "DOCX",
  },
  "pdf": {
    icon: IMAGE_PATH.pdfFileIcon,
    type: "PDF",
  },
  "txt": {
    icon: IMAGE_PATH.colorInfoDoc,
    type: "TXT",
  },
  "xls": {
    icon: IMAGE_PATH.excelFileIcon,
    type: "XLS",
  },
  "xlsx": {
    icon: IMAGE_PATH.excelFileIcon,
    type: "XLSX",
  },
  "pptx": {
    icon: IMAGE_PATH.pptIcon,
    type: "PPTX",
  },
};