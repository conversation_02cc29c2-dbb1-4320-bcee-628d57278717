import { StaticPage } from "pages";
import { Route } from "react-router-dom";
import { ROUTE_PATH } from "routes";

const PolicyRoutes = () => {
  const policyPages = [
    {
      path: ROUTE_PATH.PRIVACY,
      title: "Privacy Policy",
      id: "************************************",
    },
    {
      path: ROUTE_PATH.TERMS,
      title: "TERMS OF USE",
      id: "d7cec228-caed-4177-bc13-91331e9074c9",
    },
    {
      path: ROUTE_PATH.EULA,
      title: "EULA",
      id: "ff0d0b21-bb36-4ef6-9211-63b6307da0ed",
    },
  ];
  return policyPages.map(({ path, title, id }) => (
    <Route
      key={path}
      path={path}
      element={<StaticPage title={title} policyId={id} />}
    />
  ));
};

export default PolicyRoutes;
