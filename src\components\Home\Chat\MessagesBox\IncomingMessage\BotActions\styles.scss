.chat-action-buttons {
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.12);
  gap: 10px;
  padding: 10px 10px;
  border-radius: 8px;
  background-color: #f9f9f9;
  margin-top: 5px;

  svg {
    fill: #0d3149;
  }

  .export-document {
    &.custom-dropdown {
      .dropdown {
        .dropdown-toggle {
          width: 100%;
          background: transparent;
          border: none;
          padding: 0;
          gap: 0;
          height: auto;
        }

        .dropdown-menu {
          min-width: fit-content;
        }
      }
    }
  }
}
