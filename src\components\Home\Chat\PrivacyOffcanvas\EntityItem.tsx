import { useEntityItemActions } from "hooks";
import { useMemo } from "react";
import { Accordion, Form } from "react-bootstrap";

const EntityItem = ({
  entityItem,
  entityItemIdx,
  setLocalConfiguration,
  localConfiguration,
  activePreset,
  isGlobalDisabled,
  isBasicDisabled,
}: any) => {
  const {
    availableEntities,
    checkCategorySelected,
    comingSoonEntities,
    expanded,
    isAllEntitiesComingSoon,
    isFieldDisabled,
    handleToggle,
    onChangeEntity,
    onChangeCategory,
    privateEntities,
  } = useEntityItemActions({
    activePreset,
    entityItem,
    localConfiguration,
    setLocalConfiguration,
  });

  const disableBasicAccordion = useMemo(
    () =>
      isBasicDisabled &&
      (availableEntities.length === 0 ||
        availableEntities.some(
          (entity: any) => entity.preset_type === "basic",
        )),
    [isBasicDisabled, availableEntities],
  );

  return (
    <Accordion.Item
      eventKey={`${entityItemIdx}`}
      className="bg-transparent coming-soon-features"
    >
      <Accordion.Header onClick={() => handleToggle(`${entityItemIdx}`)}>
        <Form onClick={(e) => e.stopPropagation()}>
          <Form.Check
            type="switch"
            className={`${expanded ? "expanded" : ""} p-0`}
            checked={checkCategorySelected()}
            onChange={(evt) => onChangeCategory(evt, entityItem)}
            disabled={
              isFieldDisabled ||
              isAllEntitiesComingSoon ||
              isGlobalDisabled ||
              disableBasicAccordion
            }
          />
        </Form>
        <p className="mb-0 option-title">{entityItem?.title}</p>

        {isAllEntitiesComingSoon && (
          <p className="mb-0 coming-soon-tag text-center d-flex justify-content-center align-items-center text-capitalize position-absolute">
            Coming Soon
          </p>
        )}
      </Accordion.Header>

      <Accordion.Body
        className="d-flex flex-column justify-content-start align-items-stretch"
        style={{ gap: "8px" }}
      >
        <div className="option-check-list d-flex flex-column">
          {availableEntities?.length > 0 &&
            availableEntities?.map((entity: any, idx: number) => {
              return (
                <Form key={`${entity}-${idx}`}>
                  <Form.Check
                    type="switch"
                    label={entity?.label}
                    value={entity?.value}
                    onChange={onChangeEntity}
                    checked={privateEntities?.includes(entity?.value)}
                    disabled={
                      isFieldDisabled ||
                      isGlobalDisabled ||
                      (isBasicDisabled && entity?.preset_type === "basic")
                    }
                  />
                </Form>
              );
            })}
        </div>

        {comingSoonEntities?.length > 0 && (
          <div
            className={`option-check-list d-flex flex-column ${isAllEntitiesComingSoon ? "" : "coming-soon-group"} position-relative`}
          >
            {!isAllEntitiesComingSoon && (
              <p className="mb-0 coming-soon-tag text-center d-flex justify-content-center align-items-center text-capitalize position-absolute">
                Coming Soon
              </p>
            )}
            {comingSoonEntities?.length > 0 &&
              comingSoonEntities?.map((entity: any, idx: number) => {
                return (
                  <Form key={`${entity}-${idx}`}>
                    <Form.Check
                      type="switch"
                      label={entity?.label}
                      value={entity?.value}
                      checked={false}
                      disabled
                    />
                  </Form>
                );
              })}
          </div>
        )}
      </Accordion.Body>
    </Accordion.Item>
  );
};

export default EntityItem;
