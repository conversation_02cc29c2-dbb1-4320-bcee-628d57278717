import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { useLocation } from "react-router-dom";

const useCheckReportSection = () => {
    const location = useLocation();

    const isGenerateSection = location.pathname.includes(
        REPORTS_ROUTE_PATH.GENERATE_REPORT
    );
    const isBuildSection = location.pathname.includes(
        REPORTS_ROUTE_PATH.BUILD_REPORTS
    );

    return { isGenerateSection, isBuildSection }
}

export default useCheckReportSection;