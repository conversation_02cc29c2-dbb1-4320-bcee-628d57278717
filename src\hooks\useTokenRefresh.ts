import { useAuth0 } from "@auth0/auth0-react";
import { useEffect, useCallback } from "react";
import { LOGIN_TYPE } from "globals";
import useUserStore from "stores/user";
import { tokenRefreshService } from "services/tokenRefreshService";

export const useTokenRefresh = () => {
  const { isAuthenticated, getAccessTokenSilently } = useAuth0();
  const { token, loginType, refreshToken, tokenExpiresAt } = useUserStore(
    (state) => state.userInfo
  );

  // Initialize token refresh service with Auth0 context
  useEffect(() => {
    if (isAuthenticated && getAccessTokenSilently) {
      tokenRefreshService.setAuth0Context({
        isAuthenticated,
        getAccessTokenSilently,
      } as any);
    }
  }, [isAuthenticated, getAccessTokenSilently]);

  // Initialize automatic token refresh
  useEffect(() => {
    if (token && loginType) {
      tokenRefreshService.initializeTokenRefresh(
        token,
        loginType,
        refreshToken || undefined
      );
    }

    return () => {
      tokenRefreshService.cleanup();
    };
  }, [token, loginType, refreshToken]);

  // Manual token refresh function
  const performTokenRefresh = useCallback(async () => {
    if (!loginType) {
      throw new Error("No login type available");
    }

    const result = await tokenRefreshService.refreshToken(
      loginType,
      refreshToken || undefined
    );

    if (!result.success) {
      throw new Error(result.error || "Token refresh failed");
    }

    return result.token;
  }, [loginType, refreshToken]);

  // Check if token is expired or will expire soon
  const isTokenExpired = useCallback(
    (bufferMinutes: number = 5) => {
      if (!token) return true;
      return tokenRefreshService.isTokenExpired(token, bufferMinutes);
    },
    [token]
  );

  // Get token expiration info
  const getTokenInfo = useCallback(() => {
    if (!token) return null;

    return {
      token,
      expiresAt: tokenExpiresAt,
      isExpired: isTokenExpired(0),
      willExpireSoon: isTokenExpired(5),
    };
  }, [token, tokenExpiresAt, isTokenExpired]);

  return {
    refreshToken: performTokenRefresh,
    isTokenExpired,
    getTokenInfo,
    isAuthenticated: !!token,
    loginType,
  };
};

export default useTokenRefresh;
