@use "/src/styles/mixins/mixins.scss";

.modal {
  &-body {
    @media only screen and (max-width: 991px) {
      .auth-form {
        padding: 50px 0px;
      }
    }

    @media only screen and (max-width: 767px) {
      .auth-form {
        padding: 20px 0px;
      }
    }

    .error-icon {
      width: 150px;
      height: 150px;
    }

    .dropdown {
      .dropdown-toggle {
        height: 56px;
        padding: 0px 20px 0px 20px;
        border-radius: 50px;
        background: rgba(173, 152, 111, 0.1);
        font-size: 18px;
        border: 3px solid rgba(173, 152, 111, 0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
        color: #0d3149;

        &:focus,
        &:active {
          box-shadow: none;
          outline: none;
        }

        &::after {
          transition: transform 0.2s ease-in-out;
        }

        &.show::after {
          transform: rotate(-180deg);
          transition: transform 0.2s ease-in-out;
        }
      }

      .dropdown-menu {
        border-radius: 10px;
        background: #ffffff;
        border: none;
        padding: 10px 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

        .dropdown-item {
          font-size: 16px;
          padding: 10px 20px;
          color: #0d3149;
          border-radius: 8px;

          &:hover {
            background: rgba(173, 152, 111, 0.2);
          }
        }
      }
    }

    .icon-dropdown .dropdown-menu {
      max-height: 250px;
      overflow-y: auto;

      @include mixins.slim-scrollbar;
    }

    .delete-btn {
      background-color: #b81a1a !important;
      border-color: #b81a1a !important;
    }
  }

  .text-disclaimer {
    font-size: 12px;
    line-height: 14px;
  }
}
