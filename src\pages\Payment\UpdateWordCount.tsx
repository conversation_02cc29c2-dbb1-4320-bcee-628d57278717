import { useCreateWordCountPaymentIntent } from "api";
import { SETTINGS } from "globals";
import { useState } from "react";
import { isMobileOnly } from "react-device-detect";
import { useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { formatNumberWithCommas } from "utils";

const UpdateWordCount = ({ paymentInfo, member_id }: any) => {
  const { mutateAsync: createWordCountPaymentIntent } =
    useCreateWordCountPaymentIntent();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    setLoading(true);
    try {
      const result: any = await createWordCountPaymentIntent({
        word_limit: paymentInfo?.word_limit,
        member_id,
      });
      if (result?.success && result?.data?.paymentIntent?.id) {
        navigate(
          `${ROUTE_PATH.WORD_COUNT_PAYMENT_STATUS}?payment_status=${result?.data?.paymentIntent?.status}`,
        );
      }
    } catch (error: any) {
      console.log("An unexpected error occured.", error);
    } finally {
      setLoading(false);
    }
  };
  return (
    <>
      <div
        className={`d-flex flex-column ${isMobileOnly ? "text-center" : ""}`}
        style={{ gap: "10px" }}
      >
        <h1 className="mb-0 text-center auth-form-heading text-uppercase fw-bold">
          Payment Details
        </h1>

        <p className="mb-0 auth-form-description font-gray text-center">
          Looking to upgrade your word limit? Make the payment to add more
          words.
        </p>

        <div className="plan-details mt-3">
          <div
            className="d-flex text-lg-start text-center flex-lg-row flex-column justify-content-center align-items-center mt-3"
            style={{ gap: "10px", fontSize: "18px" }}
          >
            <p className="mb-0">
              <strong className="me-1">Amount:</strong>
              <br className="d-lg-none d-block" />
              {SETTINGS.CURRENCY_INFO.GBP.symbol}
              {paymentInfo?.amount}
            </p>

            <p className="mb-0">
              <strong className="me-1">Word Limit:</strong>
              <br className="d-lg-none d-block" />
              {formatNumberWithCommas(paymentInfo?.word_limit)}
            </p>
          </div>
        </div>
      </div>

      <form
        id="payment-form"
        className="d-flex flex-column"
        style={{ gap: "20px" }}
        onSubmit={handleSubmit}
      >
        <div className="website-form w-100 mt-3">
          <div className="action-btns d-flex flex-column gap-3">
            <button
              id="submit"
              className="btn submit-btn w-100 bg-brown border-brown text-uppercase font-light"
              disabled={loading}
            >
              {loading ? "Please wait..." : "Update Plan"}
            </button>
          </div>
        </div>
      </form>
    </>
  );
};

export default UpdateWordCount;
