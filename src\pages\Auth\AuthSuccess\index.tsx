import { RiCheckboxCircleFill } from "@remixicon/react";
import { BackButton } from "components";
import { SUCCESS_PAGE_CONTENT } from "globals";
import parse from "html-react-parser";
import { Image } from "react-bootstrap";
import { ROUTE_PATH } from "routes";
import "./styles.scss";
import { SuccessPageContent } from "types";
import useUserStore, { setUserInfo } from "stores/user";

interface AuthSuccessProps {
  type: keyof SuccessPageContent;
}

const AuthSuccess: React.FC<AuthSuccessProps> = ({ type }) => {
  const userInfo = useUserStore((state) => state.userInfo);
  const content = SUCCESS_PAGE_CONTENT[type];
  if (!content) {
    return null;
  }

  const { heading, subheading, description, useIcon, imagePath } = content;

  const handleUpdateUser = () => {
    setUserInfo({
      ...userInfo,
      user: {
        ...userInfo.user,
        email_verified: true,
      },
    });
  };
  return (
    <div
      className="auth-form d-flex justify-content-center align-items-center flex-column"
      style={{ gap: "30px" }}
    >
      <div className="d-flex flex-column" style={{ gap: "23px" }}>
        <div className="text-center">
          {useIcon ? (
            <RiCheckboxCircleFill size={"150px"} color="#ad986f" />
          ) : (
            <Image src={imagePath} style={{ width: "120px" }} />
          )}
        </div>

        <h1 className="auth-form-heading text-uppercase mb-0 text-center">
          {heading}
          <br />
          {parse(subheading)}
        </h1>

        <p className="mb-0 auth-form-description font-gray text-center">
          {parse(description)}
        </p>
      </div>

      <div className="website-form">
        <div className="action-btns d-flex flex-column" style={{ gap: "30px" }}>
          <BackButton
            title={`Back To ${userInfo.token ? "Home" : "Login"}`}
            url={ROUTE_PATH.LOGIN}
            onClick={userInfo?.token ? handleUpdateUser : null}
          />
        </div>
      </div>
    </div>
  );
};

export default AuthSuccess;
