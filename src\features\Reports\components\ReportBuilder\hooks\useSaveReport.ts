import { RiArticleLine } from "@remixicon/react";
import { useAddReport, useEditReport } from "features/Reports/api";
import useReportStore, {
  setIsDirty,
  setReportInfo
} from "features/Reports/store/report";
import { useInvalidateQuery } from "hooks";
import toast from "react-hot-toast";
import { useParams } from "react-router-dom";
import { setConfirmModalConfig } from "stores";
import { useTiptapConverter } from "./useTiptapConverter";

export default function useSaveReport() {
  const [invalidateQueries] = useInvalidateQuery();
  const { convertSections } = useTiptapConverter();

  const { id: report_id } = useParams();

  const { mutateAsync: addReport } = useAddReport();
  const { mutateAsync: editReport } = useEditReport();

  const saveReport = async ({
    useJsonFormat = false,
    saveSilently = false,
  } = {}) => {
    try {
      // Get the current state directly to ensure we have the latest data
      const currentReportInfo: any = useReportStore.getState().reportInfo;
      // TEMPORARY: Transform content based on format toggle (will be removed later)
      const reportId = report_id ?? currentReportInfo?.id;
      let payload = { ...currentReportInfo };
      const noTitleAvailable = currentReportInfo?.title?.trim()?.length === 0;
      const noSectionAvailable =
        !currentReportInfo?.sections[0]?.content?.content?.length;

      // If no title or sections are available, we don't proceed with saving
      if (saveSilently && (noSectionAvailable || noTitleAvailable)) return;

      // Remove empty sections if they exist and filter visibility keys if not in generate mode
      payload.sections = currentReportInfo.sections
        .filter((section: any) => {
          return section.content?.content?.length > 0;
        })
        .map((section: any) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { show_section_title, ...sectionWithoutVisibility } = section || {};
          return sectionWithoutVisibility;
        });

      if (useJsonFormat) {
        const sectionsForConversion = currentReportInfo.sections.map(
          (section: any) => {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { show_section_title, ...sectionWithoutVisibility } = section || {};
            return sectionWithoutVisibility;
          }
        );
        payload = {
          ...currentReportInfo,
          sections: convertSections(sectionsForConversion, "json"),
        };
      }

      const body = {
        sections: payload.sections,
        title: payload.title,
      };
      const response: any = reportId
        ? await editReport({ id: reportId, body })
        : await addReport(body);
      if (response?.success) {
        const isNewReport = !reportId;

        if (!saveSilently) {
          if (isNewReport) {
            // resetReportState();
          } else {
            setReportInfo(response?.data);
          }
          toast.success(response?.message);
        } else {
          const currentSections = currentReportInfo.sections || [];
          const responseSections = response?.data?.sections || [];

          const updatedSections = currentSections.map((section: any) => {
            const matched = responseSections.find(
              (resSec: any) => resSec.position === section.position
            );
            return matched ? { ...section, id: matched.id } : section;
          });

          const updatedReportInfo = {
            ...currentReportInfo,
            ...(isNewReport && { id: response?.data?.id }),
            sections: updatedSections,
          };

          setReportInfo(updatedReportInfo);
        }

        setIsDirty(false);
        invalidateQueries(["reports-list"]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleSaveReport = ({ useJsonFormat = false }) => {
    const currentReportInfo = useReportStore.getState().reportInfo;

    if (!currentReportInfo.title) {
      toast.error("Please enter report title");
      return;
    }
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => saveReport({ useJsonFormat }),
        content: {
          heading: `${report_id ? "Update" : "Save"} Report`,
          description: `Are you sure you want to ${report_id ? "update" : "save"} this report?`,
        },
        iconColor: "#ad986f",
        icon: RiArticleLine,
      },
    });
  };

  return { handleSaveReport, saveReport };
}
