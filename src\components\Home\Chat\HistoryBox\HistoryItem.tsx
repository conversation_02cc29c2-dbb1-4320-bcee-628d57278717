import { Link, useNavigate, useParams } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import HistoryItemAction from "./HistoryItemAction";

interface ItemInfo {
  chat_id: string;
  text: string;
}

interface HistoryItemProps {
  itemInfo: ItemInfo;
  setHistoryList: React.Dispatch<React.SetStateAction<any[]>>;
  setTotalCount: React.Dispatch<React.SetStateAction<number>>;
  openItemId: string | null;
  setOpenItemId: React.Dispatch<React.SetStateAction<string | null>>;
}

const HistoryItem: React.FC<HistoryItemProps> = ({
  itemInfo,
  setHistoryList,
  setTotalCount,
  openItemId,
  setOpenItemId,
}) => {
  const { id: paramsChatId } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const handleClick = (chat_id: string) => {
    navigate(`/${chat_id}`);
  };

  return (
    <li
      className={`position-relative list-item ${paramsChatId === itemInfo.chat_id ? "active-chat" : ""}`}
      onClick={() => handleClick(itemInfo.chat_id)}
    >
      <div className="list-item-wrapper overflow-hidden">
        <Link
          to={ROUTE_PATH.HOME}
          className="mb-0 text-decoration-none list-unstyled m-0 list-item-wrapper-description position-relative d-block text-truncate"
        >
          {itemInfo.text}
        </Link>

        <HistoryItemAction
          itemInfo={itemInfo}
          setHistoryList={setHistoryList}
          setTotalCount={setTotalCount}
          openItemId={openItemId}
          setOpenItemId={setOpenItemId}
        />
      </div>
    </li>
  );
};

export default HistoryItem;
