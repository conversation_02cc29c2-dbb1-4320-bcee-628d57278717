import { RiUserFollowLine, RiUserForbidLine } from "@remixicon/react";
import { useUpdateMemberStatus } from "api";
import { OrgUserStatus } from "globals";
import { useInvalidateQuery } from "hooks";
import toast from "react-hot-toast";
import { setConfirmModalConfig } from "stores";

const useHandleMemberStatus = ({ queryKeys = [] }: any) => {
  const { mutateAsync: updateMember } = useUpdateMemberStatus();
  const [invalidateQueries] = useInvalidateQuery();

  const onClickItem = ({ row, organization, status }: any) => {
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleStatusChange({ row, organization, status }),
        content: {
          heading: "Update Status",
          description: `Are you sure you want to ${status}?`,
        },
        icon:
          status === OrgUserStatus.ACTIVE ? RiUserFollowLine : RiUserForbidLine,
        iconColor: status === OrgUserStatus.ACTIVE ? "#ad986f" : "red",
      },
    });
  };

  const handleStatusChange = async ({ row, organization, status }: any) => {
    try {
      const params = {
        org_id: organization?.id,
        member_id: row?.id,
        payload: {
          status,
        },
      };
      const result: any = await updateMember(params);
      if (result?.success) {
        toast.success(result?.message || "Status Updated Successfully!");
        invalidateQueries(queryKeys);
      }
    } catch (err: any) {
      console.log(err);
    }
  };

  return { onClickItem };
};

export default useHandleMemberStatus;
