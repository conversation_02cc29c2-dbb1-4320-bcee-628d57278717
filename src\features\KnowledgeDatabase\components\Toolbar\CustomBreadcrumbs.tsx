import { KNOWLEDGE_BASE_ROUTE_PATH } from "features/KnowledgeDatabase/routePath";
import React from "react";
import { Breadcrumb } from "react-bootstrap";
import Dropdown from "react-bootstrap/Dropdown";
import { useNavigate, useParams } from "react-router-dom";

const MAX_VISIBLE = 3;

export interface Breadcrumb {
  id: string;
  name: string;
  isBase?: boolean;
}

export interface BreadcrumbsProps {
  breadcrumbs: Breadcrumb[];
  owner_type: string;
}

const CustomBreadcrumbs: React.FC<BreadcrumbsProps> = ({
  breadcrumbs,
  owner_type,
}) => {
  const navigate = useNavigate();
  const { parent_id } = useParams();

  const allCrumbs = [
    { id: "knowledge-base-root", name: "Knowledge Base", isBase: true },
    ...(breadcrumbs || []),
  ];

  const isActive = (id: any) => parent_id === id;

  const onClickItem = (item: any) => {
    if (isActive(item?.id)) return;
    if (item.isBase) {
      navigate(
        `${KNOWLEDGE_BASE_ROUTE_PATH.KNOWLEDGE_BASE}?owner_type=${owner_type}`
      );
    } else {
      navigate(
        `${KNOWLEDGE_BASE_ROUTE_PATH.KNOWLEDGE_BASE}/${item.id}?owner_type=${owner_type}`
      );
    }
  };

  let visibleCrumbs: any[] = [];

  if (allCrumbs.length > MAX_VISIBLE) {
    visibleCrumbs = [
      allCrumbs[0],
      { type: "ellipsis", items: allCrumbs.slice(1, -2) },
      ...allCrumbs.slice(-2),
    ];
  } else {
    visibleCrumbs = allCrumbs;
  }

  return (
    <Breadcrumb className="custom-breadcrumb">
      {visibleCrumbs.map((crumb, idx) => {
        if (crumb.type === "ellipsis") {
          return (
            <Dropdown
              as={Breadcrumb.Item}
              key="ellipsis"
              align="end"
              title="Show More..."
            >
              <Dropdown.Toggle as="span" style={{ cursor: "pointer" }}>
                …
              </Dropdown.Toggle>
              <Dropdown.Menu>
                {crumb.items.map((item: any) => (
                  <Dropdown.Item
                    key={item.id}
                    onClick={() => onClickItem(item)}
                  >
                    {item.name}
                  </Dropdown.Item>
                ))}
              </Dropdown.Menu>
            </Dropdown>
          );
        }
        const isLast = idx === visibleCrumbs.length - 1;
        return (
          <Breadcrumb.Item
            key={crumb.id}
            onClick={() => !isLast && onClickItem(crumb)}
            active={isLast}
            style={isLast ? { cursor: "default" } : { cursor: "pointer" }}
          >
            <span style={{ color: "#0d3149" }}>{crumb.name}</span>
          </Breadcrumb.Item>
        );
      })}
    </Breadcrumb>
  );
};

export default CustomBreadcrumbs;
