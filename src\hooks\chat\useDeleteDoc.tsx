import { useDeleteDocument } from "api";
import toast from "react-hot-toast";
import { useParams } from "react-router-dom";
import {
  setConfirmModalConfig,
  setIsDocDeleted,
  setMessageHistory,
} from "stores";

const useDeleteDoc = ({ metadata, msgId }: any) => {
  const { mutateAsync: deleteDocument } = useDeleteDocument();
  const { id: chat_id } = useParams();

  const handleDelete = async (doc_id: any, msg_id: any) => {
    try {
      setIsDocDeleted(true);
      const result: any = await deleteDocument({ chat_id, doc_id, msg_id });
      if (result?.success) {
        toast.success(result?.message || "Chat Deleted Successfully!");
        setMessageHistory((prev: any) =>
          prev.map((item: any) => {
            if (item.id === msgId) {
              return {
                ...item,
                is_doc_deleted: true,
              };
            }
            return item;
          }),
        );
      }
    } catch (err: any) {
      console.log(err);
    }
  };

  const onClickDelete = (evt: any) => {
    evt.stopPropagation();
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleDelete(metadata?.doc_id, msgId),
        content: {
          heading: "Delete Document?",
          description: "Are you sure you want to delete this document?",
        },
      },
    });
  };

  return { onClickDelete };
};

export default useDeleteDoc;
