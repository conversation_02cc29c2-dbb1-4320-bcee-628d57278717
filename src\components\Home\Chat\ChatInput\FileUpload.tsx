import { RiAttachmentLine } from "@remixicon/react";
import { Form } from "react-bootstrap";
import "./styles.scss";

const FileUpload = ({
  handleFileChange,
  fileRef,
  icon: FileIcon = RiAttachmentLine,
  controlId = "fileInput",
  customClass = "",
  accept = "*",
}: any) => {
  const onChange = (e: any) => {
    const files = Array.from(e.target.files);
    if (files.length) {
      handleFileChange(files[0]);
      e.target.value = null;
    }
  };

  return (
    <Form.Group
      controlId={controlId}
      className={`m-0 p-0 file-input-label ${customClass} position-absolute`}
    >
      <Form.Label className="cursor-pointer p-0 m-0">
        <FileIcon size={"24px"} color="#D1D1D1" />
      </Form.Label>
      <Form.Control
        type="file"
        className="d-none"
        onChange={onChange}
        ref={fileRef}
        accept={accept}
      />
    </Form.Group>
  );
};

export default FileUpload;
