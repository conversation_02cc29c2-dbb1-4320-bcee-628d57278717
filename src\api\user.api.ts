import { useMutation } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals";
import { apiClient } from "./apiClient";
import { EntityConfigurationInterface } from "types";

export const useMyProfileMutation = () => {
  return useMutation({
    mutationFn: (token: string | null) => {
      return apiClient.get(API_ENDPOINTS.MY_PROFILE, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    },
  });
};

export const useUpdateProfileMutation = () => {
  return useMutation({
    mutationFn: (payload: Record<string, any>) => {
      return apiClient.post(API_ENDPOINTS.UPDATE_PROFILE, payload);
    },
  });
};

export const useUpdateRedactSettingMutation = () => {
  return useMutation({
    mutationFn: (payload: EntityConfigurationInterface) => {
      return apiClient.post(API_ENDPOINTS.UPDATE_REDACT_SETTING, payload);
    },
  });
};
