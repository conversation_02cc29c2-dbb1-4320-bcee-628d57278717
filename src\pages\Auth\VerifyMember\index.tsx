import {
  useVerifyAccountToken,
  useVerifyMemberAccount,
  useVerifyOfflineAccount,
} from "api";
import { BackButton, PasswordField } from "components";
import { VerifyMemberValidations } from "formSchema/schemaValidations";
import { Form, Formik } from "formik";
import { IMAGE_PATH } from "globals";
import { useEffect, useState } from "react";
import { Button, Image, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";
import { useNavigate, useSearchParams } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { VerifyAccountTypeEnum } from "../../../types";

const CONTENT_CONFIG = {
  loading: {
    heading: "Just a moment...",
    description: "",
  },
  verified: {
    heading: "Set Up Your Profile",
    description: "Welcome! Enter a password to create your profile.",
  },
  error: {
    heading: "Something went wrong!",
    description: "Please try again later.",
  },
};

const VerifyMember = ({ type }: { type: VerifyAccountTypeEnum }) => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token");
  const navigate = useNavigate();
  const { data: tokenResponse } = useVerifyAccountToken({ token, type });
  const { mutateAsync: verifyMemberAccount } = useVerifyMemberAccount();
  const { mutateAsync: verifyOfflineAccount } = useVerifyOfflineAccount();

  const [verifyTokenLoading, setVerifyTokenLoading] = useState(true);
  const [isTokenVerified, setIsTokenVerified] = useState(false);

  useEffect(() => {
    if (tokenResponse?.id) {
      setIsTokenVerified(true);
      setTimeout(() => {
        setIsTokenVerified(true);
        setVerifyTokenLoading(false);
      }, 2000);
    } else {
      setTimeout(() => {
        setVerifyTokenLoading(false);
      }, 2000);
    }
  }, [tokenResponse]);

  const handleSubmit = async (values: any, { setSubmitting }: any) => {
    try {
      setSubmitting(true);
      const response: any = await (
        type === VerifyAccountTypeEnum.MEMBER
          ? verifyMemberAccount
          : verifyOfflineAccount
      )(values);
      if (response?.success) {
        toast.success(response?.message);
        navigate(ROUTE_PATH.LOGIN);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };

  const renderLoadingState = () => (
    <div
      className="d-flex flex-column justify-content-center align-items-center"
      style={{ gap: "23px" }}
    >
      <Spinner />
      <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
        {CONTENT_CONFIG.loading.heading}
      </h1>
      <p className="mb-0 auth-form-description font-gray text-center">
        {CONTENT_CONFIG.loading.description}
      </p>
    </div>
  );

  const renderVerifiedState = () => (
    <>
      <div className="d-flex flex-column" style={{ gap: "23px" }}>
        <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
          {CONTENT_CONFIG.verified.heading}
        </h1>
        <p className="mb-0 auth-form-description font-gray text-center">
          {CONTENT_CONFIG.verified.description}
        </p>
      </div>

      <div className="website-form">
        <Formik
          initialValues={{ newPassword: "", activation_token: token }}
          validationSchema={VerifyMemberValidations}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting }) => (
            <Form className="d-flex flex-column" style={{ gap: "30px" }}>
              <div className="form-group position-relative">
                <PasswordField
                  label="Password"
                  fieldName="newPassword"
                  placeholder="Enter your password"
                />
              </div>
              <div
                className="action-btns d-flex flex-column"
                style={{ gap: "30px" }}
              >
                <Button
                  type="submit"
                  className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? <Spinner /> : "Submit"}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </>
  );

  const renderErrorState = () => (
    <>
      <div className="d-flex flex-column" style={{ gap: "23px" }}>
        <div className="text-center">
          <Image src={IMAGE_PATH.redErrorIcon} style={{ width: "120px" }} />
        </div>
        <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
          {CONTENT_CONFIG.error.heading}
        </h1>
        <p className="mb-0 auth-form-description font-gray text-center">
          {CONTENT_CONFIG.error.description}
        </p>
      </div>

      <div className="website-form">
        <div className="action-btns d-flex flex-column" style={{ gap: "30px" }}>
          <BackButton title="Back To Home" url={ROUTE_PATH.LOGIN} />
        </div>
      </div>
    </>
  );

  return (
    <div
      className="auth-form d-flex justify-content-center align-items-center flex-column login-form̥"
      style={{ gap: "30px" }}
    >
      {verifyTokenLoading
        ? renderLoadingState()
        : isTokenVerified
          ? renderVerifiedState()
          : renderErrorState()}
    </div>
  );
};

export default VerifyMember;
