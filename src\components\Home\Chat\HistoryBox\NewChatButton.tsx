import { RiAddLine } from "@remixicon/react";
import { But<PERSON> } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";

const NewChatButton = ({ onCloseCanvas = () => {} }: any) => {
  const navigate = useNavigate();

  const handleNewChat = () => {
    navigate(ROUTE_PATH.HOME);
    onCloseCanvas();
  };
  return (
    <div className="ps-3 mb-3 start-new-chat">
      <Button
        variant="primary"
        className="w-100 py-2 bg-blue font-light border-blue text-decoration-none fw-bold d-flex justify-content-between align-items-center"
        onClick={handleNewChat}
      >
        Start New Chat
        <RiAddLine size={"22px"} color="#f9f9f9" />
      </Button>
    </div>
  );
};

export default NewChatButton;
