@use "/src/styles/mixins/mixins.scss" as mixins;

.privacy-offcanvas {
  min-width: 500px;

  @media only screen and (max-width: 576px) {
    min-width: auto;
  }

  &-header {
    padding: 20px 20px;

    .btn-close {
      @include mixins.btn-close-style;
    }
  }

  &-title {
    font-weight: 600;
  }

  &-body {
    @include mixins.slim-scrollbar;

    .coming-soon {
      &-tag {
        background: linear-gradient(to right, #e4cfa5, #ad986f);
        right: 10px;
        font-weight: 700;
        letter-spacing: 1px;
        font-size: 11px;
        padding: 7px 10px;
        padding-right: 35px;
        color: #0d3149;
        top: 50%;
        transform: translateY(-50%);
        border-radius: 100px;
        z-index: -1;
      }

      &-group {
        border-top: 2px dashed #ad986f;
        padding-top: 10px;

        .coming-soon-tag {
          right: -5px;
          top: 12px;
          transform: none;
          padding: 5px 10px;
        }
      }
    }

    hr {
      border-color: #0d3149;
      margin-bottom: 20px;
    }

    .accordion {
      &-item {
        border: 1px solid #0d3149;

        &:not(:last-child) {
          margin-bottom: 15px;
        }
      }

      &-header {
        &.without-switch {
          button {
            padding-left: 20px;
          }
        }

        button {
          position: relative;
          z-index: 1;
          outline: none;
          padding-left: 50px;

          @include mixins.custom-check-box;

          .check-box {
            left: 20px;
            top: 45%;

            input[type="checkbox"] {
              width: 20px;
              height: 20px;
              position: relative;
              z-index: 3;
            }
          }

          &:focus {
            border-color: transparent;
          }

          &::after {
            background-image: url("../../../../assets/images/arrowBlue.svg");
            background-size: 15px;
            background-position: center;
          }

          &:not(.collapsed) {
            background-color: #fff;
            font-weight: bold;

            &::after {
              background-image: url("../../../../assets/images/arrowBlue.svg");
            }

            .check-box {
              input[type="checkbox"] {
                border: 2px solid #f9f9f9;

                &:checked {
                  border-color: #ad986f;
                  background-color: #ad986f;
                }
              }
            }

            &.collapsed {
              background-color: transparent;
            }
          }

          @media only screen and (max-width: 767px) {
            .option-title {
              max-width: 165px;
            }
          }
        }
      }

      &-body {
        .option-check-list {
          gap: 8px;

          @include mixins.custom-check-box;

          .non-check-tick {
            padding: 15px 0px;
            border: 1px solid #efefef;
            width: fill-avilable;
            width: -webkit-fill-available;
            font-size: 15px;
            border-radius: 7px;

            &.selected {
              background-color: #0d3149;
              color: #fff;
            }
          }

          .option-button {
            padding: 12px 0px;
            font-size: 17px;

            &:hover {
              border: 1px solid #0d3149;
              cursor: pointer;
            }

            &.active-item {
              border-bottom: 4px solid #ad986f !important;
            }

            .preset-remove-btn {
              top: -8px;
              right: -5px;
              color: #0d3149;
              width: 20px;
              height: 20px;
            }
          }
        }
      }
    }

    .form {
      &-switch {
        input {
          width: 32px;
          height: 16px;
          cursor: pointer;
          border: 2px solid #0d3149;
          background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9Jy00IC00IDggOCc+PGNpcmNsZSByPSczJyBmaWxsPSdyZ2JhKDE3MywgMTUyLCAxMTEpJy8+PC9zdmc+DQoNCg==");

          &:checked {
            background-color: #0d3149;
            background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9Jy00IC00IDggOCc+PGNpcmNsZSByPSczJyBmaWxsPScjZmZmJy8+PC9zdmc+DQoNCg==");
          }

          &:focus {
            box-shadow: 0 0 0 0.25rem rgba(13, 49, 73, 0.25);
          }
        }

        label {
          font-size: 18px;
          font-weight: 500;
        }
      }
    }

    .ai-toggler {
      border: 1px solid #0d3149;
      margin-bottom: 15px;
      padding: 10px 16px;
      line-height: inherit;
      gap: 10px;
      border-top-right-radius: var(--bs-accordion-inner-border-radius);
      border-top-left-radius: var(--bs-accordion-inner-border-radius);

      &-button {
        padding: 12px 0px;
        font-size: 17px;
        border: 1px solid #efefef;
        border-radius: 7px;
        width: 50%;

        &:hover {
          border: 1px solid #0d3149;
          cursor: pointer;
        }

        &.active {
          background-color: #0d3149;
          color: #ffffff;
        }
      }
    }
  }

  .action-btns {
    .submit-btn {
      @include mixins.submit-btn;

      &.bg-transparent {
        @include mixins.submit-btn-transparent;
        @include mixins.button-layer-hover;
      }
    }
  }
}
