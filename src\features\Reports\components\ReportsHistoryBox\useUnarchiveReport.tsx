import { RiInboxUnarchiveFill } from "@remixicon/react";
import { useArchiveReportMutation } from "features/Reports/api";
import { useInvalidateQuery } from "hooks";
import toast from "react-hot-toast";
import { setConfirmModalConfig } from "stores";

const useUnarchiveReport = () => {
  const { mutateAsync: archiveHistory } = useArchiveReportMutation();
  const [invalidateQueries] = useInvalidateQuery();

  const handleUnarchive = async (id: string) => {
    try {
      const result: any = await archiveHistory(id);
      if (result?.success) {
        toast.success(result?.message || "Report Unarchived Successfully!");
        invalidateQueries(["archived-reports"]);
      }
    } catch (err) {
      console.error(err);
    }
  };

  const onClickUnarchive = (id: string) => {
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleUnarchive(id),
        content: {
          heading: "Unarchive",
          description: "Are you sure you want to unarchive this report?",
        },
        icon: RiInboxUnarchiveFill,
        iconColor: "#ad986f",
      },
    });
  };

  return { onClickUnarchive };
};

export default useUnarchiveReport;
