import {
  RiArticleLine,
  RiBook<PERSON>helfLine,
  RiCustomerService2Line,
  RiDiscussLine,
  RiLock2Line,
  RiTeamLine,
} from "@remixicon/react";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { ROUTE_PATH } from "routes";
import useUserStore from "stores/user";
import useFeatureEnable from "./useFeatureEnable";
import useFeatureFlags from "./useFeatureFlags";
import { KNOWLEDGE_BASE_ROUTE_PATH } from "features/KnowledgeDatabase/routePath";

const useSidebarLinks = () => {
  const userData = useUserStore((state) => state.userInfo.user);
  const { isFeatureEnabled } = useFeatureFlags();
  const { isReportsEnabled, isKnowledgeBaseEnabled } = useFeatureEnable();

  const sidebarLinks = [
    {
      path: userData?.is_subscription
        ? ROUTE_PATH.HOME
        : ROUTE_PATH.SUBSCRIPTIONS,
      label: "Secure Chat",
      icon: RiLock2Line,
    },
    isFeatureEnabled("MEETINGS")
      ? {
          path: "",
          label: "Meetings",
          icon: RiDiscussLine,
          coming_soon: true,
        }
      : null,
    isFeatureEnabled("REPORTS") && isReportsEnabled
      ? {
          path: REPORTS_ROUTE_PATH.GENERATE_REPORT,
          label: "Reports",
          icon: RiArticleLine,
          subItems: [
            {
              path: REPORTS_ROUTE_PATH.GENERATE_REPORT,
              label: "Generate",
            },
            {
              path: REPORTS_ROUTE_PATH.BUILD_REPORTS,
              label: "Templates",
            },
            {
              path: REPORTS_ROUTE_PATH.REPORT_ARCHIVES,
              label: "Archives",
            },
          ],
        }
      : null,
    isFeatureEnabled("CLIENT_DATABASE")
      ? {
          path: "",
          label: "Client Database",
          icon: RiTeamLine,
          coming_soon: true,
        }
      : null,
    isFeatureEnabled("KNOWLEDGE_BASE") && isKnowledgeBaseEnabled
      ? {
          path: KNOWLEDGE_BASE_ROUTE_PATH.KNOWLEDGE_BASE,
          label: "Knowledge Database",
          icon: RiBookShelfLine,
          subItems: [
            {
              path: KNOWLEDGE_BASE_ROUTE_PATH.KNOWLEDGE_BASE,
              label: "Knowledge Base",
            },
            {
              path: KNOWLEDGE_BASE_ROUTE_PATH.FAQ_PROMPTS,
              label: "FAQ Prompts",
            },
          ],
        }
      : null,
    {
      path: ROUTE_PATH.CONTACT_US,
      label: "Contact Us",
      icon: RiCustomerService2Line,
    },
  ].filter(Boolean);
  return [sidebarLinks];
};

export default useSidebarLinks;
