import { useResetPasswordMutation } from "api";
import { <PERSON><PERSON>utton, PasswordField } from "components";
import { ResetPasswordInitialValues } from "formSchema/initialValues";
import { ResetPasswordValidations } from "formSchema/schemaValidations";
import { Form, Formik, FormikProps } from "formik";
import { <PERSON><PERSON>, Spinner } from "react-bootstrap";
import { useNavigate, useSearchParams } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import "./styles.scss";

interface ResetPasswordInterface {
  password: string;
}

const ResetPassword = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const resetPasswordToken = searchParams.get("resetPasswordToken");

  const { mutateAsync: resetPassword } = useResetPasswordMutation();

  const handleSubmit = async (
    values: ResetPasswordInterface,
    { setSubmitting }: any,
  ) => {
    try {
      const payload = {
        newPassword: values.password,
        reset_password_token: resetPasswordToken,
      };
      const response: any = await resetPassword(payload);
      if (response?.success) {
        navigate(ROUTE_PATH.RESET_SUCCESS);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };
  return (
    <div
      className="auth-form d-flex justify-content-center align-items-center flex-column pass-reset-form"
      style={{ gap: "30px" }}
    >
      <div className="d-flex flex-column" style={{ gap: "23px" }}>
        <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
          Reset Password
        </h1>

        <p className="mb-0 auth-form-description font-gray text-center">
          Your new password must be different from previous
          <br className="d-lg-block d-none" />
          used passwords.
        </p>
      </div>

      <div className="website-form">
        <Formik
          initialValues={ResetPasswordInitialValues}
          validationSchema={ResetPasswordValidations}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting }: FormikProps<any>) => (
            <Form className="d-flex flex-column" style={{ gap: "30px" }}>
              <div className="form-group position-relative">
                <PasswordField
                  label="New Password*"
                  fieldName="password"
                  placeholder="Enter your password"
                />
              </div>

              <div className="form-group position-relative">
                <PasswordField
                  label="Confirm Password*"
                  fieldName="confirmPassword"
                  placeholder="Confirm your password"
                />
              </div>

              <div
                className="action-btns d-flex flex-column"
                style={{ gap: "30px" }}
              >
                <Button
                  type="submit"
                  className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? <Spinner /> : "Submit"}
                </Button>

                <BackButton url={ROUTE_PATH.LOGIN} title="Back to login" />
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default ResetPassword;
