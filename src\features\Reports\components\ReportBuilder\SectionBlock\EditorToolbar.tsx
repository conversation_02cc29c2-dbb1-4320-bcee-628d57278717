import { Ri<PERSON>ar<PERSON><PERSON><PERSON><PERSON>, RiSave2<PERSON>ill, RiTableFill } from "@remixicon/react";
import { Editor } from "@tiptap/react";
import { useSaveReportSection } from "features/Reports/api";
import { Button } from "react-bootstrap";
import toast from "react-hot-toast";
import ImageUploadButton from "./ImageUploadButton";

interface EditorToolbarProps {
  editor: Editor | null;
  sectionId: string | undefined;
}

const EditorToolbar = ({ editor, sectionId }: EditorToolbarProps) => {
  const { mutateAsync: saveReportSection } = useSaveReportSection();

  if (!editor) {
    return null;
  }

  const handleInsertTable = () => {
    editor
      .chain()
      .focus()
      .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
      .run();
  };

  const handleInsertGraph = () => {
    editor.chain().focus().insertGraphPlaceholder().run();
  };

  const onSave = async () => {
    if (!sectionId) return;
    try {
      const res: any = await saveReportSection({
        id: sectionId,
      });
      if (res?.success) {
        toast.success(res?.message);
      }
    } catch (err: any) {
      console.log(err);
    }
  };

  return (
    <>
      <Button
        variant="outline-secondary"
        size="sm"
        onClick={handleInsertTable}
        title="Insert table"
        disabled={editor.isActive("table") || editor.isActive("listItem")}
      >
        <RiTableFill size={18} />
      </Button>

      <Button
        variant="outline-secondary"
        size="sm"
        onClick={handleInsertGraph}
        title="Insert graph placeholder"
      >
        <RiBarChartFill size={18} />
      </Button>

      <ImageUploadButton editor={editor} text="Add Image" />

      <Button
        variant="outline-secondary"
        size="sm"
        onClick={onSave}
        title={sectionId ? "Save section content" : "Please save report first"}
      >
        <RiSave2Fill size={18} />
      </Button>
    </>
  );
};

export default EditorToolbar;
