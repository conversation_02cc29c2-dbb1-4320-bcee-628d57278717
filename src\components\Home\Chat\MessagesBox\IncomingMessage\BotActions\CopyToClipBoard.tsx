import { RiCheckLine, RiFileCopyLine } from "@remixicon/react";
import { HoverTooltip } from "components/Common";
import { useState } from "react";
import { marked } from "marked";

const CopyToClipBoard = ({ messageItem }: any) => {
  const { reid_text } = messageItem ?? {};
  const [isCopied, setIsCopied] = useState(false);

  const htmlContent = reid_text ? marked(reid_text) : "";

  const handleCopyClick = async () => {
    setIsCopied(true);
    const type = "text/html";
    const data: string = await Promise.resolve(htmlContent);
    const blob = new Blob([data], { type });
    const clipboardItem = new ClipboardItem({
      [type]: blob,
      "text/plain": new Blob([reid_text], { type: "text/plain" }),
    });
    navigator.clipboard.write([clipboardItem]);
    setTimeout(() => {
      setIsCopied(false);
    }, 2000);
  };

  return (
    <HoverTooltip title={isCopied ? "Copied" : "Copy"} customClass="fw-bold">
      <span
        onClick={handleCopyClick}
        className="d-block text-decoration-none cursor-pointer"
      >
        {isCopied ? <RiCheckLine size={18} /> : <RiFileCopyLine size={18} />}
      </span>
    </HoverTooltip>
  );
};

export default CopyToClipBoard;
