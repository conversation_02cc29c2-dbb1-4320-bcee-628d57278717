import { RiStarSFill } from "@remixicon/react";
import { LoaderIcon } from "react-hot-toast";
import { OrgUserRole } from "./globals";
import { formatNumberWithCommas } from "utils";

export const getSettingsCardsConfig = ({
  IMAGE_PATH,
  ROUTE_PATH,
  isOrganisationEnabled,
  isIndividualUser,
  isOfflineAccount,
  termsSheetURL,
}: any) => {
  return [
    {
      icon: IMAGE_PATH.colorLock,
      headingAlt: "password",
      path: ROUTE_PATH.CHANGE_PASSWORD,
      heading: "Password",
      subheading: (
        <>
          <RiStarSFill size={"12px"} />
          <RiStarSFill size={"12px"} />
          <RiStarSFill size={"12px"} />
          <RiStarSFill size={"12px"} />
          <RiStarSFill size={"12px"} />
          <RiStarSFill size={"12px"} />
          <RiStarSFill size={"12px"} />
        </>
      ),
      subheadingIcon: IMAGE_PATH.colorEdit,
      subheadingAlt: "edit",
      user_role: [OrgUserRole.ADMIN, OrgUserRole.USER],
    },
    {
      icon: IMAGE_PATH.colorUser,
      headingAlt: "user",
      path: ROUTE_PATH.MY_PROFILE,
      heading: "My Info",
      subheading: <>View Information</>,
      subheadingIcon: IMAGE_PATH.colorArrowRight,
      subheadingAlt: "arrow",
      user_role: [OrgUserRole.ADMIN, OrgUserRole.USER],
    },
    {
      icon: IMAGE_PATH.colorPrivacy,
      headingAlt: "subscription",
      path: ROUTE_PATH.SUBSCRIPTIONS,
      heading: "Subscriptions",
      subheading: <>Upgrade Your Plan</>,
      subheadingIcon: IMAGE_PATH.colorArrowRight,
      subheadingAlt: "arrow",
      user_role: [OrgUserRole.ADMIN],
      disabled: isOfflineAccount,
    },
    {
      icon: IMAGE_PATH.colorSubscriptions,
      headingAlt: "my-subscription",
      path: ROUTE_PATH.MY_SUBSCRIPTIONS,
      heading: "My Subscriptions",
      subheading: <>View Your Plan</>,
      subheadingIcon: IMAGE_PATH.colorArrowRight,
      subheadingAlt: "arrow",
      customStyle: {
        width: "50px",
        height: "50px",
      },
      user_role: [OrgUserRole.ADMIN],
      disabled: isOfflineAccount,
    },
    {
      icon: IMAGE_PATH.colorPaymentCard,
      headingAlt: "payment",
      path: ROUTE_PATH.PAYMENT_CARDS,
      heading: "Payment Cards",
      subheading: <>View Saved Cards</>,
      subheadingIcon: IMAGE_PATH.colorArrowRight,
      subheadingAlt: "arrow",
      user_role: [OrgUserRole.ADMIN],
      disabled: isOfflineAccount,
    },
    {
      icon: IMAGE_PATH.colorDocs,
      headingAlt: "payment",
      path: ROUTE_PATH.INVOICES,
      heading: "Invoices",
      subheading: <>View Invoices</>,
      subheadingIcon: IMAGE_PATH.colorArrowRight,
      subheadingAlt: "arrow",
      user_role: [OrgUserRole.ADMIN],
    },
    {
      icon: IMAGE_PATH.colorInfoDoc,
      headingAlt: "quota-utilization",
      path: ROUTE_PATH.QUOTA_UTILISATION,
      heading: "Quota Utilisation",
      subheading: <>View Utilisation</>,
      subheadingIcon: IMAGE_PATH.colorArrowRight,
      subheadingAlt: "arrow",
      user_role: [OrgUserRole.ADMIN, OrgUserRole.USER],
    },
    // {
    //   icon: IMAGE_PATH.colorCrown,
    //   headingAlt: "referral",
    //   path: "#",
    //   heading: "Referrals",
    //   subheading: (
    //     <small className="text-center d-block">
    //       Refer a friend and earn up to{" "}
    //       <a
    //         href={SETTINGS.REFERRAL_REWARD_LINK}
    //         onClick={(e) => e.stopPropagation()}
    //         target="_blank"
    //         className="fw-bold text-dark"
    //       >
    //         {SETTINGS.CURRENCY_INFO.GBP.symbol}
    //         {SETTINGS.REFERRAL_REWARD_AMOUNT}
    //         John Lewis Vouchers
    //       </a>{" "}
    //       each.
    //     </small>
    //   ),
    // },
    // {
    //   icon: IMAGE_PATH.colorBug,
    //   headingAlt: "report-bug",
    //   path: ROUTE_PATH.REPORT_BUG,
    //   heading: "Report a Bug",
    //   subheading: <>Report a bug and we'll get back to you.</>,
    //   subheadingIcon: IMAGE_PATH.colorArrowRight,
    //   subheadingAlt: "arrow",
    // },
    // {
    //   icon: IMAGE_PATH.colorBulb,
    //   headingAlt: "suggest-feature",
    //   path: ROUTE_PATH.SUGGEST_FEATURE,
    //   heading: "Suggest a Feature",
    //   subheading: <>Let us know and we'll get back to you.</>,
    //   subheadingIcon: IMAGE_PATH.colorArrowRight,
    //   subheadingAlt: "arrow",
    // },
    isOrganisationEnabled
      ? {
          icon: IMAGE_PATH.colorUsers,
          headingAlt: "organisation",
          path: ROUTE_PATH.ORGANISATION_MANAGER,
          heading: "Organisation",
          subheading: "Manage Your Team",
          subheadingIcon: IMAGE_PATH.colorArrowRight,
          subheadingAlt: "arrow",
          user_role: [OrgUserRole.ADMIN],
        }
      : {},
    isIndividualUser
      ? {
          icon: IMAGE_PATH.colorSetting,
          headingAlt: "doc-settings",
          path: ROUTE_PATH.DOC_SETTINGS,
          heading: "Document Settings",
          subheading: <>Set Export Branding</>,
          subheadingIcon: IMAGE_PATH.colorArrowRight,
          subheadingAlt: "arrow",
          user_role: [OrgUserRole.ADMIN],
          disabled: isOfflineAccount,
        }
      : {},
    termsSheetURL && {
      icon: IMAGE_PATH.sheetIcon,
      headingAlt: "terms_sheet",
      heading: "Terms Sheet",
      subheading: <>View Terms Sheet</>,
      subheadingAlt: "arrow",
      user_role: [OrgUserRole.ADMIN],
      disabled: !isOfflineAccount,
      onClick: (e: any) => {
        e.stopPropagation();
        window.open(termsSheetURL, "_blank");
      },
      customStyle: {
        width: "50px",
        height: "50px",
      },
    },
  ];
};

export const getQuotaCardsConfig = ({
  IMAGE_PATH,
  quotaData,
  isLoading,
  isOrgAdmin,
  isOfflineAccount,
}: any) => {
  const quotaTypes = [
    {
      key: "word",
      label: "Word Limit",
      used: "words_used",
      limit: "word_limit",
      icon: IMAGE_PATH.colorWord,
      org_limit: "org_word_limit",
      org_used: "org_words_used",
    },
    {
      key: "document",
      label: "Document Limit",
      used: "document_uploaded",
      limit: "document_limit",
      icon: IMAGE_PATH.colorArticle,
    },
    {
      key: "ocr",
      label: "OCR Limit",
      used: "ocr_used",
      limit: "ocr_limit",
      icon: IMAGE_PATH.colorOCR,
    },
    {
      key: "stt",
      label: "STT Limit",
      used: "stt_used",
      limit: "stt_limit",
      icon: IMAGE_PATH.colorSTT,
      isUnlimited: true,
      customStyle: {
        width: "60px",
        height: "60px",
      },
    },
  ];

  const getQuotaCard = ({
    key,
    label,
    used,
    limit,
    icon = IMAGE_PATH.colorInfoDoc,
    isUnlimited,
    customStyle = {
      width: "50px",
      height: "50px",
    },
    org_limit,
    org_used,
  }: any) => {
    const limitValue = quotaData?.[limit];
    const usedValue = quotaData?.[used];
    const orgLimitValue = quotaData?.[org_limit];
    const orgUsedValue = quotaData?.[org_used];

    return {
      icon,
      headingAlt: `${key}-limit`,
      path: "#",
      heading: label,
      subheading:
        key === "word" && isOrgAdmin && isOfflineAccount ? (
          <>
            {isUnlimited ? (
              <div>
                <p className="mb-0 split-heading">
                  <small className="ms-1">Unlimited</small>
                </p>
              </div>
            ) : limitValue ? (
              <div className="d-flex flex-column">
                <p className="mb-0 split-heading">
                  Organisation:{" "}
                  <small className="ms-1">
                    {orgLimitValue > 0
                      ? `${formatNumberWithCommas(orgUsedValue)}/${formatNumberWithCommas(orgLimitValue)}`
                      : "Unlimited"}
                  </small>
                </p>
                <p className="mb-0 split-heading">
                  Personal Usage:{" "}
                  <small className="ms-1">
                    {limitValue > 0
                      ? `${formatNumberWithCommas(usedValue)}/${formatNumberWithCommas(limitValue)}`
                      : "Unlimited"}
                  </small>
                </p>
              </div>
            ) : isLoading ? (
              <LoaderIcon />
            ) : (
              <div className="text-danger">N/A</div>
            )}
          </>
        ) : (
          <>
            {isUnlimited ? (
              <div>
                <p className="mb-0 split-heading">
                  <small className="ms-1">Unlimited</small>
                </p>
              </div>
            ) : limitValue ? (
              <div>
                <p className="mb-0 split-heading">
                  <small className="ms-1">
                    {limitValue > 0
                      ? `${formatNumberWithCommas(usedValue)}/${formatNumberWithCommas(limitValue)}`
                      : "Unlimited"}
                  </small>
                </p>
              </div>
            ) : isLoading ? (
              <LoaderIcon />
            ) : (
              <div className="text-danger">N/A</div>
            )}
          </>
        ),
      customStyle,
    };
  };

  return quotaTypes.map(getQuotaCard);
};
