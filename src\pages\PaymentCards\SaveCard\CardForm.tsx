import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import {
  CardNumberElement,
  CardCvcElement,
  CardExpiryElement,
} from "@stripe/react-stripe-js";
import { cloneElement } from "react";
import { useCardForm } from "hooks";

const CardForm = ({ onSuccess }: any) => {
  const {
    formData,
    errors,
    isSubmitting,
    handleSubmit,
    handleFormDataChange,
    handleStripeElementChange,
  } = useCardForm(onSuccess);

  return (
    <form className="d-flex flex-column" onSubmit={handleSubmit}>
      <FormInput
        name="cardholder_name"
        value={formData.cardholder_name}
        onChange={handleFormDataChange}
        error={errors?.cardholder_name}
        label="Cardholder Name"
        placeholder="Enter cardholder name"
      />
      <FormStripeElement
        id="cc-number"
        element={<CardNumberElement />}
        error={errors?.cardNumber}
        label="Card Number"
        onChange={(event: any) =>
          handleStripeElementChange(event, "cardNumber")
        }
      />
      <div className="d-flex flex-row gap-4">
        <FormStripeElement
          id="cc-cvc"
          element={<CardCvcElement />}
          error={errors?.cvc}
          label="CVC"
          onChange={(event: any) => handleStripeElementChange(event, "cvc")}
        />
        <FormStripeElement
          id="cc-expiry"
          element={<CardExpiryElement />}
          error={errors?.expiry}
          label="Exp Date"
          onChange={(event: any) => handleStripeElementChange(event, "expiry")}
        />
      </div>

      <div className="action-btns d-flex flex-column" style={{ gap: "30px" }}>
        <Button
          type="submit"
          className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
          disabled={isSubmitting}
        >
          {isSubmitting ? <Spinner /> : "Add Card"}
        </Button>
      </div>
    </form>
  );
};

const FormInput = ({
  name,
  value,
  onChange,
  error,
  label,
  placeholder,
}: any) => (
  <div className="form-group position-relative">
    <label className="form-label">{label}</label>
    <input
      name={name}
      className="form-control"
      placeholder={placeholder}
      value={value}
      onChange={onChange}
    />
    {error && <span className="text-danger">{error}</span>}
  </div>
);

const FormStripeElement = ({ id, element, error, label, onChange }: any) => (
  <div className="form-group position-relative w-100">
    <label className="form-label w-50">{label}</label>
    <div id={id}>
      {cloneElement(element, {
        options: {
          classes: {
            base: "form-control",
            complete: "is-valid",
            empty: "is-empty",
            invalid: "is-invalid",
          },
          showIcon: true,
          style: {
            base: {
              fontSize: "18px",
              fontWeight: "400",
              fontStyle: "normal",
              lineHeight: "2.7",
            },
          },
        },
        onChange: onChange,
      })}
    </div>
    {error && <span className="text-danger">{error}</span>}
  </div>
);

export default CardForm;
