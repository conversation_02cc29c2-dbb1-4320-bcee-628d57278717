import { RiMicFill, RiMicOffFill } from "@remixicon/react";
import { useCountdown } from "hooks";
import { useEffect } from "react";
import { Form } from "react-bootstrap";

const SpeechToText = ({ textareaRef, transcriptionConfig }: any) => {
  const { isRecording, transcriptions, startRecording, stopRecording } =
    transcriptionConfig || {};
  const { countdown, startCountdown } = useCountdown(3);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.value = transcriptions;
    }
  }, [transcriptions]);

  const handleStartRecording = () => {
    startCountdown();
    startRecording();
  };

  return (
    <Form.Group
      className={`m-0 p-0 file-input-label voice position-absolute ${countdown > 0 ? "start-countdown" : ""}`}
    >
      <Form.Label className="cursor-pointer p-0 m-0">
        {countdown > 0 ? (
          <div className="countdown">{countdown}</div>
        ) : isRecording ? (
          <div className="wave-pulse" onClick={stopRecording}>
            <RiMicFill size="24px" className="text-danger" />
          </div>
        ) : (
          <RiMicOffFill
            size={"24px"}
            color="#D1D1D1"
            onClick={handleStartRecording}
          />
        )}
      </Form.Label>
      <Form.Control type="file" className="d-none" />
    </Form.Group>
  );
};

export default SpeechToText;
