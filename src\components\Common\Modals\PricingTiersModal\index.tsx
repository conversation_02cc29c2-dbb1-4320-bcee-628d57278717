import { RiCloseLine } from "@remixicon/react";
import { SETTINGS } from "globals";
import ProfilePicture from "pages/Profile/MyProfile/ProfilePicture";
import React from "react";
import { Button, Modal } from "react-bootstrap";
import { getOrganisationInfo } from "stores/user";
import "./styles.scss";

interface PricingTiersModalProps {
  show: boolean;
  onClose: () => void;
  tierConfig: any;
  subscriptionTiers: any;
}

const PricingTiersModal: React.FC<PricingTiersModalProps> = ({
  show,
  onClose,
  tierConfig,
  subscriptionTiers,
}) => {
  const organization = getOrganisationInfo();

  const teamSize = tierConfig.team_size || 0;

  const getHighlightedTiers = () => {
    const highlightIndexes: number[] = [];

    subscriptionTiers.forEach((tier: any, index: number) => {
      if (teamSize >= tier.min_users) {
        highlightIndexes.push(index);
      }
    });

    return highlightIndexes;
  };

  const highlightedIndexes = getHighlightedTiers();

  return (
    <Modal
      show={show}
      onHide={onClose}
      keyboard={false}
      centered
      className="pricing-tiers-modal"
    >
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={onClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>

        <div
          className="auth-form d-flex justify-content-center align-items-center flex-column"
          style={{ gap: "30px" }}
        >
          <div className="d-flex flex-column" style={{ gap: "15px" }}>
            <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
              Pricing Tiers
            </h1>
          </div>

          <div className="p-1 rounded-circle">
            <ProfilePicture
              userPicture={organization?.logo}
              title={organization?.title || ""}
              name="logo"
              imageProps={{
                width: 120,
                height: 120,
              }}
            />
          </div>

          <div
            className="w-100 overflow-auto pricing-list d-flex flex-column align-items-center"
            style={{ maxHeight: "30vh" }}
          >
            {subscriptionTiers?.length ? (
              subscriptionTiers.map((item: any, index: number) => (
                <div
                  key={index}
                  className={`pricing-item d-flex w-100 ${
                    highlightedIndexes.includes(index)
                      ? "highlight-item my-1"
                      : ""
                  }`}
                >
                  <span className="pricing-column left">
                    Users {item?.min_users}-{item?.max_users}
                  </span>
                  <span className="pricing-column right">
                    {SETTINGS.CURRENCY_INFO.GBP.symbol}
                    {item?.price_per_user} per month per user
                  </span>
                </div>
              ))
            ) : (
              <div className="d-flex justify-content-center align-items-center w-100">
                <span>No pricing tiers found</span>
              </div>
            )}
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default PricingTiersModal;
