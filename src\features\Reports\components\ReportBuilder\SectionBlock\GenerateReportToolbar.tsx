import { RiCheckDoubleLine } from "@remixicon/react";
import useReportStore, {
  updateReportBlockConfirmState,
} from "features/Reports/store/report";
import { Button } from "react-bootstrap";
import RegenerateButton from "./RegenerateButton";

interface GenerateReportToolbarProps {
  editor: any;
  index: number;
  source_section_id: number | undefined;
  sectionId: string | undefined;
}

const GenerateReportToolbar = ({
  editor,
  index,
  source_section_id,
  sectionId,
}: GenerateReportToolbarProps) => {
  const reportInfo = useReportStore((state) => state.reportInfo);

  if (!editor) return null;

  const section: any = reportInfo.sections.find((s) => s.position === index);
  if (!section) return null;

  const isConfirmed = section.is_confirmed ?? false;

  const toggleConfirm = () => {
    editor?.setEditable(isConfirmed);
    updateReportBlockConfirmState(index, !isConfirmed);
  };

  return (
    <>
      <RegenerateButton
        section_id={Number(sectionId)}
        source_section_id={source_section_id}
        index={index}
      />
      <Button
        variant=""
        size="sm"
        onClick={toggleConfirm}
        title={isConfirmed ? "Unconfirm Section" : "Confirm Section"}
        // className="border border-secondary d-inline-flex align-items-center justify-content-center"
        // style={{
        //   width: "35px",
        //   height: "35px",
        // }}
      >
        {isConfirmed ? (
          <RiCheckDoubleLine color="#ad986f" />
        ) : (
          <RiCheckDoubleLine color="gray" />
        )}
      </Button>
    </>
  );
};

export default GenerateReportToolbar;
