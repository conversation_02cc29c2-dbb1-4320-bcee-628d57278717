import { RiPauseCircleLine, RiVolumeUpLine } from "@remixicon/react";
import { useSynthesizeText } from "api";
import { HoverTooltip } from "components/Common";
import { useState } from "react";
import { LoaderIcon } from "react-hot-toast";

const ReadAloud = ({
  messageItem,
  audioRef,
  history,
  setHistory,
  isPlaying,
  setIsPlaying,
}: any) => {
  const { id: message_id } = messageItem ?? {};

  const [isFetching, setIsFetching] = useState(false);

  const { mutateAsync: synthesizeText } = useSynthesizeText();

  const stopAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(null);
    }
  };

  const playAudio = (urls: string[], index: number) => {
    if (index >= urls.length) return;

    stopAudio();

    const audio = new Audio(urls[index]);
    audioRef.current = audio;
    audio.play();
    setIsPlaying(message_id);

    audio.addEventListener("ended", () => {
      setIsPlaying(null);
      playAudio(urls, index + 1);
    });
  };

  const handleReadAloud = async () => {
    if (isPlaying === message_id) {
      stopAudio();
      return;
    }

    try {
      if (history[message_id]) {
        playAudio(history[message_id], 0);
      } else {
        setIsFetching(true);
        const result = await synthesizeText({ message_id });
        const urlsArray = result.data.urls;
        setHistory((prevHistory: any) => ({
          ...prevHistory,
          [message_id]: urlsArray,
        }));
        playAudio(urlsArray, 0);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsFetching(false);
    }
  };

  return (
    <HoverTooltip
      title={isPlaying ? "Pause" : "Read Aloud"}
      customClass="fw-bold"
    >
      <span
        className="d-block text-decoration-none cursor-pointer"
        onClick={handleReadAloud}
      >
        {isFetching ? (
          <LoaderIcon style={{ borderRightColor: "#0d3149" }} />
        ) : isPlaying === message_id ? (
          <RiPauseCircleLine size={18} />
        ) : (
          <RiVolumeUpLine size={18} />
        )}
      </span>
    </HoverTooltip>
  );
};

export default ReadAloud;
