.report-generator-section {
  .form-group {
    .form-control {
      height: 56px;
      padding: 0px 50px 0px 15px;
      border-radius: 50px;
      background: rgba(173, 152, 111, 0.1);
      font-size: 18px;
      font-style: normal;
      border: none;
      border: 3px solid rgba(173, 152, 111, 0.1);

      &:focus {
        box-shadow: none;
      }

      &::placeholder {
        color: #0d3149;
        opacity: 0.5;
      }

      &[type="password"]:not(:placeholder-shown) {
        font-family: auto;
      }
    }
  }

  .file-upload-container {
    min-height: 200px;
    border: 2px solid #ccc;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 1rem;
  }

  .file-preview-wrapper {
    width: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .file-image-wrapper {
      position: relative;
      width: 100px;
      height: 100px;

      .file-thumbnail,
      .file-placeholder {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 5px;
        background-color: #f0f0f0;
      }

      .file-placeholder {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 32px;
      }

      .btn {
        padding: 2px 6px;
      }
    }

    .file-progress {
      width: 100%;
      margin-top: 5px;
      height: 10px;
    }
  }

  .upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 130px;
    border: 2px dashed #ccc;
    border-radius: 10px;
    cursor: pointer;
  }

  .icon-wrapper {
    position: relative;
  }

  .upload-text {
    font-weight: 600;
    margin-top: 8px;
    color: #003366;
  }

  .form {
    &-switch {
      display: flex;
      justify-content: end;
      flex-direction: row-reverse;
      align-items: center;
      gap: 15px;
      padding: 15px 0px 0px 0px;

      input {
        margin: 0;
        border: 2px solid #0d3149;
        background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9Jy00IC00IDggOCc+PGNpcmNsZSByPSczJyBmaWxsPSdyZ2JhKDE3MywgMTUyLCAxMTEpJy8+PC9zdmc+DQoNCg==");

        &:checked {
          background-color: #0d3149;
          background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9Jy00IC00IDggOCc+PGNpcmNsZSByPSczJyBmaWxsPScjZmZmJy8+PC9zdmc+DQoNCg==");
        }

        &:focus {
          box-shadow: 0 0 0 0.25rem rgba(13, 49, 73, 0.25);
        }
      }
    }
  }
}
