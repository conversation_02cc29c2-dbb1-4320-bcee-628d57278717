import { Provider, ErrorBoundary } from "@rollbar/react";
import ErrorFallback from "./ErrorFallback";

const rollbarConfig = {
  accessToken: import.meta.env.VITE_ROLLBAR_ACCESS_TOKEN,
  environment: import.meta.env.VITE_ROLLBAR_ENVIRONMENT,
};

const CustomErrorBoundary = ({ children }: any) => {
  return (
    <Provider config={rollbarConfig}>
      <ErrorBoundary fallbackUI={ErrorFallback}>{children}</ErrorBoundary>
    </Provider>
  );
};

export default CustomErrorBoundary;
