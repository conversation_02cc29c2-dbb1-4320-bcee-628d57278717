import { Ri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ri<PERSON>ock2<PERSON>ill, RiLockUnlockFill } from "@remixicon/react";
import { Editor } from "@tiptap/react";
import { useEffect, useState } from "react";
import { Button } from "react-bootstrap";

interface SelectionLockButtonProps {
  editor: Editor | null;
}

const SelectionLockButton = ({ editor }: SelectionLockButtonProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [isLocked, setIsLocked] = useState(false);
  const [isInstruction, setIsInstruction] = useState(false);

  useEffect(() => {
    if (!editor) return;

    const updateButton = () => {
      const { selection } = editor.state;
      const { from, to } = selection;

      // Show button only when there's a text selection
      if (from !== to && !selection.empty) {
        const editorElement = editor.view.dom as HTMLElement;
        const editorRect = editorElement.getBoundingClientRect();

        const start = editor.view.coordsAtPos(from);
        const end = editor.view.coordsAtPos(to);

        // Convert viewport-based coords to relative coords inside the editor container
        const top = start.top - editorRect.top - 36; // place above selection
        const left = (start.left + end.left) / 2 - editorRect.left; // Centered horizontally; container will translateX(-50%)

        setPosition({ top, left });
        // Check if selection is locked / instruction
        setIsLocked(editor.isActive("contentLock"));
        setIsInstruction(editor.isActive("aiInstruction"));
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    // Listen to selection changes
    const handleSelectionUpdate = () => {
      setTimeout(updateButton, 10); // Small delay to ensure DOM is updated
    };

    editor.on("selectionUpdate", handleSelectionUpdate);
    editor.on("transaction", handleSelectionUpdate);

    return () => {
      editor.off("selectionUpdate", handleSelectionUpdate);
      editor.off("transaction", handleSelectionUpdate);
    };
  }, [editor]);

  const handleLockToggle = () => {
    if (!editor) return;

    editor.chain().focus().toggleContentLock().run();

    const newLockState = editor.isActive("contentLock");
    setIsLocked(newLockState);
  };

  const handleInstructionToggle = () => {
    if (!editor) return;

    const wasActive = editor.isActive("aiInstruction");

    if (wasActive) {
      // remove instruction mark and any labels in selection
      editor
        .chain()
        .focus()
        .toggleAiInstruction()
        .run();
      editor.commands.removeAiInstructionLabelsInSelection?.();
    } else {
      // add label and apply instruction mark
      editor.commands.insertAiInstructionLabel?.();
      editor.chain().focus().toggleAiInstruction().run();
    }

    const newState = editor.isActive("aiInstruction");
    setIsInstruction(newState);
  };

  if (!isVisible || !editor) {
    return null;
  }

  return (
    <div
      className="selection-lock-button d-flex align-items-center gap-2"
      style={{
        position: "absolute",
        top: position.top,
        left: position.left,
        transform: "translateX(-50%)",
        zIndex: 1000,
      }}
    >
      <Button
        variant=""
        className={`lock-btn ${isLocked ? "locked" : "unlocked"}`}
        onClick={handleLockToggle}
        title={isLocked ? "Unlock content" : "Lock content"}
      >
        {isLocked ? <RiLock2Fill size={14} /> : <RiLockUnlockFill size={14} />}
      </Button>
      <Button
        variant=""
        className={`lock-btn ai-btn ${isInstruction ? "active" : "inactive"}`}
        onClick={handleInstructionToggle}
        title={isInstruction ? "Remove AI Instruction" : "Add AI Instruction"}
      >
        <RiLightbulbLine size={14} />
      </Button>
    </div>
  );
};

export default SelectionLockButton;
