import { RiArchive2Fill, RiInboxUnarchiveLine } from "@remixicon/react";
import { DataGridTable, TableToolbar } from "components";
import ActionButton from "components/Common/DataGridTable/ActionButton";
import { useArhivedReports } from "features/Reports/api";
import useUnarchiveReport from "features/Reports/components/ReportsHistoryBox/useUnarchiveReport";
import { useEffect, useState } from "react";
import { convertTimestampToFormat } from "utils";
import "./styles.scss";

const ReportArchives = () => {
  const [paginationConfig, setPaginationConfig] = useState<any>({
    page: 1,
    limit: 25,
  });

  const {
    data: dataList = {},
    refetch,
    isLoading,
  } = useArhivedReports({
    params: { ...paginationConfig },
  });
  const { data: rowData = [], count: totalCount } = dataList;

  const { onClickUnarchive } = useUnarchiveReport();

  const columns: any = [
    {
      field: "title",
      headerName: "Name",
      renderCell: (row: any) => (
        <p className="mb-0 fw-bold">{row?.title ?? "NA"}</p>
      ),
    },
    {
      field: "created_at",
      headerName: "Created At",
      renderCell: (row: any) => (
        <p className="mb-0 fw-bold">
          {row?.created_at ? (
            convertTimestampToFormat(row?.created_at)
          ) : (
            <span className="text-danger ms-4">N/A</span>
          )}
        </p>
      ),
    },
    {
      field: "actions",
      headerName: "Actions",
      renderCell: (row: any) => (
        <>
          <ActionButton
            title="Unarchive"
            icon={<RiInboxUnarchiveLine color="white" />}
            onClick={() => onClickUnarchive(row.id)}
          />
        </>
      ),
    },
  ];

  useEffect(() => {
    refetch();
  }, [paginationConfig?.limit, paginationConfig?.page, refetch]);

  return (
    <main className="report-archives-wrapper bg-white">
      <TableToolbar
        title="Archived Reports"
        description="View the status of your archived reports, plus manage or unarchive them."
        icon={RiArchive2Fill}
      />
      <DataGridTable
        columns={columns}
        rows={rowData}
        loading={isLoading}
        paginationProps={{
          paginationConfig,
          setPaginationConfig,
          totalCount,
        }}
      />
    </main>
  );
};

export default ReportArchives;
