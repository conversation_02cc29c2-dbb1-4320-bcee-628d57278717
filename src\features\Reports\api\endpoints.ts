const createUrl = (...arg: string[]) => {
  return arg.map((a) => a.replace(/\/$/, "")).join("/");
};

export const API_ENDPOINTS = {
  GET_REPORTS: "/reports",
  GET_REPORT_DETAILS: (id: any) => `/reports/${id}`,
  ADD_REPORT: "/reports",
  UPLOAD_REPORT_ASSET: "/reports/upload/asset",
  GET_REPORT_ASSET: (assetId: any) => `/reports/${assetId}/asset`,
  DELETE_REPORT_ASSET: (assetId: any) => `/reports/${assetId}/delete`,
  DELETE_REPORT: (id: any) => `/reports/${id}`,
  EDIT_REPORT: (id: any) => `/reports/${id}`,
  GET_SAVED_SECTIONS: "/reports/saved-sections/list",
  SAVE_REPORT_SECTION: (id: any) => `/reports/${id}/save-section`,
  DELETE_SAVED_SECTION: (id: any) => `/reports/${id}/delete-saved-section`,
  ARCHIVE_REPORT: (id: any) => `/reports/${id}/archive`,
  GET_ARCHIVED_REPORTS: "/reports/archived-reports/listing",
  REPORT_CHECK_COMPLIANCE: createUrl(
    import.meta.env.VITE_CHAT_API_URL,
    `chat_ai/compliance_checker`
  ),
  GET_GENERATED_REPORTS: "/reports/report-generation/listing",
  GENERATE_REPORT: createUrl(
    import.meta.env.VITE_CHAT_API_URL,
    `chat_ai/report_generator`
  ),
  GET_GENERATED_REPORT_DETAILS: (id: any) => `/reports/${id}/report-generation`,
  SAVE_GENERATED_REPORT: (id: any) => `/reports/${id}/update-report-generation`,
  DELETE_GENERATED_REPORT: (id: any) =>
    `/reports/${id}/delete-report-generation`,
  EXPORT_REPORT: createUrl(
    import.meta.env.VITE_CHAT_API_URL,
    `chat_ai/export_report`
  ),
  REGENERATE_SECTION: createUrl(
    import.meta.env.VITE_CHAT_API_URL,
    `chat_ai/regenerate_section`
  ),
};
