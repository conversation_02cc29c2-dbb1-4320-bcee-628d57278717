@use "/src/styles/mixins/mixins.scss" as mixins;

.notification-wrapper .table-responsive {
  max-height: 100% !important;
}

.notification-custom-table {
  tbody {
    td {
      padding: 15px 20px;
      background-color: #ffffff;

      &:first-child {
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
      }

      &:last-child {
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
      }

      &.blank-row {
        border: none;
        padding: 0;
        margin: 0;
        height: 15px;
        border-radius: 0px;
        background-color: transparent !important;
        box-shadow: none !important;
      }

      @media only screen and (max-width: 1499px) {
        .end-row {
          padding-right: 130px;
        }
      }

      .action-btns {
        position: absolute;
        z-index: 1;
        right: -10px;
        transition: all 0.4s ease;

        @media only screen and (min-width: 992px) {
          opacity: 0;
          visibility: hidden;
        }

        .submit-btn {
          @include mixins.submit-btn;
          height: 35px;
          font-size: 12px;
          border-radius: 5px;
        }
      }

      @media only screen and (max-width: 1499px) {
        &.align-baseline {
          min-width: 400px;
        }
      }

      .action-item-btn {
        @include mixins.submit-btn;
        height: 45px;
        border-radius: 5px;
      }
    }

    tr {
      &.selected-row > * {
        background-color: #0d3149;
        color: #fff;
        width: 1%;
      }

      @media only screen and (min-width: 992px) {
        &:hover {
          .action-btns {
            opacity: 1;
            visibility: visible;
          }
        }
      }
    }
  }

  thead {
    inset-block-start: 0;
  }

  tfoot {
    inset-block-end: 0;
  }

  th,
  td {
    padding: 0px 20px;

    hr {
      height: 5px;
      background-image: url("../../../assets/images/tableDivider.webp");
      background-repeat: no-repeat;
      background-position: center center;
      background-size: contain;
    }
  }

  .sort-btn {
    outline: none !important;
    box-shadow: none !important;
  }

  .btn-transparent {
    @include mixins.btn-transparent;

    & {
      background-color: #ad986f;
      font-size: 11px;
      width: 115px;
      height: 30px;
    }

    @media only screen and (min-width: 1400px) and (max-width: 1599px) {
      width: 100%;
    }
  }
}
