.report-preview-modal {
  .modal-dialog {
    width: 100%;
    max-width: 900px;
    margin: auto;
  }

  .modal-content {
    border-radius: 12px;
    border: none;
    background-color: #fff;
    box-shadow: 0 12px 36px rgba(0, 0, 0, 0.15);
    max-height: 90vh;
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    position: relative;
    padding: 1.5rem 2rem 0.5rem;
    border: none;

    h3 {
      font-size: 1.75rem;
      font-weight: 700;
      color: #1f2937;
    }

    .btn-close {
      position: absolute;
      top: 1.25rem;
      right: 1.25rem;
      font-size: 1.25rem;
      color: #6b7280;
      background: transparent;
      border: none;

      &:hover {
        color: #111;
      }
    }
  }

  .modal-body {
    padding: 0 2rem 2rem;
    overflow-y: auto;
    flex: 1; // Makes it scrollable only within this area

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.15);
      border-radius: 4px;
    }
  }

  .report-preview-content {
    .preview-title {
      text-align: center;
      margin-bottom: 2rem;

      h1 {
        font-size: 2rem;
        font-weight: 700;
        color: #2d3748;
      }

      hr {
        width: 60px;
        height: 3px;
        background-color: #2563eb;
        border: none;
        margin: 1rem auto 0;
      }
    }

    .preview-sections {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .preview-section {
      background: #fdfdfd;
      border: 1px solid #e5e7eb;
      border-radius: 10px;
      padding: 1.75rem 1.5rem;
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      }

      .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 1rem;
      }

      .section-content {
        font-size: 1rem;
        color: #111827;
        line-height: 1.7;

        p {
          margin-bottom: 1rem;
        }

        table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 1rem;
          font-size: 0.95rem;

          th,
          td {
            padding: 0.75rem;
            border: 1px solid #d1d5db;
          }

          th {
            background-color: #f3f4f6;
            font-weight: 600;
          }
        }

        .graph-placeholder {
          border: 2px dashed #ad986f;
          border-radius: 8px;
          padding: 2rem;
          margin: 1rem 0;
          text-align: center;
          background-color: #f8f9fa;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba(173, 152, 111, 0.1);
            border-color: #8b7355;
          }

          .graph-placeholder-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
          }

          .graph-placeholder-icon {
            font-size: 2rem;
          }

          .graph-placeholder-text {
            font-weight: 600;
            color: #ad986f;
          }

          .graph-placeholder-subtitle {
            font-size: 0.875rem;
            color: #6c757d;
          }
        }

        blockquote {
          border-left: 4px solid #ad986f;
          margin: 1.5rem 0;
          padding: 1rem 1.5rem;
          background-color: #f8f9fa;
          font-style: italic;
          color: #6c757d;
  
          p {
            margin: 0;
          }
        }
      }
    }

    .text-muted {
      text-align: center;
      color: #9ca3af;
      font-style: italic;
      font-size: 1rem;
      padding: 2rem 0;
    }
  }
}
