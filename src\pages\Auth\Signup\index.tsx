import { Ri<PERSON><PERSON><PERSON>ine, RiUserLine } from "@remixicon/react";
import { useSignupMutation } from "api";
import { PasswordField } from "components";
import { SignupInitialValues } from "formSchema/initialValues";
import { SignupValidations } from "formSchema/schemaValidations";
import { ErrorMessage, Field, Form, Formik, FormikProps } from "formik";
import { SETTINGS } from "globals";
import { <PERSON><PERSON>, Spinner } from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { SignupInterface } from "types";
import "./styles.scss";

const Signup = () => {
  const { mutateAsync: signup } = useSignupMutation();

  const navigate = useNavigate();

  const handleSubmit = async (
    values: SignupInterface,
    { setSubmitting }: any,
  ) => {
    const { confirmPassword, agreeTerms, ...payload } = values;
    try {
      const response: any = await signup(payload);
      if (response?.success) {
        navigate(
          `${ROUTE_PATH.OTP}?id=${response?.data?.otpId}&email=${payload?.email}&type=signUp`,
        );
      }
    } catch (error) {
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };
  return (
    <div
      className="auth-form d-flex justify-content-center align-items-center flex-column sign-up-form"
      style={{ gap: "30px" }}
    >
      <div className="d-flex flex-column" style={{ gap: "23px" }}>
        <h1 className="auth-form-heading text-uppercase mb-3 text-center lh-1">
          Welcome to {SETTINGS.APP_NAME}
        </h1>
      </div>

      <div className="website-form">
        <Formik
          initialValues={SignupInitialValues}
          validationSchema={SignupValidations}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting }: FormikProps<any>) => (
            <Form className="d-flex flex-column">
              <div className="form-group position-relative">
                <label className="form-label">Full Name</label>
                <Field
                  name="full_name"
                  className="form-control"
                  placeholder="Enter your full name"
                />
                <ErrorMessage component={"span"} name="full_name" />

                <RiUserLine size={"20px"} color="#70828D" />
              </div>

              <div className="form-group position-relative">
                <label className="form-label">Email</label>
                <Field
                  name="email"
                  className="form-control"
                  placeholder="Enter your email"
                  type="email"
                />
                <ErrorMessage component={"span"} name="email" />

                <RiMailLine size={"20px"} color="#70828D" />
              </div>

              <div className="form-group position-relative">
                <PasswordField
                  label="Password"
                  fieldName="password"
                  placeholder="Enter your password"
                />
              </div>

              <div className="form-group position-relative">
                <PasswordField
                  label="Confirm Password"
                  fieldName="confirmPassword"
                  placeholder="Confirm your password"
                />
              </div>

              <div className="interaction-btns d-flex justify-content-between align-items-center position-relative">
                <div className="form-check form-check-inline me-0">
                  <Field
                    className="form-check-input"
                    type="checkbox"
                    id="formCheckbox"
                    name="agreeTerms"
                  />
                  <label
                    className="form-check-label font-gray fw-500"
                    htmlFor="formCheckbox"
                  >
                    By registering you accept our
                    <Link
                      to={ROUTE_PATH.TERMS}
                      className="ms-1 text-decoration-none fw-bold"
                      style={{ color: "#60A799" }}
                    >
                      Terms of Services
                    </Link>
                    {", "}
                    <Link
                      to={ROUTE_PATH.PRIVACY}
                      className="text-decoration-none fw-bold"
                      style={{ color: "#60A799" }}
                    >
                      Privacy Policy
                    </Link>
                    <small className="mx-1">&</small>
                    <Link
                      to={ROUTE_PATH.EULA}
                      className="text-decoration-none fw-bold"
                      style={{ color: "#60A799" }}
                    >
                      EULA
                    </Link>
                  </label>
                </div>
                <ErrorMessage component={"span"} name="agreeTerms" />
              </div>

              <div
                className="action-btns d-flex flex-column"
                style={{ gap: "30px" }}
              >
                <Button
                  type="submit"
                  className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? <Spinner /> : "Register"}
                </Button>
              </div>

              <div className="interaction-btns d-flex align-items-center font-gray">
                Already have an account?
                <Link
                  to={ROUTE_PATH.LOGIN}
                  className="mb-0 fw-bold text-decoration-none ms-1"
                  style={{ color: "#60A799" }}
                >
                  Login Now
                </Link>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default Signup;
