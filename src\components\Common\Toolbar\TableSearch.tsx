import { RiSearchLine } from "@remixicon/react";
import { Form } from "react-bootstrap";

interface TableSearchProps {
  filters?: any;
  setFilters: any;
  placeholder?: string;
}

const TableSearch = ({
  filters = {},
  setFilters,
  placeholder = "Search by Name or email",
}: TableSearchProps) => {
  return (
    <Form.Group
      className="mb-0 position-relative w-100"
      controlId="dashboard-search"
    >
      <Form.Control
        type="search"
        placeholder={placeholder}
        className="shadow-none"
        onChange={(e) => {
          setFilters((prev: any) => ({
            ...prev,
            search: e.target.value,
          }));
        }}
        value={filters?.search || ""}
      />

      <RiSearchLine
        color="#70828D"
        size={18}
        className="position-absolute search-floating-icon translate-middle-y top-50"
      />
    </Form.Group>
  );
};

export default TableSearch;
