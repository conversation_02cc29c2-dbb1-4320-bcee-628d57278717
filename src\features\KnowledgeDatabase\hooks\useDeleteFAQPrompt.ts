import toast from "react-hot-toast";
import {
    setConfirmModalConfig
} from "stores";
import { IDType } from "types";
import { useDeleteFAQPrompt } from "../api";
import { useInvalidateQuery } from "hooks";

const useDeleteFAQPromptHook = () => {
    const { mutateAsync: deleteFAQPrompt } = useDeleteFAQPrompt();
    const [invalidateQueries] = useInvalidateQuery();

    const handleDelete = async (itemId: IDType) => {
        try {
            const result: any = await deleteFAQPrompt({ id: itemId });
            if (result?.success) {
                toast.success(result?.message || "FAQ Prompt Deleted Successfully!");
                invalidateQueries(['faq-prompts']);
            }
        } catch (err: any) {
            console.log(err);
            toast.error("Failed to delete FAQ prompt");
        }
    };

    const onClickDelete = (itemId: IDType) => {
        setConfirmModalConfig({
            visible: true,
            data: {
                onSubmit: () => handleDelete(itemId),
                content: {
                    heading: "Delete FAQ Prompt",
                    description: "Are you sure you want to delete this FAQ prompt?",
                },
            },
        });
    };

    return { onClickDelete };
};

export default useDeleteFAQPromptHook;
