@use "/src/styles/mixins/mixins.scss" as mixins;

.doc-settings {
  .font-selection {
    margin-bottom: 20px;
  }

  .logo-selection {
    margin-bottom: 20px;

    .logo-card {
      border: 1px solid #ccc;
      border-radius: 10px;
      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
      padding: 20px;
      background-color: #fff;
      flex: 1;

      .card-body {
        min-height: 150px;
      }
    }
  }

  .preview-area {
    .heading {
      font-size: 20px;
      font-weight: 700;
      color: #0d3149;
    }

    .preview-content {
      border: 1px solid #ccc;
      padding: 20px;
      background: white;
      border-radius: 5px;
      box-shadow: 0px 10px 20px rgba(26, 26, 26, 0.1);

      div {
        gap: 50px;
      }

      .preview-header-vr {
        width: 1px;
        margin: 10px 0;
        opacity: 1;
        background-color: #000;
      }

      .logo-preview.primary {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #ff69b4, #ffa500);
        margin-bottom: 10px;
      }

      .company-name {
        font-size: 18px;
        font-weight: 500;
        color: #333;
        margin-bottom: 10px;
      }

      .logo-img {
        width: 100px;
        height: 100px;
      }

      .company-name-img {
        max-width: 300px;
        width: 100%;
        height: auto;
      }

      .text-lines {
        .line {
          height: 10px;
          background: #e0e0e0;
          margin-bottom: 5px;
        }
      }

      .title-placeholder {
        font-family: Urbanist, sans-serif;
      }
    }
  }

  @media only screen and (max-width: 576px) {
    .logo-selection {
      .heading {
        font-size: 18px;
      }
      .logo-placeholder {
        width: 80px;
        height: 80px;
        &.secondary {
          font-size: 14px;
        }
      }
    }

    .preview-area {
      .heading {
        font-size: 18px;
      }
      .preview-content {
        padding: 10px;
        div {
          gap: 10px;
        }
        .logo-preview.primary {
          width: 40px;
          height: 40px;
        }
        .company-name {
          font-size: 16px;
        }
        .logo-img {
          width: 50px;
          height: 50px;
        }
        .company-name-img {
          max-width: 150px;
        }
        .text-lines .line {
          height: 8px;
        }
      }
    }
  }
}
