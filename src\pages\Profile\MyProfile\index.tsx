import { Ri<PERSON>ailLine, RiUserLine } from "@remixicon/react";
import { useUpdateProfileMutation } from "api";
import { BackBreadcrumb } from "components";
import VerifyOTPModal from "components/Common/Modals/VerifyOTPModal";
import { ErrorMessage, Field, Form, Formik, FormikProps } from "formik";
import { ProfileInitialValues } from "formSchema/initialValues/user";
import { ProfileValidations } from "formSchema/schemaValidations/user";
import { useState } from "react";
import { But<PERSON>, Card, Col, Container, Row, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";
import useUserStore, { setUserInfo } from "stores/user";
import { fillValues } from "utils";
import "../styles.scss";
import ProfilePicture from "./ProfilePicture";

const MyProfile = () => {
  const [showModal, setShowModal] = useState<boolean>(false);
  const userInfo = useUserStore((state) => state.userInfo);
  const { mutateAsync: updateProfile } = useUpdateProfileMutation();

  const handleClose = () => setShowModal(false);
  const handleShow = () => setShowModal(true);

  const handleSubmit = async (values: any, { setSubmitting }: any) => {
    try {
      setSubmitting(true);
      const formData = new FormData();

      Object.keys(values).forEach((key) => {
        formData.append(key, values[key]);
      });
      const response: any = await updateProfile(formData);
      if (response?.success) {
        toast.success(response?.message);
        setUserInfo({
          ...userInfo,
          user: {
            ...userInfo?.user,
            ...response?.data,
          },
        });
      }
    } catch (error: any) {
      console.error(error?.response?.data?.message);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <main className="profile-section d-flex bg-white flex-column align-items-stretch w-100">
      <BackBreadcrumb />

      <div className="profile-section-form-container">
        <Container fluid>
          <Row className="justify-content-center align-items-center">
            <Col lg="9" xxl="7">
              <div className="auth-form w-100 m-0">
                <Card className="auth-form-card justify-content-center aligh-items-center">
                  <Card.Body className="d-flex gap-4 flex-column align-items-center justify-content-center">
                    <h1 className="auth-form-heading text-uppercase">
                      My Profile
                    </h1>

                    <div className="website-form w-100">
                      <Formik
                        initialValues={{
                          ...fillValues(ProfileInitialValues, userInfo?.user),
                        }}
                        enableReinitialize
                        validationSchema={ProfileValidations}
                        onSubmit={handleSubmit}
                      >
                        {({
                          isSubmitting,
                          setFieldValue,
                          values,
                        }: FormikProps<any>) => (
                          <Form
                            className="d-flex flex-column"
                            style={{ gap: "30px" }}
                          >
                            <ProfilePicture
                              userPicture={values?.profile_photo}
                              setFieldValue={setFieldValue}
                              title={userInfo?.user?.full_name || ""}
                            />

                            <div className="form-group position-relative">
                              <label className="form-label">Full Name</label>
                              <Field
                                name="full_name"
                                className="form-control"
                                placeholder="Enter your full name"
                                type="text"
                                autoFocus
                              />
                              <ErrorMessage
                                component={"span"}
                                name="full_name"
                              />
                              <RiUserLine size={"20px"} color="#70828D" />
                            </div>

                            <div className="form-group position-relative">
                              <div className="d-flex justify-content-between align-items-center">
                                <label className="form-label">Email</label>
                                {/* <Link
                                  to={"javascript:void(0);"}
                                  onClick={handleShow}
                                  className="text-decoration-none text-success fw-bold"
                                >
                                  Verify Email
                                  <RiVerifiedBadgeLine
                                    className="text-success position-static ms-1"
                                    style={{
                                      transform: "none",
                                      verticalAlign: "text-bottom",
                                    }}
                                    size={"20px"}
                                  />
                                </Link> */}
                              </div>
                              <Field
                                name="email"
                                className="form-control text-dark opacity-75"
                                placeholder="Enter your email"
                                type="email"
                                disabled
                              />
                              <RiMailLine size={"20px"} color="#70828D" />
                            </div>

                            <div
                              className="action-btns d-flex flex-column"
                              style={{ gap: "30px" }}
                            >
                              <Button
                                type="submit"
                                className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                                disabled={isSubmitting}
                              >
                                {isSubmitting ? <Spinner /> : "Update"}
                              </Button>
                            </div>
                          </Form>
                        )}
                      </Formik>
                    </div>
                  </Card.Body>
                </Card>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      <VerifyOTPModal
        show={showModal}
        handleClose={handleClose}
        handleShow={handleShow}
      />
    </main>
  );
};

export default MyProfile;
