import { IMAGE_PATH } from "globals";
import { Form, Image } from "react-bootstrap";
import { getAppOriginURL } from "utils";

const ErrorFallback = ({
  title = "Something went wrong!",
  description = "Please try refreshing the page or come back later.",
}: any) => {
  return (
    <section className="auth-layout d-flex flex-lg-row flex-column align-items-stretch justify-content-between">
      <div className="auth-background">
        <div className="auth-website-logo">
          <a href={getAppOriginURL()}>
            <Image
              src={IMAGE_PATH.websiteLogo}
              className="w-100 h-100 object-fit-contain"
              alt="logo"
            />
          </a>
        </div>

        <div className="auth-background-featureImg position-absolute translate-middle-y d-lg-block d-none">
          <Image
            src={IMAGE_PATH.authFeatureImg}
            className="w-100 h-100 object-fit-contain"
            alt="feature"
          />
        </div>
      </div>

      <div
        className="auth-form d-flex justify-content-center align-items-center flex-column login-form̥"
        style={{ gap: "30px" }}
      >
        <div className="d-flex flex-column" style={{ gap: "23px" }}>
          <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
            {title}
          </h1>

          <p className="mb-0 auth-form-description font-gray text-center">
            {description}
          </p>
        </div>

        <div className="website-form">
          <Form className="d-flex flex-column" style={{ gap: "30px" }}>
            <div
              className="action-btns d-flex flex-column"
              style={{ gap: "30px" }}
            >
              <a
                href={getAppOriginURL()}
                className="d-flex justify-content-center align-items-center text-decoration-none submit-btn position-relative w-100 border-brown bg-transparent text-uppercase font-primary"
              >
                Go to home
              </a>
            </div>
          </Form>
        </div>
      </div>
    </section>
  );
};

export default ErrorFallback;
