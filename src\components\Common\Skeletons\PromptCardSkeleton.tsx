import { Card, Placeholder } from "react-bootstrap";

const PromptCardSkeleton = () => {
  return (
    <Card className="m-0 p-0 position-relative">
      <Card.Body className="cursor-pointer">
        <Placeholder as="div" animation="glow" className="mb-2">
          <Placeholder
            xs={2}
            style={{
              height: "30px",
              width: "30px",
              borderRadius: "50%",
            }}
          />
        </Placeholder>

        <Placeholder as="p" animation="glow" className="mb-0">
          <Placeholder xs={6} />
        </Placeholder>
        <Placeholder as="p" animation="glow" className="mb-0">
          <Placeholder xs={6} />
        </Placeholder>
      </Card.Body>
    </Card>
  );
};

export default PromptCardSkeleton;
