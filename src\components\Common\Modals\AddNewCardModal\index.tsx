import { useConfigQuery } from "api";
import CardForm from "pages/PaymentCards/SaveCard/CardForm";
import { RiCloseLine } from "@remixicon/react";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import React, { useEffect, useState } from "react";
import { Button, Modal } from "react-bootstrap";
import "../styles.scss";
import toast from "react-hot-toast";
import { useInvalidateQuery } from "hooks";

interface AddNewCardModalProps {
  show: boolean;
  onClose: () => void;
}

const AddNewCardModal: React.FC<AddNewCardModalProps> = ({ show, onClose }) => {
  const [stripePromise, setStripePromise] = useState<any>(null);

  const [invalidateQueries] = useInvalidateQuery();

  const { data: { publishableKey = null } = {} }: any = useConfigQuery();

  useEffect(() => {
    if (publishableKey) {
      setStripePromise(loadStripe(publishableKey));
    }
  }, [publishableKey]);

  const onSuccess = () => {
    onClose();
    toast.success("Card added successfully");
    invalidateQueries(["cards"]);
  };

  return (
    <Modal
      show={show}
      onHide={onClose}
      keyboard={false}
      centered
      className="add-payment-card-modal"
    >
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={onClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>

        <div
          className="auth-form d-flex justify-content-center align-items-stretch flex-column pass-reset-form"
          style={{ gap: "25px" }}
        >
          <div className="d-flex flex-column" style={{ gap: "15px" }}>
            <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
              Add card
            </h1>
          </div>

          <div className="website-form w-100">
            {stripePromise && (
              <Elements stripe={stripePromise}>
                <CardForm onSuccess={onSuccess} />
              </Elements>
            )}
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default AddNewCardModal;
