import { useDeleteOrgPromptMutation, useUpdateOrgPromptMutation } from "api";
import useInvalidateQuery from "hooks/useInvalidateQuery";
import toast from "react-hot-toast";
import { setConfirmModalConfig, setOrganisation } from "stores";
import { convertObjToFormData } from "utils";

const useUpdateOrgPrompt = () => {
  const { mutateAsync: deletePrompt } = useDeleteOrgPromptMutation();
  const { mutateAsync: updatePrompt } = useUpdateOrgPromptMutation();
  const [invalidateQueries] = useInvalidateQuery();

  const handleReset = async ({
    organisation,
    prompt_id,
    onCloseModal,
  }: any) => {
    try {
      const response: any = await deletePrompt({
        org_id: organisation?.id,
        prompt_id,
      });
      if (response?.success) {
        toast.success(response?.message);
        const updatedOrg = {
          ...organisation,
          prompts: organisation.prompts.filter(
            (prompt: any) => prompt.id !== prompt_id,
          ),
        };
        setOrganisation(updatedOrg);
        invalidateQueries(["custom-prompts"]);
        onCloseModal();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onClickSubmit = async ({ organisation, prompt_data }: any) => {
    try {
      const payloadObj = {
        org_id: organisation?.id,
        payload: convertObjToFormData({
          prompt: prompt_data?.prompt,
          title: prompt_data?.title,
          icon: prompt_data?.icon,
          file: prompt_data?.file,
          prompt_id: prompt_data?.id,
        }),
      };
      const response: any = await updatePrompt(payloadObj);
      if (response?.success) {
        toast.success(response?.message);
        const updatedOrg = {
          ...organisation,
          prompts: response?.data,
        };
        setOrganisation(updatedOrg);
        invalidateQueries(["custom-prompts"]);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onClickReset = ({
    evt,
    organisation,
    prompt_id,
    promptModalConfig,
  }: any) => {
    evt.stopPropagation();
    const { onOpenModal, onCloseModal } = promptModalConfig || {};
    onCloseModal();
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleReset({ organisation, prompt_id, onCloseModal }),
        onClose: () => onOpenModal(),
        content: {
          heading: "Reset Prompt?",
          description: "Are you sure you want to reset this prompt?",
        },
      },
    });
  };

  return { onClickReset, onClickSubmit };
};

export default useUpdateOrgPrompt;
