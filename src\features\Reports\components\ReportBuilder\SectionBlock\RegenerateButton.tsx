import { RiAiGenerate2 } from "@remixicon/react";
import { useRegenerateSection } from "features/Reports/api";
import { updateReportBlockContent } from "features/Reports/store";
import { useCallback, useState } from "react";
import { But<PERSON>, Spinner } from "react-bootstrap";
import { setConfirmModalConfig } from "stores";
import { getTimezone } from "utils";

interface RegenerateButtonProps {
  section_id: number;
  source_section_id: number | undefined;
  index: number;
}

const MODAL_CONFIG = {
  CONFIRMATION: {
    icon: RiAiGenerate2,
    iconColor: "#ad986f",
    content: {
      heading: "Regenerate?",
      description: "Are you sure you want to regenerate this section?",
    },
    buttonText: "Regenerate",
  },
  LOADING: {
    icon: () => <Spinner animation="border" />,
    iconColor: "#ad986f",
    content: {
      heading: "Regenerating...",
      description: "Please wait while we regenerate the section content.",
    },
    showCloseIcon: false,
    buttonText: "Regenerating...",
    showSubmitButton: false,
  },
} as const;

const RegenerateButton = ({
  section_id,
  source_section_id,
  index,
}: RegenerateButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const { mutateAsync: regenerateSection } = useRegenerateSection();

  const closeModal = useCallback(() => {
    setConfirmModalConfig({
      visible: false,
      data: MODAL_CONFIG.LOADING,
    });
  }, []);

  const showLoadingModal = useCallback(() => {
    setConfirmModalConfig({
      visible: true,
      data: MODAL_CONFIG.LOADING,
    });
  }, []);

  const performRegeneration = useCallback(
    async ({ source_section_id, section_id, index }: any) => {
      try {
        const payload = {
          section_id,
          source_section_id,
          timezone: getTimezone(),
        };
        const response: any = await regenerateSection(payload);

        if (response?.success) {
          updateReportBlockContent(index, response.data.content);
          setTimeout(closeModal, 1000);
        }
      } catch (error) {
        console.error("Regeneration failed:", error);
        closeModal();
      } finally {
        setIsLoading(false);
      }
    },
    [index, closeModal]
  );

  const handleRegenerateConfirm = useCallback(
    (payload: any) => {
      setIsLoading(true);

      setTimeout(() => {
        showLoadingModal();
        performRegeneration(payload);
      }, 50);
    },
    [showLoadingModal, performRegeneration]
  );

  const handleClick = useCallback(() => {
    if (!source_section_id) return;

    setConfirmModalConfig({
      visible: true,
      data: {
        ...MODAL_CONFIG.CONFIRMATION,
        onSubmit: () =>
          handleRegenerateConfirm({ source_section_id, section_id, index }),
      },
    });
  }, [source_section_id, isLoading, handleRegenerateConfirm]);

  const isDisabled = !source_section_id || isLoading;
  const buttonTitle = source_section_id ? "Re-Generate Section with AI" : "Re-Generate Section with AI";

  return (
    <Button
      variant=""
      onClick={handleClick}
      title={buttonTitle}
      className={`p-0 px-1 ${isDisabled ? "opacity-50" : ""}`}
    >
      <RiAiGenerate2 color="#ad986f" />
    </Button>
  );
};

export default RegenerateButton;
