import { useMutation } from "@tanstack/react-query";
import auth0, { Auth0Error } from "auth0-js";
import { AUTH0_INFO } from "globals";
import { API_ENDPOINTS } from "globals/endpoints";
import { apiClient } from "./apiClient";
import toast from "react-hot-toast";
import {
  ChangePasswordInterface,
  ForgotInterface,
  ResetPasswordPayloadInterface,
  SignupInterface,
} from "types";

interface LoginPayloadInterface {
  email: string;
  password: string;
}

interface VerifyOTPInterface {
  payload: {
    id: string | null;
    code: string;
  };
  url: string;
}

interface ResendOTPInterface {
  id: string | null;
}

export const useLoginMutation = (payload: LoginPayloadInterface) => {
  const {
    AUTH0_DOMAIN,
    AUTH0_CLIENT_ID,
    AUTH0_SCOPE,
    AUTH0_REALM,
    AUTH0_LOGIN_REDIRECT_URI,
    AUTH0_LOGIN_RESPONSE_TYPE,
    AUTH0_AUDIENCE,
  } = AUTH0_INFO ?? {};

  const { email: username, password } = payload ?? {};

  const webAuth = new auth0.WebAuth({
    domain: AUTH0_DOMAIN,
    clientID: AUTH0_CLIENT_ID,
    scope: AUTH0_SCOPE,
  });

  try {
    webAuth.login(
      {
        username,
        password,
        realm: AUTH0_REALM,
        redirectUri: AUTH0_LOGIN_REDIRECT_URI,
        responseType: "code", // Changed to code to get refresh token
        audience: AUTH0_AUDIENCE,
      },
      (err: Auth0Error | null) => {
        if (err) {
          if (err?.code === "blocked_user" || err?.error === "blocked_user") {
            toast.error(
              <div>
                Your account has been inactivated. Please contact{" "}
                <a href="mailto:<EMAIL>">
                  <EMAIL>
                </a>
              </div>,
              {
                id: "generic-error",
                duration: 15000,
              },
            );
            return;
          }
          toast.error(err?.description ?? "Error during login", {
            id: err?.description ?? "generic-error",
          });
          return;
        }
      },
    );
  } catch (error) {
    console.error("Error during login:", error);
  }
};

export const useSignupMutation = () =>
  useMutation({
    mutationFn: async (payload: SignupInterface) => {
      const response = await apiClient.post(API_ENDPOINTS.SIGNUP, payload);
      return response;
    },
  });

export const useVerifyOTPMutation = () =>
  useMutation({
    mutationFn: async (data: VerifyOTPInterface) => {
      const response = await apiClient.post(data?.url, data?.payload);
      return response;
    },
  });

export const useResendOTPMutation = () =>
  useMutation({
    mutationFn: async (payload: ResendOTPInterface) => {
      const response = await apiClient.post(API_ENDPOINTS.RESEND_OTP, payload);
      return response;
    },
  });

export const useResendOTPEmailMutation = () =>
  useMutation({
    mutationFn: async (payload: ResendOTPInterface) => {
      const response = await apiClient.post(
        API_ENDPOINTS.RESEND_OTP_EMAIL,
        payload,
      );
      return response;
    },
  });

export const useForgotPasswordMutation = () =>
  useMutation({
    mutationFn: async (payload: ForgotInterface) => {
      const response = await apiClient.post(
        API_ENDPOINTS.FORGOT_PASSWORD,
        payload,
      );
      return response;
    },
  });

export const useResetPasswordMutation = () =>
  useMutation({
    mutationFn: async (payload: ResetPasswordPayloadInterface) => {
      const response = await apiClient.post(
        API_ENDPOINTS.RESET_PASSWORD,
        payload,
      );
      return response;
    },
  });

export const useChangePasswordMutation = () =>
  useMutation({
    mutationFn: async (
      payload: Omit<ChangePasswordInterface, "confirmPassword">,
    ) => {
      const response = await apiClient.post(
        API_ENDPOINTS.CHANGE_PASSWORD,
        payload,
      );
      return response;
    },
  });
