import { useInvalidateQuery } from "hooks";
import toast from "react-hot-toast";
import { useUpdatePermissionGroup } from "../api";

const useHideOrShowDocs = () => {
  const [invalidateQueries] = useInvalidateQuery();
  const { mutateAsync: updatePermissionGroup } = useUpdatePermissionGroup();

  const handleHideOrShow = async ({ itemInfo }: { itemInfo: any }) => {
    try {
      const params = {
        id: itemInfo?.id,
        access_control: {
          ...itemInfo?.access_control,
          visible: !itemInfo?.access_control?.visible,
        },
      };
      const result: any = await updatePermissionGroup(params);
      if (result?.success) {
        toast.success(result?.message || "Visibility Changed Successfully!");
        invalidateQueries(["knowledge-base"]);
      }
    } catch (err: any) {
      console.log(err);
    }
  };

  const onClickHideOrShow = ({
    // type,
    itemInfo,
  }: {
    // type: any;
    itemInfo: any;
  }) => {
    handleHideOrShow({ itemInfo });
    // setConfirmModalConfig({
    //   visible: true,
    //   data: {
    //     onSubmit: () => handleHideOrShow({ itemInfo }),
    //     content: {
    //       heading: `${type === "hide" ? "Hide" : "Show"} Item?`,
    //       description: `Are you sure you want to ${type} this item?`,
    //     },
    //     icon: type === "hide" ? RiEyeOffFill : RiEyeFill,
    //     iconColor: "#ad986f",
    //   },
    // });
  };

  return { onClickHideOrShow };
};

export default useHideOrShowDocs;
