import { RiDownload2Line, RiUserLine } from "@remixicon/react";
import { BackBreadcrumb } from "components";
import { useOrganisationProfile } from "hooks/organisation/useOrganisationProfile";
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Container,
  Form,
  Row,
  Spinner,
} from "react-bootstrap";
import { ROUTE_PATH } from "routes";
import ProfilePicture from "../MyProfile/ProfilePicture";
import "../styles.scss";
import OrganisationPrompts from "./OrganisationPrompts";
import OrganisationSettings from "./OrganisationSettings";

const OrganisationalProfile = () => {
  const {
    formik,
    organisation,
    orgConfig,
    setOrgConfig,
    onSavePrompt,
    onResetPrompt,
    onLocalReset,
    getLocalPrompts,
    handleSwitchChange,
    ORGANISATION_SETTINGS,
    navigate,
    handleSaveDraft,
  } = useOrganisationProfile();

  const renderError = (field: string) => {
    if (formik.touched[field] && formik.errors[field]) {
      return <span className="mb-2">{formik.errors[field]}</span>;
    }
  };
  return (
    <main className="profile-section d-flex bg-white flex-column align-items-stretch w-100">
      <BackBreadcrumb />

      <div className="profile-section-form-container">
        <Container fluid>
          <Row className="justify-content-center align-items-center">
            <Col lg="9" xxl="7">
              <div className="auth-form w-100 m-0">
                <Card className="auth-form-card justify-content-center aligh-items-center">
                  <Card.Body className="d-flex gap-4 flex-column align-items-center justify-content-center">
                    <h1 className="auth-form-heading text-uppercase">
                      Organisational Profile
                    </h1>

                    <div className="website-form w-100">
                      <Form
                        className="d-flex flex-column"
                        style={{ gap: "30px" }}
                        onSubmit={formik.handleSubmit}
                      >
                        <ProfilePicture
                          userPicture={formik.values.logo}
                          setFieldValue={formik.setFieldValue}
                          title={organisation?.title || ""}
                          name="logo"
                        />

                        <div className="form-group position-relative">
                          <label className="form-label fw-bold">
                            Organisation Name
                          </label>
                          <Form.Group className="m-0 p-0 position-relative w-100">
                            <Form.Control
                              type="text"
                              placeholder="Enter Organisation Name"
                              {...formik.getFieldProps("title")}
                            />
                            <div className="d-flex flex-column mt-2">
                              {renderError("title")}
                            </div>
                          </Form.Group>
                          <RiUserLine size={"20px"} color="#70828D" />
                        </div>

                        <OrganisationPrompts
                          orgConfig={orgConfig}
                          setOrgConfig={setOrgConfig}
                          organisation={organisation}
                          onSavePrompt={onSavePrompt}
                          onResetPrompt={onResetPrompt}
                          onLocalReset={onLocalReset}
                          getLocalPrompts={getLocalPrompts}
                        />

                        <div className="form-group position-relative">
                          <div className="d-flex justify-content-between align-items-center">
                            <label className="form-label fw-bold">
                              Export Document
                            </label>
                            <Form.Switch
                              checked={orgConfig.export_settings}
                              onChange={(e) =>
                                setOrgConfig({
                                  ...orgConfig,
                                  export_settings: e.target.checked,
                                })
                              }
                            />
                          </div>
                        </div>
                        <div
                          className="prompts-wrapper d-flex align-items-start justify-content-start w-100 pb-2"
                          style={{ position: "unset" }}
                        >
                          <Card className="m-0 p-0">
                            <Card.Body
                              onClick={() => {
                                handleSaveDraft();
                                navigate(
                                  `${ROUTE_PATH.DOC_SETTINGS}?type=organisation${organisation?.id ? `&orgId=${organisation?.id}` : ""}`,
                                );
                              }}
                              className="cursor-pointer"
                            >
                              <RiDownload2Line
                                size={30}
                                className="prompt-icon"
                              />
                              <p className={"mb-0"}>Set Export Settings</p>
                            </Card.Body>
                          </Card>
                        </div>

                        <OrganisationSettings
                          formik={formik}
                          handleSwitchChange={handleSwitchChange}
                          ORGANISATION_SETTINGS={ORGANISATION_SETTINGS}
                        />

                        <div
                          className="action-btns d-flex flex-column"
                          style={{ gap: "30px" }}
                        >
                          <Button
                            type="submit"
                            className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                            disabled={formik.isSubmitting}
                          >
                            {formik.isSubmitting ? (
                              <Spinner />
                            ) : organisation?.id ? (
                              "Update"
                            ) : (
                              "Create"
                            )}
                          </Button>
                        </div>
                      </Form>
                    </div>
                  </Card.Body>
                </Card>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </main>
  );
};

export default OrganisationalProfile;
