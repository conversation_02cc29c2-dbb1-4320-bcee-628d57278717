import { RiArticleLine, RiCheckboxCircleFill } from "@remixicon/react";
import { useAcceptOfflineAccountTerms, useOfflineAccountTerms } from "api";
import { BackButton } from "components";
import { ErrorMessage, Field, Form, Formik, FormikProps } from "formik";
import { IMAGE_PATH, SETTINGS } from "globals";
import { useEffect, useState } from "react";
import { Button, Col, Container, Image, Row, Spinner } from "react-bootstrap";
import { Link, useSearchParams } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import * as Yup from "yup";
import "./styles.scss";

const Acknowledge = () => {
  const [searchParams] = useSearchParams();
  const account_acknowledge_token = searchParams.get("token");
  const termsheet = searchParams.get("termsheet");
  const [activationToken, setActivationToken] = useState<string | null>(null);

  const CONTENT_CONFIG = {
    loading: {
      heading: "Just a moment...",
      description: "",
    },
    verified: {
      heading: `Welcome to ${SETTINGS.APP_NAME}`,
    },
    error: {
      heading: "Something went wrong!",
      description: "Please try again later or contact support.",
    },
    success: {
      heading: "Terms Acknowledged",
      description: termsheet
        ? "Thank you for accepting the new terms."
        : "Please proceed by clicking the 'Setup Profile' button.",
    },
  };

  const { mutateAsync: acceptOfflineAccountTerms } =
    useAcceptOfflineAccountTerms();

  const {
    data: { terms_sheet = "", initial_invoice = "", username = "" } = {},
    isLoading: isDataLoading,
  } = useOfflineAccountTerms({
    token: account_acknowledge_token,
  });

  const [displayState, setDisplayState] = useState<
    "loading" | "verified" | "error" | "success"
  >("loading");

  useEffect(() => {
    if (isDataLoading) {
      setDisplayState("loading");
      return;
    }

    const timer = setTimeout(() => {
      if (terms_sheet && initial_invoice) {
        setDisplayState("verified");
      } else {
        setDisplayState("error");
      }
    }, 1500);

    return () => clearTimeout(timer);
  }, [isDataLoading, terms_sheet, initial_invoice]);

  const handleSubmit = async (
    _values: { agreeTerms: boolean },
    { setSubmitting }: any,
  ) => {
    try {
      setSubmitting(true);
      const payload = {
        account_acknowledge_token,
        terms_sheet: termsheet ? Boolean(termsheet) : undefined,
      };
      const result: any = await acceptOfflineAccountTerms({ payload });
      if (result?.success) {
        setDisplayState("success");
        if (!termsheet) {
          setActivationToken(
            result?.data?.offline_billing_setting?.activation_token
          );
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };

  const formatPolicyPath = (path: string) => {
    return `${path}?source=redirect`;
  };

  const renderLoadingState = () => (
    <div
      className="d-flex flex-column justify-content-center align-items-center"
      style={{ gap: "23px" }}
    >
      <Spinner />
      <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
        {CONTENT_CONFIG.loading.heading}
      </h1>
      <p className="mb-0 auth-form-description font-gray text-center">
        {CONTENT_CONFIG.loading.description}
      </p>
    </div>
  );

  const renderVerifiedState = () => (
    <>
      <div className="d-flex flex-column" style={{ gap: "23px" }}>
        <h1 className="auth-form-heading text-uppercase mb-3 text-center lh-1">
          {CONTENT_CONFIG.verified.heading}
        </h1>
      </div>
      <div className="website-form">
        <Formik
          initialValues={{
            agreeTerms: false,
          }}
          validationSchema={Yup.object().shape({
            agreeTerms: Yup.boolean().oneOf(
              [true],
              "You must agree to the terms.",
            ),
          })}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting }: FormikProps<any>) => (
            <Form className="d-flex flex-column">
              <h4 className="text-center">{username}</h4>
              <Container className="text-center">
                <Row className="justify-content-center gap-lg-4">
                  {terms_sheet && (
                    <Col
                      xs={12}
                      md={6}
                      lg={4}
                      className="d-flex flex-column align-items-center"
                    >
                      <a
                        href={terms_sheet}
                        target="_blank"
                        rel="noreferrer"
                        style={{
                          textDecoration: "none",
                          color: "#0d3149",
                        }}
                      >
                        <RiArticleLine size={250} />
                        <p className="fw-bold text-decoration-underline">
                          Organisational Terms
                        </p>
                      </a>
                    </Col>
                  )}
                  {initial_invoice && !termsheet && (
                    <Col
                      xs={12}
                      md={6}
                      lg={4}
                      className="d-flex flex-column align-items-center"
                    >
                      <a
                        href={initial_invoice}
                        target="_blank"
                        rel="noreferrer"
                        style={{
                          textDecoration: "none",
                          color: "#0d3149",
                        }}
                      >
                        <RiArticleLine size={250} />
                        <p className="fw-bold text-decoration-underline">
                          Payment Details
                        </p>
                      </a>
                    </Col>
                  )}
                </Row>
              </Container>

              <div className="interaction-btns d-flex justify-content-center align-items-center position-relative">
                <div className="form-check form-check-inline me-0">
                  <Field
                    className="form-check-input"
                    type="checkbox"
                    id="formCheckbox"
                    name="agreeTerms"
                  />
                  <label
                    className="form-check-label font-gray fw-500"
                    htmlFor="formCheckbox"
                  >
                    By registering you accept our
                    <Link
                      to={formatPolicyPath(ROUTE_PATH.TERMS)}
                      className="ms-1 text-decoration-none fw-bold"
                      style={{ color: "#60A799" }}
                    >
                      Terms of Services
                    </Link>
                    {", "}
                    <Link
                      to={formatPolicyPath(ROUTE_PATH.PRIVACY)}
                      className="text-decoration-none fw-bold"
                      style={{ color: "#60A799" }}
                    >
                      Privacy Policy
                    </Link>
                    <small className="mx-1">&</small>
                    <Link
                      to={formatPolicyPath(ROUTE_PATH.EULA)}
                      className="text-decoration-none fw-bold"
                      style={{ color: "#60A799" }}
                    >
                      EULA
                    </Link>
                  </label>
                </div>
                <ErrorMessage component={"span"} name="agreeTerms" />
              </div>

              <div
                className="action-btns d-flex flex-column"
                style={{ gap: "30px" }}
              >
                <Button
                  type="submit"
                  className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? <Spinner /> : "Acknowledge"}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </>
  );

  const renderErrorState = () => (
    <>
      <div className="d-flex flex-column" style={{ gap: "23px" }}>
        <div className="text-center">
          <Image src={IMAGE_PATH.redErrorIcon} style={{ width: "120px" }} />
        </div>
        <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
          {CONTENT_CONFIG.error.heading}
        </h1>
        <p className="mb-0 auth-form-description font-gray text-center">
          {CONTENT_CONFIG.error.description}
        </p>
      </div>
      <div className="website-form">
        <div className="action-btns d-flex flex-column" style={{ gap: "30px" }}>
          <BackButton title="Back To Home" url={ROUTE_PATH.LOGIN} />
        </div>
      </div>
    </>
  );

  const renderSuccessState = () => (
    <>
      <div className="d-flex flex-column" style={{ gap: "23px" }}>
        <div className="text-center">
          <RiCheckboxCircleFill size={"150px"} color="#ad986f" />
        </div>
        <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
          {CONTENT_CONFIG.success.heading}
        </h1>
        <p className="mb-0 auth-form-description font-gray text-center">
          {CONTENT_CONFIG.success.description}
        </p>
      </div>
      <div className="website-form">
        <div className="action-btns d-flex flex-column" style={{ gap: "30px" }}>
          {termsheet ? (
            <BackButton title="Back To Home" url={ROUTE_PATH.LOGIN} />
          ) : (
            <Link
              to={`${ROUTE_PATH.VERIFY_OFFLINE_ACCOUNT}?token=${activationToken}`}
              className="text-decoration-none d-flex justify-content-center align-items-center submit-btn position-relative w-100 border-brown bg-brown text-uppercase text-light z-3"
            >
              Setup Profile
            </Link>
          )}
        </div>
      </div>
    </>
  );

  return (
    <div
      className="auth-form d-flex justify-content-center align-items-center flex-column sign-up-form"
      style={{ gap: "30px" }}
    >
      {displayState === "loading" && renderLoadingState()}
      {displayState === "verified" && renderVerifiedState()}
      {displayState === "error" && renderErrorState()}
      {displayState === "success" && renderSuccessState()}
    </div>
  );
};

export default Acknowledge;
