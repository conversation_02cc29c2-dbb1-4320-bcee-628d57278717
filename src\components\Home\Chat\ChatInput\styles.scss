@use "/src/styles/mixins/mixins.scss" as mixins;

.form {
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  border: 1px solid rgba(26, 26, 26, 0.1) !important;
  border-radius: 10px;

  textarea {
    padding: 17px 45px 15px 120px;
    box-shadow: none !important;
    background: transparent !important;
    resize: none;
    letter-spacing: 0.6px;

    @include mixins.slim-scrollbar;

    &:focus,
    &:hover {
      background: transparent !important;
    }

    &::placeholder {
      color: rgba(26, 26, 26, 0.2);
      font-weight: 600;
    }
  }

  .file-input-label {
    left: 10px;
    bottom: 14px;

    &.camera {
      left: 45px;
    }

    &.voice {
      left: 80px;
    }
  }

  .chat-submit-btn {
    right: 10px;
    bottom: 11px;
  }

  &-file-output-wrapper {
    .remove-btn {
      border: 1px solid #0d3149;
      background-color: #b5bdc3;
      width: 20px;
      height: 20px;
      line-height: 1px;
      top: -5px;
      right: -10px;
      z-index: 1;
      transition: all 0.4s ease;

      @media only screen and (min-width: 992px) {
        opacity: 0;
        visibility: hidden;
      }
    }

    &-scrolltrack {
      padding: 10px 0px 5px 0px;
      gap: 20px;
      overflow-x: scroll;
      overflow-y: hidden;
      max-width: calc(100% - 32px);
      @include mixins.slim-scrollbar;
    }

    &-file {
      @include mixins.uploaded-file-container;
    }

    .progress-bar-bottom {
      bottom: -1px;
      left: 0;
      right: 0;
      width: 100%;
      height: 8px;
      border-radius: 0;
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      background-color: transparent;
      border-bottom: 1px solid transparent;
      margin: 0 auto;
    }
  }

  &.dragging {
    background-color: #fff;
    color: #ffffff;
    text-align: center;
  }

  &.dragging::before {
    content: "Drop your file here!";
    font-size: 1.1rem;
    font-weight: bold;
    color: #0d3149;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 999;
  }

  .wave-pulse {
    position: relative;
  }

  .wave-pulse::before,
  .wave-pulse::after {
    content: "";
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 8px solid transparent;
    animation: pulse-animation 3s ease-out infinite;
    top: -18px;
    left: -18px;
  }

  .wave-pulse::after {
    animation-delay: 1.5s;
  }

  @keyframes pulse-animation {
    0% {
      transform: scale(0);
      opacity: 0;
      border-color: #800000;
    }
    50% {
      opacity: 0.8;
      border-color: #ff4d4d;
    }
    90% {
      transform: scale(0.6);
      opacity: 0.2;
      border-color: #ff6666;
    }
    100% {
      transform: scale(0.7);
      opacity: 0;
      border-color: #ff9999;
    }
  }

  .start-countdown {
    bottom: 10px;
    left: 85px !important;
  }

  .countdown {
    font-size: 20px;
    font-weight: bold;
    color: #ff4d4d;
    animation: countdown-fade 1s linear;
  }

  @keyframes countdown-fade {
    0% {
      opacity: 1;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(0.8);
    }
  }
}
