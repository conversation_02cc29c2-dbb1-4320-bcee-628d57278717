version: 0.2
phases:
  install:
    commands:
      - echo "Installing dependencies..."
      - npm install
  build:
    commands:
      - echo "Building the project..."
      - npm run build
  post_build:
    commands:
      - echo "Deploying to S3..."
      - aws s3 sync build/ s3://$S3_BUCKET --delete
      - echo "Invalidate CloudFront Cache..."
      - aws cloudfront create-invalidation --distribution-id $PROD_DISTRIBUTION --paths "/*"