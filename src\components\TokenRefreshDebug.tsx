import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON> } from "react-bootstrap";
import useTokenRefresh from "hooks/useTokenRefresh";
import useUserStore from "stores/user";

const TokenRefreshDebug: React.FC = () => {
  const { refreshToken: performRefresh, isTokenExpired, getTokenInfo } = useTokenRefresh();
  const { token, loginType, refreshToken: storedRefreshToken } = useUserStore(
    (state) => state.userInfo
  );
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefreshResult, setLastRefreshResult] = useState<string>("");

  const handleManualRefresh = async () => {
    setIsRefreshing(true);
    setLastRefreshResult("");

    try {
      const newToken = await performRefresh();
      setLastRefreshResult(`Success: Token refreshed successfully`);
      console.log("New token:", newToken);
    } catch (error: any) {
      setLastRefreshResult(`Error: ${error.message}`);
      console.error("Refresh failed:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const tokenInfo = getTokenInfo();

  if (!token) {
    return (
      <Card className="m-3">
        <Card.Header>Token Refresh Debug</Card.Header>
        <Card.Body>
          <p>No token available. Please log in first.</p>
        </Card.Body>
      </Card>
    );
  }

  return (
    <Card className="m-3">
      <Card.Header>Token Refresh Debug</Card.Header>
      <Card.Body>
        <div className="mb-3">
          <h6>Current Token Info:</h6>
          <p><strong>Login Type:</strong> {loginType}</p>
          <p><strong>Has Refresh Token:</strong> {storedRefreshToken ? "Yes" : "No"}</p>
          <p><strong>Token Expired:</strong> {tokenInfo?.isExpired ? "Yes" : "No"}</p>
          <p><strong>Will Expire Soon:</strong> {tokenInfo?.willExpireSoon ? "Yes" : "No"}</p>
          {tokenInfo?.expiresAt && (
            <p><strong>Expires At:</strong> {new Date(tokenInfo.expiresAt).toLocaleString()}</p>
          )}
        </div>

        <div className="mb-3">
          <Button
            variant="primary"
            onClick={handleManualRefresh}
            disabled={isRefreshing}
          >
            {isRefreshing ? "Refreshing..." : "Manual Token Refresh"}
          </Button>
        </div>

        {lastRefreshResult && (
          <div className="mb-3">
            <h6>Last Refresh Result:</h6>
            <p className={lastRefreshResult.startsWith("Success") ? "text-success" : "text-danger"}>
              {lastRefreshResult}
            </p>
          </div>
        )}

        <div className="mb-3">
          <h6>Token Preview:</h6>
          <small className="text-muted" style={{ wordBreak: "break-all" }}>
            {token.substring(0, 50)}...
          </small>
        </div>
      </Card.Body>
    </Card>
  );
};

export default TokenRefreshDebug;
