import {
  PaymentElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { BackButton } from "components/Common";
import { useState } from "react";
import toast from "react-hot-toast";
import { ROUTE_PATH } from "routes";
import { getAppOriginURL } from "utils";

const CheckoutForm = ({ intentInfo }: any) => {
  const stripe = useStripe();
  const elements = useElements();

  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    const { error }: any = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${getAppOriginURL()}${ROUTE_PATH.WORD_COUNT_PAYMENT_STATUS}`,
      },
    });

    if (error?.type) {
      toast.error(error.message, { id: error.message ?? "generic-error" });
    } else {
      console.log("An unexpected error occured.", error);
    }

    setIsProcessing(false);
  };

  return (
    <form
      id="payment-form"
      className="d-flex flex-column"
      style={{ gap: "20px" }}
      onSubmit={handleSubmit}
    >
      <PaymentElement id="payment-element" />

      <div className="website-form w-100 mt-3">
        <div className="action-btns d-flex flex-column gap-3">
          <button
            disabled={isProcessing || !stripe || !elements}
            id="submit"
            className="btn submit-btn w-100 bg-brown border-brown text-uppercase font-light"
          >
            <span id="button-text">
              {isProcessing ? "Processing ... " : `Pay Now`}
            </span>
          </button>
          <BackButton title="Cancel" />
        </div>
      </div>
    </form>
  );
};

export default CheckoutForm;
