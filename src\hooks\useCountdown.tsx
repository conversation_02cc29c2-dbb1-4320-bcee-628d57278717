import { useCallback, useState } from "react";

const useCountdown = (
  initialValue: number,
  onComplete: () => void = () => {},
) => {
  const [countdown, setCountdown] = useState(0);

  const startCountdown = useCallback(() => {
    setCountdown(initialValue);
    const interval = setInterval(() => {
      setCountdown((prev) => {
        if (prev === 1) {
          clearInterval(interval);
          onComplete();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }, [initialValue, onComplete]);

  return { countdown, startCountdown };
};

export default useCountdown;
