@use "../../styles/mixins/mixins.scss" as mixins;

.payment-listing-container {
  gap: 10px;

  &-left {
    flex: 1;
    border-radius: 12px;
    padding: 0 30px;

    @media only screen and (max-width: 576px) {
      max-height: 550px;
      padding: 30px 15px;
    }

    .toolbar-wrapper-right {
      button {
        @media only screen and (max-width: 576px) {
          width: 100%;
        }
      }
    }

    .linked-card-list {
      gap: 30px;
      overflow-x: hidden;
      overflow-y: auto;

      @include mixins.slim-scrollbar;

      @media only screen and (max-width: 576px) {
        gap: 10px;
      }

      li:first-child {
        .card {
          &-data {
            &-image {
              height: 60px;
              padding: 0;
            }
          }
        }
      }

      li {
        padding: 10px;

        @media only screen and (max-width: 576px) {
          border: 1px solid rgba(0, 0, 0, 0.2);
          border-radius: 6px;
        }

        .card {
          &-data {
            flex: 1;
            max-width: 330px;

            .payment-card {
              width: 80px;
              min-width: 80px;
              height: 50px;
              min-height: 50px;
              padding: 0;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            &-image {
              width: 70px;
              height: 50px;
              object-fit: contain;
              object-position: center;
              padding: 5px;

              @media only screen and (max-width: 576px) {
                width: 85px;
                padding: 0;
              }
            }

            &-nickname {
              width: 80px;
              height: 50px;
              padding: 5px;

              @media only screen and (max-width: 576px) {
                width: 85px;
                padding: 0;
              }
            }
          }

          &-status-label {
            font-size: 16px;
            font-weight: 600;
            line-height: 1;
            padding: 10px 30px;

            &.default {
              background: #33bc00;
              color: #ffffff !important;
            }

            &.action-btn {
              background: #0d3149;
              color: #ffffff !important;
            }
          }
        }
      }

      button,
      a {
        @media only screen and (max-width: 576px) {
          position: absolute;
          bottom: 10px;
          right: 10px;
          background-color: #ff0000;
          color: #ffffff;
          border-radius: 50%;
          padding: 7px;
          width: 30px;
          height: 30px;

          svg {
            fill: #ffffff;
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}

.add-payment-card-modal,
.add-prompt-card-modal {
  .modal-dialog {
    @media only screen and (min-width: 992px) {
      width: 625px;
      max-width: 700px;
    }
  }

  .submit-btn.delete-btn {
    background-color: #b81a1a !important;
    border-color: #b81a1a !important;
  }
}

.add-prompt-card-modal {
  .auth-form .website-form form {
    gap: 20px;
  }
}
