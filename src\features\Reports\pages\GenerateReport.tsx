import { useEffect } from "react";
import { <PERSON><PERSON>, Spin<PERSON> } from "react-bootstrap";
import { isMobileOnly } from "react-device-detect";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useGeneratedReportDetails } from "../api";
import GeneratedReportsHistoryBox from "../components/GeneratedReportsHistoryBox";
import ReportBuilder from "../components/ReportBuilder";
import useSaveGeneratedReport from "../components/ReportBuilder/hooks/useSaveGeneratedReport";
import ReportGenerator from "../components/ReportGenerator";
import { REPORTS_ROUTE_PATH } from "../routePath";
import { resetReportState, setReportInfo } from "../store";
import useReportStore from "../store/report";
import "./styles.scss";

const GenerateReport = () => {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const reportInfo = useReportStore((state) => state.reportInfo);
  const { handleSaveReport, saveReport }: any = useSaveGeneratedReport();

  const { data: reportDetails = {}, isLoading } = useGeneratedReportDetails(id);

  const showEditor = searchParams.get("edit") === "true" || !!id;

  useEffect(() => {
    if (reportDetails && Object.keys(reportDetails).length) {
      setReportInfo(reportDetails);
    }
  }, [reportDetails]);

  const handleGenerateReportClick = () => {
    resetReportState();
    navigate(`${REPORTS_ROUTE_PATH.GENERATE_REPORT}?edit=true`);
  };

  return (
    <main className="reports-section d-flex flex-lg-row flex-column align-items-stretch w-100">
      {!isMobileOnly && <GeneratedReportsHistoryBox />}
      <div className="report-content w-100">
        {!showEditor ? (
          <div className="bg-white d-flex flex-column justify-content-center align-items-center h-100 rounded">
            <h2>Welcome to the Reports</h2>
            <p>Click below to generate your report.</p>
            <Button
              variant="primary"
              className="py-2 bg-blue font-light border-blue text-decoration-none fw-bold d-flex justify-content-between align-items-center"
              onClick={handleGenerateReportClick}
            >
              Generate New Report
            </Button>
          </div>
        ) : (
          <>
            {isLoading ? (
              <div className="d-flex justify-content-center align-items-center h-100 bg-white rounded">
                <Spinner />
              </div>
            ) : (
              <>
                {reportInfo?.title ? (
                  <ReportBuilder
                    onSaveReport={handleSaveReport}
                    saveReportSilenty={saveReport}
                  >
                    <div
                      className="position-sticky bottom-0 bg-white p-3 d-flex justify-content-end"
                      style={{ zIndex: 9999 }}
                    >
                      <Button
                        variant=""
                        className="bg-brown text-light fw-bold w-100 p-3 border-0 rounded-3"
                        onClick={handleSaveReport}
                      >
                        Finalise Report
                      </Button>
                    </div>
                  </ReportBuilder>
                ) : (
                  <ReportGenerator />
                )}
              </>
            )}
          </>
        )}
      </div>
    </main>
  );
};

export default GenerateReport;
