import { RiThumbDownFill, RiThumbDownLine } from "@remixicon/react";
import { useRemoveBadResponse, useUpdateBadResponse } from "api";
import { HoverTooltip } from "components/Common";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

const BadResponse = ({ messageItem }: any) => {
  const { id: msg_id, is_bad_response } = messageItem ?? {};

  const [isThumbDownClicked, setIsThumbDownClicked] = useState(false);
  const { id: chat_id } = useParams();

  const { mutateAsync: updateBadResponse } = useUpdateBadResponse();
  const { mutateAsync: removeBadResponse } = useRemoveBadResponse();

  useEffect(() => {
    setIsThumbDownClicked(is_bad_response);
  }, [is_bad_response]);

  const handleThumbDownClick = async () => {
    try {
      const result: any = isThumbDownClicked
        ? await removeBadResponse({
            chat_id,
            msg_id,
          })
        : await updateBadResponse({
            chat_id,
            msg_id,
          });
      if (result?.success) {
        setIsThumbDownClicked(!isThumbDownClicked);
      }
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <HoverTooltip title="Bad Response" customClass="fw-bold">
      <span
        onClick={handleThumbDownClick}
        className="d-block text-decoration-none cursor-pointer"
      >
        {isThumbDownClicked ? (
          <RiThumbDownFill size={18} />
        ) : (
          <RiThumbDownLine size={18} />
        )}
      </span>
    </HoverTooltip>
  );
};

export default BadResponse;
