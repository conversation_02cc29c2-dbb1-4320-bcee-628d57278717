import { useDeleteChatHistoryMutation } from "api";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { setConfirmModalConfig } from "stores";

const useDeleteChatHistory = ({
  itemInfo,
  setHistoryList,
  setTotalCount,
}: any) => {
  const { id: paramsChatId } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { mutateAsync: deleteHistory } = useDeleteChatHistoryMutation();

  const handleDelete = async (id: string) => {
    try {
      const result: any = await deleteHistory(id);
      if (result?.success) {
        toast.success(result?.message || "Chat Deleted Successfully!");
        setHistoryList((prev: any) =>
          prev.filter((item: any) => item.chat_id !== id),
        );
        setTotalCount((prev: any) => prev - 1);
        if (paramsChatId === id) {
          navigate(ROUTE_PATH.HOME);
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const onClickDelete = (evt: React.MouseEvent) => {
    evt.stopPropagation();
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleDelete(itemInfo.chat_id),
      },
    });
  };

  return { onClickDelete };
};

export default useDeleteChatHistory;
