import {
  GenerateReport,
  ReportArchives,
  REPORTS,
} from "features/Reports/pages";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { OrgUserRole } from "globals";
import { FeatureFlagName } from "hooks/useFeatureFlags";
import { DashboardContainerLayout, MainLayout } from "layouts";
import {
  ChangePassword,
  ChatSection,
  ContactUs,
  MyProfile,
  Notifications,
  QuotaUtilisation,
  Settings,
  WordCountPayment,
  WordCountPaymentStatus,
} from "pages";
import { Route } from "react-router-dom";
import { AdminRoutes } from "./AdminRoutes";
import { ROUTE_PATH } from "./routePath";
import { KNOWLEDGE_BASE_ROUTE_PATH } from "features/KnowledgeDatabase/routePath";
import { FAQPrompts, KnowledgeBase } from "features/KnowledgeDatabase/pages";

interface PrivateRoutesProps {
  userRole: OrgUserRole;
  isOrganisationEnabled: boolean;
  IsOfflineAccount: boolean;
  isFeatureEnabled: (feature: FeatureFlagName) => boolean;
  isReportsEnabled: boolean;
  isKnowledgeBaseEnabled: boolean;
}

const PrivateRoutes = ({
  userRole,
  isOrganisationEnabled,
  IsOfflineAccount,
  isFeatureEnabled,
  isReportsEnabled,
  isKnowledgeBaseEnabled,
}: PrivateRoutesProps) => {
  return (
    <Route element={<MainLayout />}>
      <Route path={ROUTE_PATH.HOME} element={<ChatSection />} />
      <Route path={`${ROUTE_PATH.HOME}/:id`} element={<ChatSection />} />
      <Route path={ROUTE_PATH.SETTINGS} element={<Settings />} />
      <Route path={ROUTE_PATH.MY_PROFILE} element={<MyProfile />} />
      <Route path={ROUTE_PATH.CHANGE_PASSWORD} element={<ChangePassword />} />
      <Route path={ROUTE_PATH.NOTIFICATIONS} element={<Notifications />} />
      <Route path={ROUTE_PATH.CONTACT_US} element={<ContactUs />} />
      <Route
        path={ROUTE_PATH.WORD_COUNT_PAYMENT}
        element={<WordCountPayment />}
      />
      <Route
        path={ROUTE_PATH.WORD_COUNT_PAYMENT_STATUS}
        element={<WordCountPaymentStatus />}
      />
      {/* <Route path={ROUTE_PATH.SUGGEST_FEATURE} element={<SuggestFeature />} />
      <Route path={ROUTE_PATH.REPORT_BUG} element={<ReportBug />} /> */}
      <Route
        path={ROUTE_PATH.QUOTA_UTILISATION}
        element={<QuotaUtilisation />}
      />
      {userRole !== OrgUserRole.USER &&
        AdminRoutes({ isOrganisationEnabled, IsOfflineAccount })}

      {isFeatureEnabled("REPORTS") && isReportsEnabled && (
        <Route>
          <Route
            path={REPORTS_ROUTE_PATH.BUILD_REPORTS}
            element={<REPORTS />}
          />
          <Route
            path={REPORTS_ROUTE_PATH.GENERATE_REPORT}
            element={<GenerateReport />}
          />
          <Route
            path={REPORTS_ROUTE_PATH.REPORT_ARCHIVES}
            element={<ReportArchives />}
          />
          <Route
            path={REPORTS_ROUTE_PATH.REPORT_DETAILS}
            element={<REPORTS />}
          />
          <Route
            path={REPORTS_ROUTE_PATH.GENERATE_REPORT_DETAILS}
            element={<GenerateReport />}
          />
        </Route>
      )}

      {isFeatureEnabled("KNOWLEDGE_BASE") && isKnowledgeBaseEnabled && (
        <Route element={<DashboardContainerLayout />}>
          <Route
            path={KNOWLEDGE_BASE_ROUTE_PATH.KNOWLEDGE_BASE}
            element={<KnowledgeBase />}
          />
          <Route
            path={KNOWLEDGE_BASE_ROUTE_PATH.KNOWLEDGE_BASE_FOLDER}
            element={<KnowledgeBase />}
          />
          <Route
            path={KNOWLEDGE_BASE_ROUTE_PATH.FAQ_PROMPTS}
            element={<FAQPrompts />}
          />
        </Route>
      )}
    </Route>
  );
};

export default PrivateRoutes;
