import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";

const useAllSearchParams = () => {
  const [searchParams] = useSearchParams();
  const [params, setParams] = useState<Record<string, string>>({});

  useEffect(() => {
    if (searchParams) {
      const urlParams: Record<string, string> = {};
      searchParams.forEach((value, key) => {
        urlParams[key] = value;
      });
      setParams(urlParams);
    }
  }, [searchParams]);

  return params;
};

export default useAllSearchParams;
