import React from "react";
import { RiArrowDownFill, RiArrowUpFill } from "@remixicon/react";
import { Button } from "react-bootstrap";

interface SortArrowProps {
  direction?: "asc" | "desc";
  onClick: () => void;
}

const SortArrow: React.FC<SortArrowProps> = ({ direction, onClick }) => {
  return (
    <Button
      variant="link"
      className="m-0 p-0 border-0 sort-btn"
      onClick={onClick}
    >
      {direction === "asc" ? (
        <RiArrowUpFill color="#ad986f" size={"24px"} />
      ) : direction === "desc" ? (
        <RiArrowDownFill color="#ad986f" size={"24px"} />
      ) : (
        <RiArrowDownFill color="#ad986f" size={"24px"} />
      )}
    </Button>
  );
};

export default SortArrow;
