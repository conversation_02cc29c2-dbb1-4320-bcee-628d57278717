import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { useAddCardMutation } from "api";
import { useState } from "react";

const useCardForm = (onSuccess: any) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [formData, setFormData] = useState<Record<string, any>>({
    cardholder_name: "",
  });
  const [errors, setErrors] = useState<any>({});

  const { mutateAsync: createCard } = useAddCardMutation();

  const validateFormData = (formData: any) => {
    const errorsObj: any = {};
    if (!formData.cardholder_name) {
      errorsObj.cardholder_name = "Cardholder name is required";
    }
    setErrors(errorsObj);
    return Object.keys(errorsObj).length === 0;
  };

  const handleCreateCard = async (paymentId: string) => {
    try {
      const response: any = await createCard({ payment_method_id: paymentId });
      if (response?.success) {
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = async (event: any) => {
    event.preventDefault();

    validateFormData(formData);
    setIsSubmitting(true);

    const stripeErrors: any = {};

    if (!formData.cardholder_name) {
      stripeErrors.cardholder_name = "Cardholder name is required";
    }

    if (!stripe || !elements) {
      setIsSubmitting(false);
      return;
    }

    const cardElement: any = elements.getElement(CardNumberElement);
    const expiryElement: any = elements.getElement(CardExpiryElement);
    const cvcElement: any = elements.getElement(CardCvcElement);

    if (cardElement && cardElement._empty) {
      stripeErrors.cardNumber = "Card number is required";
    }

    if (expiryElement && expiryElement._empty) {
      stripeErrors.expiry = "Expiry date is required";
    } else if (expiryElement && expiryElement._invalid) {
      stripeErrors.expiry = "Invalid expiry date";
    }

    if (cvcElement && cvcElement._empty) {
      stripeErrors.cvc = "CVC is required";
    } else if (cvcElement && cvcElement._invalid) {
      stripeErrors.cvc = "Invalid CVC";
    }

    if (Object.keys(stripeErrors).length > 0) {
      setErrors((prev: any) => ({ ...prev, ...stripeErrors }));
      setIsSubmitting(false);
      return;
    }

    const { error: cardError, ...paymentResponse }: any =
      await stripe.createPaymentMethod({
        type: "card",
        card: cardElement!,
        billing_details: {
          name: formData.cardholder_name,
        },
      });

    if (cardError) {
      stripeErrors.cardNumber = cardError.message;
      setErrors((prev: any) => ({ ...prev, ...stripeErrors }));
      setIsSubmitting(false);
      return;
    }

    setErrors({});
    await handleCreateCard(paymentResponse.paymentMethod.id);
  };

  const handleFormDataChange = (event: any) => {
    const { name, value } = event.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" });
  };

  const handleStripeElementChange = (event: any, elementName: string) => {
    if (event.complete || event.error === undefined) {
      setErrors((prev: any) => ({ ...prev, [elementName]: "" }));
    }

    if (event.error) {
      setErrors((prev: any) => ({
        ...prev,
        [elementName]: event.error.message,
      }));
    }
  };

  return {
    formData,
    errors,
    isSubmitting,
    handleSubmit,
    handleFormDataChange,
    handleStripeElementChange,
  };
};

export default useCardForm;
