import { useSetDefaultCardMutation } from "api";
import { setConfirmModalConfig } from "stores";
import toast from "react-hot-toast";

const useHandleUserStatus = ({ setDefaultCard }: any) => {
  const { mutateAsync: setDefaultCardMutation } = useSetDefaultCardMutation();

  const handleSetDefaultCard = async (id: any) => {
    try {
      const result: any = await setDefaultCardMutation({ id });
      if (result?.success) {
        toast.success(result?.message || "Card set as default successfully!");
        setDefaultCard(id);
      }
    } catch (err: any) {
      console.log(err);
    }
  };

  const onClickSetDefault = (id: number) => {
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleSetDefaultCard(id),
        content: {
          heading: "Set Default Card",
          description:
            "Are you sure you want to set this card as your default payment method?",
        },
        showModalIcon: false,
      },
    });
  };

  return { onClickSetDefault };
};

export default useHandleUserStatus;
