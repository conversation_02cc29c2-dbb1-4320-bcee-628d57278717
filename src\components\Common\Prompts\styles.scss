@use "/src/styles/mixins/mixins.scss";

.prompts-wrapper {
  padding-bottom: 50px;
  gap: 20px;
  max-width: 890px;
  margin: 0 auto;

  @media only screen and (max-width: 1199px) {
    padding: 0px 10px 20px 10px;
    position: absolute;
    left: 0;
    right: 0;
    bottom: -100px;
    overflow-x: auto;
    overflow-y: hidden;
    @include mixins.slim-scrollbar;
  }

  @media only screen and (min-width: 1630px) {
    div[class^="col-"] {
      width: 25% !important;
    }
  }

  .prompt-action-button {
    width: 25px;
    height: 25px;
    padding: 5px;
    z-index: 99999;
    right: -30px;
    top: 0;
  }

  .card {
    border-radius: 12px;
    border: 1px solid #f7f5f1;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.12);
    width: 210px;
    height: 140px;

    @media only screen and (max-height: 620px) {
      height: auto;
    }

    @media only screen and (min-height: 621px) and (max-height: 799px) {
      height: 165px;
    }

    @media only screen and (min-width: 1200px) and (max-width: 1899px) {
      height: auto;
    }

    @media only screen and (max-width: 1199px) {
      height: auto;
      flex: auto;
      min-width: 200px;
    }

    &-body {
      padding: 16px;
    }

    p {
      font-size: 16px;
      line-height: 25px;
      margin-top: 15px;
      font-weight: 600;

      @media only screen and (max-width: 576px) {
        font-size: 14px;
      }
    }

    .prompt-action-button {
      right: 10px;
      top: 18px;
      border: 2px solid #ad986f;

      @media only screen and (min-width: 992px) {
        opacity: 0;
      }
    }

    &:hover {
      background-color: #0d3149;

      p {
        color: #ffffff !important;
      }

      svg {
        fill: #ad986f;
      }

      @media only screen and (min-width: 992px) {
        .prompt-action-button {
          opacity: 1;
        }
      }
    }

    &:nth-child(3n + 1) {
      .prompt-icon {
        color: #60a799;
      }
    }

    &:nth-child(3n + 2) {
      .prompt-icon {
        color: #ad986f;
      }
    }

    &:nth-child(3n + 3) {
      .prompt-icon {
        color: #0d3149;
      }
    }
  }
}
