import { RiCameraLine } from "@remixicon/react";
import { DefaultProfile } from "components/Common";
import { IMAGE_PATH } from "globals";
import { Image } from "react-bootstrap";
import { generateNickName, isValidFileSize, isValidFileType } from "utils";

type DefaultProfileSize = "large" | "medium" | "small";

interface ProfilePictureProps {
  userPicture: any;
  title: string;
  setFieldValue?: any;
  name?: string;
  imageProps?: any;
  defaultProfileSize?: DefaultProfileSize;
}

const ProfilePicture: React.FC<ProfilePictureProps> = ({
  userPicture,
  title,
  setFieldValue,
  name = "profile_photo",
  imageProps,
  defaultProfileSize = "large",
}) => {
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files && event.target.files[0];
    if (file) {
      if (!isValidFileType(file) || !isValidFileSize(file)) {
        return;
      }
      setFieldValue(name, file);
    }
  };

  return (
    <div className="auth-form-file-input w-auto position-relative align-self-center">
      {!userPicture && !title ? (
        <Image
          src={IMAGE_PATH.avatarIcon}
          alt="profile"
          className="auth-form-card-user-img object-fit-cover rounded-circle bg-transparent border border-4 border-brown"
          {...imageProps}
          loading="eager"
        />
      ) : (
        <>
          {userPicture ? (
            <Image
              src={
                userPicture instanceof File
                  ? URL.createObjectURL(userPicture)
                  : userPicture
              }
              alt="profile"
              className="auth-form-card-user-img object-fit-cover rounded-circle bg-transparent border border-4 border-brown p-0"
              {...imageProps}
            />
          ) : (
            <DefaultProfile
              text={generateNickName(title)}
              className={`${defaultProfileSize} text-uppercase`}
            />
          )}
        </>
      )}

      {setFieldValue && (
        <>
          <input
            type="file"
            className="d-none"
            id="profileEdit"
            onChange={handleFileChange}
            accept=".png, .jpg, .jpeg"
          />
          <label
            htmlFor="profileEdit"
            className="auth-form-file-input-label text-center rounded-circle position-absolute d-flex justify-content-center align-items-center cursor-pointer"
          >
            <RiCameraLine size={"22px"} color="#f9f9f9" />
          </label>
        </>
      )}
    </div>
  );
};

export default ProfilePicture;
