import { RiAttachmentLine, RiCloseLine } from "@remixicon/react";
import { useFormik } from "formik";
import { UpdatePromptValidations } from "formSchema/schemaValidations";
import { DEFAULT_PROMPT_ICONS } from "globals";
import useUpdatePrompt from "hooks/chat/useUpdatePrompt";
import React, { useEffect, useState } from "react";
import { Button, Dropdown, DropdownButton, Form, Modal } from "react-bootstrap";
import { isValidFileSize, isValidPromptFileType } from "utils";
import "../styles.scss";

interface AddPromptModalProps {
  show: boolean;
  promptData: any;
  isStoredPrompt?: boolean;
  onClose: () => void;
  onOpen: () => void;
  onSavePrompt?: (prompt_data: any) => void;
  onResetPrompt?: (data: any) => void;
  onLocalReset?: (prompt_id: any) => void;
}

const AddPromptModal: React.FC<AddPromptModalProps> = ({
  show,
  promptData,
  isStoredPrompt = false,
  onClose,
  onOpen,
  onSavePrompt,
  onResetPrompt,
  onLocalReset,
}) => {
  const [selectedIcon, setSelectedIcon] = useState<string>("");

  const { onClickReset, onClickSubmit } = useUpdatePrompt({
    onCloseModal: onClose,
    onOpenModal: onOpen,
  });

  const { prompt_id, icon } = promptData || {};

  const formik: any = useFormik({
    initialValues: isStoredPrompt
      ? promptData
      : {
          title: "",
          prompt: "",
          prompt_id,
          icon,
        },
    enableReinitialize: true,
    validationSchema: UpdatePromptValidations,
    onSubmit: async (values) => {
      if (onSavePrompt) {
        onSavePrompt(values);
        onClose();
        return;
      }
      onClickSubmit(values);
    },
  });

  const handleIconSelect = (icon: string) => {
    setSelectedIcon(DEFAULT_PROMPT_ICONS[icon]);
    formik.setFieldValue("icon", icon);
  };

  useEffect(() => {
    if (icon) {
      setSelectedIcon(DEFAULT_PROMPT_ICONS[icon]);
    }
  }, [icon]);

  const renderError = (field: string) => {
    if (formik.touched[field] && formik.errors[field]) {
      return <span className="mb-2">{formik.errors[field]}</span>;
    }
  };

  const handleFileChange = (e: any) => {
    const file = e.target.files[0];
    if (!isValidPromptFileType(file) || !isValidFileSize(file)) {
      return;
    }
    formik.setFieldValue("file", file);
  };

  const handleClose = () => {
    formik.resetForm();
    onClose();
  };

  return (
    <Modal
      show={show}
      onHide={handleClose}
      keyboard={false}
      centered
      className="add-prompt-card-modal"
    >
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={handleClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>

        <div
          className="auth-form d-flex justify-content-center align-items-stretch flex-column"
          style={{ gap: "20px" }}
        >
          <div className="d-flex flex-column" style={{ gap: "15px" }}>
            <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
              Create Bespoke Prompt
            </h1>
          </div>

          <div className="website-form w-100">
            <Form
              className="d-flex flex-column gap-3"
              onSubmit={formik.handleSubmit}
            >
              <div className="d-flex flex-lg-row flex-column gap-3">
                <Form.Group className="m-0 p-0 position-relative w-100">
                  <Form.Label>Prompt Title</Form.Label>
                  <Form.Control
                    type="text"
                    placeholder="Enter prompt title"
                    {...formik.getFieldProps("title")}
                  />
                  <div className="d-flex flex-column">
                    <small className="font-gray position-static">
                      *Max 40 characters
                    </small>
                    {renderError("title")}
                  </div>
                </Form.Group>

                <Form.Group className="m-0 p-0 position-relative">
                  <Form.Label>Emoticon</Form.Label>
                  <DropdownButton
                    id="icon-dropdown"
                    title={selectedIcon}
                    variant="outline-secondary"
                    className="icon-dropdown w-100"
                  >
                    {Object.keys(DEFAULT_PROMPT_ICONS).map((icon: any) => {
                      const EmotiIcon = DEFAULT_PROMPT_ICONS[icon];
                      return (
                        <Dropdown.Item
                          key={icon}
                          onClick={() => handleIconSelect(icon)}
                        >
                          <EmotiIcon size={24} />{" "}
                          <div className="text-capitalize d-inline-block">
                            {icon}
                          </div>
                        </Dropdown.Item>
                      );
                    })}
                  </DropdownButton>
                </Form.Group>
              </div>

              <Form.Group className="m-0 p-0 position-relative">
                <Form.Label>Prompt</Form.Label>
                <Form.Control
                  as="textarea"
                  style={{ height: "54px", transition: "all -.4s ease" }}
                  className="border-0 fw-medium"
                  placeholder="Enter prompt"
                  {...formik.getFieldProps("prompt")}
                />
                <div className="d-flex flex-column">
                  <small className="font-gray position-static">
                    *Max 2500 characters
                  </small>
                  {renderError("prompt")}
                </div>
              </Form.Group>

              <Form.Group className="m-0 p-0 position-relative">
                <Form.Label>Upload File (Optional)</Form.Label>
                <Form.Control
                  type="file"
                  className="d-none"
                  id="prompt-file-id"
                  onChange={handleFileChange}
                />
                <label
                  htmlFor="prompt-file-id"
                  className="form-control position-relative d-flex justify-content-start align-items-center"
                >
                  <RiAttachmentLine className="font-gray me-3" size={22} />
                  <p className="font-gray mb-0">
                    {formik.values.file?.name ||
                      formik.values?.metadata?.name ||
                      "Upload your file"}
                  </p>
                </label>
                <small className="font-gray position-static">
                  *Only PDF/DOCX files allowed
                </small>
              </Form.Group>

              <div
                className="action-btns mt-3 d-flex flex-column"
                style={{ gap: "30px" }}
              >
                <Button
                  type="submit"
                  className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                >
                  Save
                </Button>

                {isStoredPrompt && (
                  <Button
                    type="button"
                    className="submit-btn delete-btn w-100 bg-brown border-brown text-uppercase font-light"
                    onClick={(evt: any) =>
                      onResetPrompt
                        ? onResetPrompt({
                            evt,
                            prompt_id: promptData?.id || prompt_id,
                            promptModalConfig: {
                              onCloseModal: onClose,
                              onOpenModal: onOpen,
                            },
                            promptData,
                          })
                        : onClickReset({ evt, prompt_id })
                    }
                  >
                    Reset Prompt
                  </Button>
                )}

                {onLocalReset && (
                  <Button
                    type="button"
                    className="submit-btn delete-btn w-100 bg-brown border-brown text-uppercase font-light"
                    onClick={() => {
                      onLocalReset(prompt_id);
                      onClose();
                      formik.resetForm();
                    }}
                    disabled={!formik.isValid || !formik.dirty}
                  >
                    Reset Prompt
                  </Button>
                )}
              </div>
            </Form>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default AddPromptModal;
