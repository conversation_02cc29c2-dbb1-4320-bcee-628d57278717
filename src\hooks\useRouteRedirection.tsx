import { useEffect } from "react";
import { ROUTE_PATH } from "routes";
import { setUserInfo } from "stores";
import { getAppOriginURL } from "utils";

const useRouteRedirection = (userInfo: any, setLoading: any) => {
  const { token, user } = userInfo;
  useEffect(() => {
    if (
      token &&
      !user?.email_verified &&
      window.location.pathname !== ROUTE_PATH.OTP &&
      user?.otp_data?.id
    ) {
      window.location.href = `${getAppOriginURL()}${ROUTE_PATH.OTP}?id=${user?.otp_data?.id}&email=${user?.email}&type=login`;
      setUserInfo({
        ...userInfo,
        user: {
          ...user,
          otp_data: undefined,
        },
      });
    } else if (
      token &&
      user?.email_verified &&
      !user?.is_subscription &&
      window.location.pathname !==
        `${import.meta.env.VITE_BASE_URL}${ROUTE_PATH.SUBSCRIPTIONS}` &&
      window.location.pathname !==
        `${import.meta.env.VITE_BASE_URL}${ROUTE_PATH.PAYMENT_STATUS}`
    ) {
      window.location.href = `${getAppOriginURL()}${ROUTE_PATH.SUBSCRIPTIONS}`;
    } else {
      setLoading(false);
    }
  }, [user]);
};

export default useRouteRedirection;
