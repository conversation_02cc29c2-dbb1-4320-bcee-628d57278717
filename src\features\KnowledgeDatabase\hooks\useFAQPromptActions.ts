import toast from "react-hot-toast";
import { useInvalidateQuery } from "hooks";
import { useAddFAQPrompt, useUpdateFAQPrompt } from "../api";

const useFAQPromptActions = () => {
  const { mutateAsync: addFAQPrompt } = useAddFAQPrompt();
  const { mutateAsync: updateFAQPrompt } = useUpdateFAQPrompt();
  const [invalidateQueries] = useInvalidateQuery();

  const handleAddFAQPrompt = async (data: any) => {
    try {
      const payload = {
        title: data.title,
        context: data.promptContext,
        sub_context: data.subContext ? JSON.parse(data.subContext) : null,
        access_control: data.access_control,
        icon: data.icon,
      };

      const result: any = await addFAQPrompt(payload);
      if (result?.success) {
        toast.success(result?.message || "FAQ Prompt created successfully!");
        invalidateQueries(["faq-prompts"]);
        return { success: true };
      }
    } catch (err: any) {
      console.error("Error adding FAQ prompt:", err);
      toast.error(
        err?.response?.data?.message || "Failed to create FAQ prompt"
      );
      return { success: false };
    }
  };

  const handleUpdateFAQPrompt = async (data: any) => {
    try {
      const payload = {
        id: data.id,
        title: data.title,
        context: data.promptContext,
        sub_context: data.subContext ? JSON.parse(data.subContext) : null,
        access_control: data.access_control,
        icon: data.icon,
      };

      const result: any = await updateFAQPrompt(payload);
      if (result?.success) {
        toast.success(result?.message || "FAQ Prompt updated successfully!");
        invalidateQueries(["faq-prompts"]);
        return { success: true };
      }
    } catch (err: any) {
      console.error("Error updating FAQ prompt:", err);
      toast.error(
        err?.response?.data?.message || "Failed to update FAQ prompt"
      );
      return { success: false };
    }
  };

  return {
    handleAddFAQPrompt,
    handleUpdateFAQPrompt,
  };
};

export default useFAQPromptActions;
