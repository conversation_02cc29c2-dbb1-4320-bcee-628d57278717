import { tokenRefreshService } from "services/tokenRefreshService";
import { LOGIN_TYPE } from "globals";

/**
 * Test utility functions for validating token refresh implementation
 */

export const testTokenRefresh = {
  /**
   * Test if a token is properly parsed and expiration is detected
   */
  testTokenParsing: (token: string) => {
    console.log("Testing token parsing...");
    
    const isExpired = tokenRefreshService.isTokenExpired(token, 0);
    const willExpireSoon = tokenRefreshService.isTokenExpired(token, 5);
    const expirationTime = tokenRefreshService.getTokenExpiration(token);
    
    console.log("Token expired:", isExpired);
    console.log("Will expire soon (5 min):", willExpireSoon);
    console.log("Expiration time:", expirationTime ? new Date(expirationTime).toLocaleString() : "Unknown");
    
    return {
      isExpired,
      willExpireSoon,
      expirationTime,
    };
  },

  /**
   * Test SSO token refresh (requires Auth0 context to be set)
   */
  testSSORefresh: async () => {
    console.log("Testing SSO token refresh...");
    
    try {
      const result = await tokenRefreshService.refreshTokenSSO();
      console.log("SSO refresh result:", result);
      return result;
    } catch (error) {
      console.error("SSO refresh test failed:", error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Test embedded login token refresh (requires refresh token)
   */
  testEmbeddedRefresh: async (refreshToken: string) => {
    console.log("Testing embedded token refresh...");
    
    try {
      const result = await tokenRefreshService.refreshTokenEmbedded(refreshToken);
      console.log("Embedded refresh result:", result);
      return result;
    } catch (error) {
      console.error("Embedded refresh test failed:", error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Test the main refresh method with current user info
   */
  testMainRefresh: async () => {
    console.log("Testing main refresh method...");
    
    // Get current user info from localStorage
    const userStore = JSON.parse(localStorage.getItem('user') || '{}');
    const userInfo = userStore.state?.userInfo;
    
    if (!userInfo?.loginType || !userInfo?.token) {
      console.error("No user info available for testing");
      return { success: false, error: "No user info available" };
    }
    
    console.log("Current login type:", userInfo.loginType);
    console.log("Has refresh token:", !!userInfo.refreshToken);
    
    try {
      const result = await tokenRefreshService.refreshToken(
        userInfo.loginType,
        userInfo.refreshToken
      );
      console.log("Main refresh result:", result);
      return result;
    } catch (error) {
      console.error("Main refresh test failed:", error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Test token refresh scheduling
   */
  testScheduling: (token: string, loginType: string, refreshToken?: string) => {
    console.log("Testing token refresh scheduling...");
    
    const expirationTime = tokenRefreshService.getTokenExpiration(token);
    if (!expirationTime) {
      console.error("Cannot test scheduling - no expiration time");
      return false;
    }
    
    const now = Date.now();
    const timeUntilExpiry = expirationTime - now;
    const refreshTime = timeUntilExpiry - (5 * 60 * 1000); // 5 minutes before
    
    console.log("Token expires in:", Math.round(timeUntilExpiry / 1000 / 60), "minutes");
    console.log("Refresh scheduled in:", Math.round(refreshTime / 1000 / 60), "minutes");
    
    if (refreshTime > 0) {
      tokenRefreshService.scheduleTokenRefresh(token, loginType, refreshToken);
      console.log("Token refresh scheduled successfully");
      return true;
    } else {
      console.log("Token expires too soon to schedule refresh");
      return false;
    }
  },

  /**
   * Run all tests with current user session
   */
  runAllTests: async () => {
    console.log("=== Running Token Refresh Tests ===");
    
    // Get current user info
    const userStore = JSON.parse(localStorage.getItem('user') || '{}');
    const userInfo = userStore.state?.userInfo;
    
    if (!userInfo?.token) {
      console.error("No token available - please log in first");
      return;
    }
    
    // Test 1: Token parsing
    console.log("\n1. Testing token parsing...");
    testTokenRefresh.testTokenParsing(userInfo.token);
    
    // Test 2: Scheduling
    console.log("\n2. Testing scheduling...");
    testTokenRefresh.testScheduling(
      userInfo.token,
      userInfo.loginType,
      userInfo.refreshToken
    );
    
    // Test 3: Main refresh method
    console.log("\n3. Testing main refresh method...");
    await testTokenRefresh.testMainRefresh();
    
    console.log("\n=== Tests Complete ===");
  },
};

// Make it available globally for browser console testing
(window as any).testTokenRefresh = testTokenRefresh;

export default testTokenRefresh;
