import { Button } from "react-bootstrap";
import { useNavigate } from "react-router-dom";

interface BackButtonProps {
  title?: string;
  url?: string;
  onClick?: any;
}

const BackButton: React.FC<BackButtonProps> = ({
  title = "Back",
  url = "",
  onClick,
}) => {
  const navigate = useNavigate();

  const handleBack = () => {
    if (onClick) {
      onClick();
    }
    url ? navigate(url) : navigate(-1);
  };

  return (
    <Button
      onClick={handleBack}
      className="text-decoration-none d-flex justify-content-center align-items-center submit-btn position-relative w-100 border-brown bg-transparent text-uppercase font-primary z-3"
    >
      {title}
    </Button>
  );
};

export default BackButton;
