import * as Yup from "yup";
import {
  emailValidation,
  passwordValidation,
  stringRequiredValidation,
} from "./common";

export const ProfileValidations = Yup.object().shape({
  full_name: stringRequiredValidation("Full Name"),
});

export const SuggestFeatureValidations = Yup.object().shape({
  title: stringRequiredValidation("Title")
    .min(6, "Title must be at least 6 characters.")
    .max(200, "Title must be less than 200 characters."),
  description: stringRequiredValidation("Description")
    .min(10, "Description must be at least 10 characters.")
    .max(250, "Description must be less than 250 characters."),
});

export const ReportBugValidations = Yup.object().shape({
  title: stringRequiredValidation("Title")
    .min(6, "Title must be at least 6 characters.")
    .max(200, "Title must be less than 200 characters."),
  description: stringRequiredValidation("Description")
    .min(10, "Description must be at least 10 characters.")
    .max(250, "Description must be less than 250 characters."),
});

export const UpdatePromptValidations = Yup.object().shape({
  title: stringRequiredValidation("Title")
    .min(6, "Title must be at least 6 characters.")
    .max(40, "Title must be less than 40 characters."),
  prompt: stringRequiredValidation("Description")
    .min(50, "Description must be at least 50 characters.")
    .max(2500, "Description must be less than 2500 characters."),
});

export const CouponValidations = Yup.object().shape({
  coupon_code: Yup.string()
    .required("Coupon code is required")
    .matches(/^[A-Z0-9_]+$/i, "Invalid coupon code format")
    .max(20, "Coupon code cannot be longer than 20 characters"),
});

export const InviteNewValidations = Yup.object().shape({
  full_name: stringRequiredValidation("Full Name"),
  email: emailValidation,
  role: stringRequiredValidation("Account Type"),
});

export const VerifyMemberValidations = Yup.object().shape({
  newPassword: passwordValidation("Password"),
});

export const OrganisationProfileValidations = Yup.object().shape({
  title: stringRequiredValidation("Name"),
});
