import { RiArticleLine, RiCloseLine } from "@remixicon/react";
import { FC } from "react";
import { Button } from "react-bootstrap";
import { setSelectedFAQPrompt } from "stores";
import "./styles.scss";

interface FAQDocPreviewProps {
  selectedFAQPrompt: any;
}

const FAQDocPreview: FC<FAQDocPreviewProps> = ({ selectedFAQPrompt }) => {
  const { title } = selectedFAQPrompt ?? {};

  const handleClickRemove = async () => {
    setSelectedFAQPrompt(null);
  };

  return (
    <div className="form-file-output-wrapper rounded-3 w-100 mx-3">
      <div className="form-file-output-wrapper-scrolltrack d-flex">
        <div className="form-file-output-wrapper-file d-flex justify-content-start align-items-center position-relative p-2">
          <Button
            variant="Link"
            className="m-0 p-0 remove-btn rounded-circle position-absolute"
            onClick={handleClickRemove}
          >
            <RiCloseLine size={"15px"} />
          </Button>

          <RiArticleLine size={"30px"} />

          <hr className="my-1 p-0 align-self-stretch opacity-100" />

          <p className="mb-0 fw-bold text-start text-truncate">
            {title}
            <hr className="border-0 my-1" />
            <small className="fw-medium text-uppercase">FAQ</small>
          </p>
        </div>
      </div>
    </div>
  );
};

export default FAQDocPreview;
