@use "sass:math";
@use "/src/styles/mixins/mixins.scss" as mixins;

.auth {
  &-layout {
    min-height: inherit;

    @media only screen and (min-width: 992px) {
      min-height: 100vh;
    }
  }

  &-background {
    padding: 45px 0px;
    width: 550px;
    background-image: url("assets/images/authBg.webp"),
      linear-gradient(180deg, #0d3149 -6.33%, #3c7981 99.98%);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: bottom;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    height: 100%;

    @media only screen and (max-width: 1699px) {
      width: 450px;
    }

    @media only screen and (max-width: 1499px) {
      width: 350px;
    }

    @media only screen and (max-width: 991px) {
      padding: 25px 0px;
      position: static;
      width: 100%;
    }

    &-featureImg {
      width: 1100px;
      left: -285px;
      top: 54%;
      filter: drop-shadow(0px 15.131px 45.392px rgba(0, 0, 0, 0.6));

      @media only screen and (max-width: 1699px) {
        width: 900px;
        left: -235px;
      }

      @media only screen and (max-width: 1499px) {
        width: 700px;
        left: -185px;
      }

      @media only screen and (min-width: 992px) and (max-width: 1199px) {
        width: 690px;
        left: -260px;
      }

      @media only screen and (max-height: 512px) {
        width: 635px;
        left: -130px;
        top: 60%;
      }
    }
  }

  &-website-logo {
    width: 250px;
    margin-left: 45px;

    @media only screen and (max-width: 991px) {
      margin: 0 auto;
    }
  }

  &-form {
    width: calc(100vw - 600px);
    margin-left: auto;

    @media only screen and (max-width: 1699px) {
      width: calc(100vw - 480px);
    }

    @media only screen and (max-width: 1499px) {
      width: calc(100vw - 430px);
    }

    @media only screen and (max-width: 991px) {
      width: 100%;
      margin: 0 auto;
      padding: 50px 20px;
    }

    @media only screen and (max-height: 512px) {
      padding: 37px 0px;
    }

    @include mixins.universal-heading-subheading;

    @include mixins.form-inputs;

    .website-form {
      width: math.percentage(0.5);
      @include mixins.slim-scrollbar;

      @media only screen and (max-width: 1499px) {
        width: math.percentage(0.64);
      }

      @media only screen and (max-width: 1199px) {
        width: math.percentage(0.9);
      }

      @media only screen and (max-width: 991px) {
        width: math.percentage(0.9);
      }

      @media only screen and (max-width: 576px) {
        width: math.percentage(1);
      }

      input[type="checkbox"] {
        border-radius: 50%;
        box-shadow: none;
        border-color: #60a799;
        width: 20px;
        height: 20px;
        margin-right: 15px;

        &:checked {
          background-color: #60a799;
        }

        @media only screen and (max-width: 576px) {
          width: 18px;
          height: 18px;
          position: relative;
          top: -1px;
        }
      }

      .action-btns {
        .submit-btn {
          @include mixins.submit-btn;

          &.bg-transparent {
            @include mixins.submit-btn-transparent;
            @include mixins.button-layer-hover;
          }
        }
      }

      .interaction-btns {
        @media only screen and (max-width: 576px) {
          font-size: 15px;
        }
      }
    }
  }
}
