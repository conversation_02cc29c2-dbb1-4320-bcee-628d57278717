@mixin input-autofill-spin($include-spin-button: true) {
  @if $include-spin-button {
    &::-webkit-inner-spin-button,
    &::-webkit-outer-spin-button {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      margin: 0;
    }
  }

  &:-webkit-autofill,
  &:-webkit-autofill:focus {
    transition:
      background-color 600000s 0s,
      color 600000s 0s;
  }

  &[data-autocompleted] {
    background-color: transparent;
  }
}

@mixin form-inputs {
  .website-form {
    form {
      gap: 30px;

      span {
        position: absolute;
        bottom: -24px;
        font-size: 14px;
        font-weight: bold;
        color: #ff0000;
      }

      .form-group {
        svg {
          position: absolute;
          top: 68%;
          transform: translateY(-50%);
          right: 20px;
        }
      }

      .form-control {
        height: 56px;
        padding: 0px 50px 0px 15px;
        border-radius: 50px;
        background: rgba(173, 152, 111, 0.1);
        font-size: 18px;
        font-style: normal;
        border: none;
        border: 3px solid rgba(173, 152, 111, 0.1);

        &:focus {
          box-shadow: none;
        }

        &::placeholder {
          color: #0d3149;
          opacity: 0.5;
        }

        &[type="password"]:not(:placeholder-shown) {
          font-family: auto;
        }
      }

      .form-label {
        font-size: 16px;
        font-weight: 500;
      }

      textarea {
        height: 150px !important;
        resize: none;
        border-radius: 10px !important;
        white-space: break-spaces;
        padding-top: 10px !important;

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-track {
          box-shadow: inset 0 0 6px #70828d;
          border-radius: 10px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 10px;
          box-shadow: inset 0 0 6px #60a799;
        }
      }
    }
  }
}

//This is scss placeholder method
%submit-btn-base {
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 1px;
  border-radius: 30px;
  height: 56px;
  line-height: 1;
  transition: 0.3s ease-in-out;

  &:hover {
    transform: scale(0.95);
  }

  &.no-shadow {
    box-shadow: none;
  }
}

@mixin submit-btn {
  @extend %submit-btn-base;
}

@mixin submit-btn-transparent {
  @extend %submit-btn-base;
  border: 1px solid;
  overflow: hidden;
  position: relative;
  transform: none !important;

  @media only screen and (max-width: 767px) {
    width: 100%;
  }
}

@mixin custom-check-box {
  .check-box {
    input[type="checkbox"] {
      width: 15px;
      height: 15px;
      border: 2px solid #0d3149;
      border-radius: 3px;
      box-shadow: none !important;

      &:checked {
        border-color: #ad986f;
        background-color: #ad986f;
      }
    }
  }
}
