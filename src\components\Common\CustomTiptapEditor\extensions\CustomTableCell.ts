import { TableCell as TiptapTableCell } from '@tiptap/extension-table-cell';

export const CustomTableCell = TiptapTableCell.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      colwidth: {
        default: null,
        parseHTML: element => {
          // Try to get colwidth from data-colwidth or style.width
          let colwidth = element.getAttribute('data-colwidth');
          if (!colwidth && element.style && element.style.width) {
            colwidth = element.style.width;
          }
          if (colwidth) {
            return [parseInt(String(colwidth), 10)];
          }
          return null;
        },
        renderHTML: () => {
          // Don't render anything here - we'll handle it in the global renderHTML
          return {};
        },
      },
      minWidth: {
        default: null,
        parseHTML: element => {
          // Extract min-width from style attribute
          if (element.style && element.style.minWidth) {
            return parseInt(element.style.minWidth, 10);
          }
          // Also check for min-width in data attribute if needed
          const minWidth = element.getAttribute('data-min-width');
          if (minWidth) {
            return parseInt(minWidth, 10);
          }
          return null;
        },
        renderHTML: () => {
          // Don't render anything here - we'll handle it in the global renderHTML
          return {};
        },
      },
    };
  },

  renderHTML({ HTMLAttributes, node }) {
    const attrs = node.attrs;
    const styleProps = [];
    const dataAttrs: Record<string, any> = {};

    // Handle colwidth
    if (attrs.colwidth && Array.isArray(attrs.colwidth) && attrs.colwidth[0]) {
      dataAttrs['data-colwidth'] = attrs.colwidth[0];
      styleProps.push(`width:${attrs.colwidth[0]}px`);
      // If we have colwidth, also set min-width to the same value for consistency
      styleProps.push(`min-width:${attrs.colwidth[0]}px`);
    }

    // Handle separate minWidth (only if colwidth is not set)
    if (attrs.minWidth && (!attrs.colwidth || !attrs.colwidth[0])) {
      dataAttrs['data-min-width'] = attrs.minWidth;
      styleProps.push(`min-width:${attrs.minWidth}px`);
    }

    // Combine all attributes
    const combinedAttrs = {
      ...HTMLAttributes,
      ...dataAttrs,
      ...(styleProps.length > 0 && { style: styleProps.join('; ') }),
    };

    return ['td', combinedAttrs, 0];
  },
});

export default CustomTableCell; 