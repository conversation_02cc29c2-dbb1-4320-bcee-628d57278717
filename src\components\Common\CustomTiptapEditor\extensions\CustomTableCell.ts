import { TableCell as TiptapTableCell } from '@tiptap/extension-table-cell';

export const CustomTableCell = TiptapTableCell.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      colwidth: {
        default: null,
        parseHTML: element => {
          // Try to get colwidth from data-colwidth or style.width
          let colwidth = element.getAttribute('data-colwidth');
          if (!colwidth && element.style && element.style.width) {
            colwidth = element.style.width;
          }
          if (colwidth) {
            return [parseInt(String(colwidth), 10)];
          }
          return null;
        },
        renderHTML: attributes => {
          if (attributes.colwidth && Array.isArray(attributes.colwidth) && attributes.colwidth[0]) {
            return {
              'data-colwidth': attributes.colwidth[0],
              style: `width:${attributes.colwidth[0]}px`,
            };
          }
          return {};
        },
      },
    };
  },
});

export default CustomTableCell; 