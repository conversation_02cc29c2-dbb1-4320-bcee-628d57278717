import { FullLoadingStatus, SimpleLoadingStatus } from "globals";
import { Fragment, useEffect, useMemo, useRef, useState } from "react";
import IncomingMessage from "./IncomingMessage";
import OutgoingMessage from "./OutgoingMessage";

const MessageHistory = ({
  containerStyle,
  messageHistory,
  messagesEndRef,
}: any) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [history, setHistory] = useState<{ [key: string]: string[] }>({});
  const [isPlaying, setIsPlaying] = useState<string | null>(null);
  const [currentStatusIdx, setCurrentStatusIdx] = useState(0);
  const [isProcessing, setIsProcessing] = useState<any>({});

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(null);
    }

    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
        setIsPlaying(null);
      }
    };
  }, [messageHistory?.length]);

  const LoadingStatus = useMemo(() => {
    if (messageHistory?.length > 0 && messageHistory?.[0]?.prompt_id) {
      return SimpleLoadingStatus;
    }
    return FullLoadingStatus;
  }, [messageHistory?.length]);

  return (
    <div className="messages d-flex flex-column" style={containerStyle}>
      {messageHistory?.length > 0 &&
        messageHistory?.map((messageItem: any, idx: number) => {
          return (
            <Fragment key={idx}>
              <OutgoingMessage messageItem={messageItem} />
              <IncomingMessage
                messageItem={messageItem}
                isLastMessage={idx === messageHistory?.length - 1}
                audioRef={audioRef}
                history={history}
                setHistory={setHistory}
                isPlaying={isPlaying}
                setIsPlaying={setIsPlaying}
                responseStatusConfig={{
                  currentStatusIdx,
                  setCurrentStatusIdx,
                  setIsProcessing,
                  isProcessing,
                  idx,
                  loadingStatus: LoadingStatus,
                }}
              />
            </Fragment>
          );
        })}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageHistory;
