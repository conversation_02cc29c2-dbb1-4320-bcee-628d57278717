import { RiArticleLine } from "@remixicon/react";
import { FC } from "react";

interface CommonDocumentCardProps {
  title: string;
  icon?: any;
  subtitle?: string;
}

const CommonDocumentCard: FC<CommonDocumentCardProps> = ({
  title,
  icon = RiArticleLine,
  subtitle,
}) => {
  const DocIcon = icon;
  return (
    <div className="user-message-file d-flex justify-content-start align-items-center ms-auto position-relative">
      <DocIcon size={"30px"} />

      <hr className="my-1 p-0 align-self-stretch opacity-100" />

      <p className="mb-0 fw-bold text-start text-truncate">
        {title}
        <hr className="border-0 my-1" />
        <small className="fw-medium text-uppercase">{subtitle}</small>
      </p>
    </div>
  );
};

export default CommonDocumentCard;
