@mixin button-layer-hover {
  &::after,
  &::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: -100%;
    left: 0;
    right: inherit;
    bottom: inherit;
    transform: none;
    background-color: #ad986f;
    z-index: -1;
    transition: all 0.35s;
  }

  &::before {
    opacity: 0.5;
  }

  &::after {
    transition-delay: 0.2s;
  }

  &:hover::after,
  &:hover:before {
    top: 0;
  }
}

@mixin btn-close-style {
  border-radius: 8px;
  background-color: #0d3149;
  opacity: 1;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxNiAxNicgZmlsbD0nI2ZmZmZmZic+PHBhdGggZD0nTS4yOTMuMjkzYTEgMSAwIDAgMSAxLjQxNCAwTDggNi41ODYgMTQuMjkzLjI5M2ExIDEgMCAxIDEgMS40MTQgMS40MTRMOS40MTQgOGw2LjI5MyA2LjI5M2ExIDEgMCAwIDEtMS40MTQgMS40MTRMOCA5LjQxNGwtNi4yOTMgNi4yOTNhMSAxIDAgMCAxLTEuNDE0LTEuNDE0TDYuNTg2IDggLjI5MyAxLjcwN2ExIDEgMCAwIDEgMC0xLjQxNHonLz48L3N2Zz4=");
  background-size: 12px;
  box-shadow: none !important;
}

@mixin btn-transparent {
  height: 50px;
  padding: 18px 10px;
  border-radius: 30px;
  text-transform: uppercase;
  text-decoration: none;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 1px;
  text-align: center;
  border: 1px solid #f9f9f9;
  line-height: 1;
  color: #ffffff;
  overflow: hidden;
  position: relative;
  width: 230px;

  @media only screen and (max-width: 767px) {
    width: 100%;
  }
}
