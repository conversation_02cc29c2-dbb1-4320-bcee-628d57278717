import { RiMailLine } from "@remixicon/react";
import { useForgotPasswordMutation } from "api";
import { BackButton } from "components";
import { ForgotPasswordInitialValues } from "formSchema/initialValues";
import { ForgotPasswordValidations } from "formSchema/schemaValidations";
import { ErrorMessage, Field, Form, Formik, FormikProps } from "formik";
import { <PERSON><PERSON>, Spinner } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { ForgotInterface } from "types";
import AuthHeaderData from "../AuthHeaderData";
import "./styles.scss";

const ForgotPassword = () => {
  const headerData = {
    heading: "fORGOT PASSWORD",
    description: `Don't worry! It happens, Please enter the email address associated <br className="d-lg-block d-none" /> with your account`,
  };

  const navigate = useNavigate();

  const { mutateAsync: forgotPassword } = useForgotPasswordMutation();

  const handleSubmit = async (
    values: ForgotInterface,
    { setSubmitting }: any,
  ) => {
    try {
      const response: any = await forgotPassword(values);
      if (response?.success) {
        const { email, id } = response?.data ?? {};
        navigate(
          `${ROUTE_PATH.OTP}?email=${email}&id=${id}&type=forgotPassword`,
        );
      }
    } catch (error: any) {
      console.log(error?.response?.data?.message);
    } finally {
      setSubmitting(false);
    }
  };
  return (
    <div
      className="auth-form d-flex justify-content-center align-items-center flex-column forgot-form"
      style={{ gap: "30px" }}
    >
      <AuthHeaderData
        heading={headerData.heading}
        description={headerData.description}
      />

      <div className="website-form">
        <Formik
          initialValues={ForgotPasswordInitialValues}
          validationSchema={ForgotPasswordValidations}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting }: FormikProps<any>) => (
            <Form className="d-flex flex-column" style={{ gap: "30px" }}>
              <div className="form-group position-relative">
                <label className="form-label">Email</label>
                <Field
                  name="email"
                  className="form-control"
                  placeholder="Enter your email"
                  type="text"
                />
                <ErrorMessage component={"span"} name="email" />
                <RiMailLine size={"20px"} color="#70828D" />
              </div>

              <div
                className="action-btns d-flex flex-column"
                style={{ gap: "30px" }}
              >
                <Button
                  type="submit"
                  className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? <Spinner /> : "Submit"}
                </Button>

                <BackButton />
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default ForgotPassword;
