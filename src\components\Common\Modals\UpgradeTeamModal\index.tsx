import { RiCloseLine } from "@remixicon/react";
import { useGetPaymentDetailsMutation } from "api";
import { useFormik } from "formik";
import React from "react";
import { Button, Form, Modal, Spinner } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import usePlanStore from "stores/plan";
import * as Yup from "yup";

const MIN_TEAM_SIZE = 1;
const MAX_TEAM_SIZE = 500;

interface UpgradeTeamModalProps {
  show: boolean;
  onClose: () => void;
  tierConfig: any;
}

const UpgradeTeamModal: React.FC<UpgradeTeamModalProps> = ({
  show,
  onClose,
  tierConfig,
}) => {
  const totalLicence = tierConfig.team_size || 1;
  const activeLicence = tierConfig.team_count || 0;

  const { mutateAsync: getPaymentDetails } = useGetPaymentDetailsMutation();
  const myPlan = usePlanStore((state: any) => state.myPlan);

  const navigate = useNavigate();
  const formik: any = useFormik({
    initialValues: { team_size: totalLicence },
    enableReinitialize: true,
    validationSchema: Yup.object({
      team_size: Yup.number()
        .required("Team size is required")
        .min(MIN_TEAM_SIZE, `Team size must be at least ${MIN_TEAM_SIZE}`)
        .max(MAX_TEAM_SIZE, `Team size must be at most ${MAX_TEAM_SIZE}`),
    }),
    onSubmit: async (values) => {
      try {
        const result: any = await getPaymentDetails({
          subscription_id: myPlan?.currentPlan?.subscription_id,
          team_size: values.team_size,
        });
        if (result?.success) {
          navigate(`${ROUTE_PATH.PAYMENT}?team_size=${values.team_size}`);
        }
      } catch (err: any) {
        console.log(err);
      }
    },
  });

  const renderError = (field: string) => {
    if (formik.touched[field] && formik.errors[field]) {
      return <span className="mb-2">{formik.errors[field]}</span>;
    }
  };

  const handleClose = () => {
    formik.resetForm();
    onClose();
  };

  return (
    <Modal show={show} onHide={handleClose} keyboard={false} centered>
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={handleClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>

        <div
          className="auth-form d-flex justify-content-center align-items-stretch flex-column"
          style={{ gap: "20px" }}
        >
          <div className="d-flex flex-column" style={{ gap: "15px" }}>
            <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
              Manage Licences
            </h1>
          </div>
          <div className="d-flex justify-content-center gap-3 fw-bold fs-5">
            <span>Total Licences: {formik.values.team_size}</span>
            <span>Active Licences: {activeLicence}</span>
          </div>

          <div className="website-form w-100">
            <Form
              className="d-flex flex-column gap-3"
              onSubmit={formik.handleSubmit}
            >
              <div className="d-flex flex-lg-row flex-column gap-3">
                <Form.Group className="m-0 p-0 position-relative w-100">
                  <Form.Label className="mb-2 text-center w-100">
                    How many licences does your team need?
                  </Form.Label>
                  <Form.Control
                    type="number"
                    placeholder="Enter team size"
                    min={MIN_TEAM_SIZE}
                    max={MAX_TEAM_SIZE}
                    className="text-center fw-bold p-0"
                    {...formik.getFieldProps("team_size")}
                  />
                  <div className="d-flex flex-column mt-3">
                    {renderError("team_size")}
                  </div>
                </Form.Group>
              </div>

              <div
                className="action-btns mt-1 d-flex flex-column"
                style={{ gap: "30px" }}
              >
                <Button
                  type="submit"
                  className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                  disabled={
                    formik.isSubmitting ||
                    formik.values.team_size === totalLicence
                  }
                >
                  {formik.isSubmitting ? <Spinner /> : "Next"}
                </Button>
                <div className="text-disclaimer mx-3 text-center">
                  Licenses are billed pro-rata, according to your billing
                  period, and are non-refundable. License count must be at least
                  equal to the number of active licenses. Blocked users are
                  counted as active licenses, until they are cancelled.
                </div>
              </div>
            </Form>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default UpgradeTeamModal;
