import { RiCloseLine } from "@remixicon/react";
import { useResetUserPasswordMutation } from "api";
import { PasswordField } from "components/FormFields";
import { Form, Formik, FormikProps } from "formik";
import { ResetPasswordInitialValues } from "formSchema/initialValues";
import { ResetPasswordValidations } from "formSchema/schemaValidations";
import React from "react";
import { <PERSON>ton, Modal, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";
import { useParams } from "react-router-dom";
import "./styles.scss";

interface ResetPasswordModalProps {
  show: boolean;
  setShow: React.Dispatch<React.SetStateAction<boolean>>;
}

const ResetPasswordModal: React.FC<ResetPasswordModalProps> = ({
  show,
  setShow,
}) => {
  const { id: user_id } = useParams();

  const { mutateAsync: resetPassword } = useResetUserPasswordMutation();

  const onClose = () => {
    setShow(false);
  };

  const handleSubmit = async (values: any, { setSubmitting }: any) => {
    try {
      const payload = {
        newPassword: values.password,
      };
      const response: any = await resetPassword({ user_id, payload });
      if (response?.success) {
        toast.success(response?.message || "Password reset successfully");
        onClose();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Modal show={show} onHide={onClose} centered>
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={onClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>

        <div
          className="auth-form d-flex justify-content-center align-items-stretch flex-column pass-reset-form"
          style={{ gap: "25px" }}
        >
          <div className="d-flex flex-column" style={{ gap: "15px" }}>
            <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
              Reset Password
            </h1>

            <p className="mb-0 auth-form-description font-gray text-center">
              Your new password must be different from previous
              <br className="d-lg-block d-none" />
              used passwords.
            </p>
          </div>

          <div className="website-form w-100">
            <Formik
              initialValues={ResetPasswordInitialValues}
              validationSchema={ResetPasswordValidations}
              onSubmit={handleSubmit}
            >
              {({ isSubmitting }: FormikProps<any>) => (
                <Form className="d-flex flex-column">
                  <div className="form-group position-relative">
                    <PasswordField
                      label="New Password*"
                      fieldName="password"
                      placeholder="Enter your password"
                    />
                  </div>

                  <div className="form-group position-relative">
                    <PasswordField
                      label="Confirm Password*"
                      fieldName="confirmPassword"
                      placeholder="Confirm your password"
                    />
                  </div>

                  <div className="action-btns d-flex flex-column">
                    <Button
                      type="submit"
                      className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? <Spinner /> : "Submit"}
                    </Button>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default ResetPasswordModal;
