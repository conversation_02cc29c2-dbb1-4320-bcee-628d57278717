import { Auth0ContextInterface, useAuth0 } from "@auth0/auth0-react";
import { LOGIN_TYPE } from "globals";
import { Button } from "react-bootstrap";
import useUserStore, { setUserInfo } from "stores/user";

const Auth0Login = () => {
  const { loginWithRedirect }: Auth0ContextInterface = useAuth0();
  const userInfo = useUserStore((state) => state.userInfo);

  return (
    <Button
      type="button"
      className="submit-btn position-relative w-100 border-brown bg-transparent text-uppercase font-primary"
      onClick={() => {
        setUserInfo({
          ...userInfo,
          loginType: LOGIN_TYPE.SSO,
        });
        loginWithRedirect();
      }}
    >
      Sign in with SSO
    </Button>
  );
};

export default Auth0Login;
