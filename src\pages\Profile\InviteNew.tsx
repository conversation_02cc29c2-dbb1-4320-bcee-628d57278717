import { Ri<PERSON>ailLine, RiUserLine } from "@remixicon/react";
import { useInviteNewMutation } from "api";
import { BackBreadcrumb, CustomDropdown } from "components";
import { ErrorMessage, Field, Form, Formik, FormikProps } from "formik";
import { InviteNewInitialValues } from "formSchema/initialValues/user";
import { InviteNewValidations } from "formSchema/schemaValidations/user";
import { USER_ROLE } from "globals";
import { <PERSON><PERSON>, Card, Col, Container, Row, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { getOrganisationInfo } from "stores/user";
import ProfilePicture from "./MyProfile/ProfilePicture";
import "./styles.scss";

const InviteNew = () => {
  const organization = getOrganisationInfo();
  const { mutateAsync: inviteNew } = useInviteNewMutation();
  const navigate = useNavigate();

  const handleSubmit = async (
    values: any,
    { setSubmitting, resetForm }: any,
  ) => {
    try {
      setSubmitting(true);
      const response: any = await inviteNew({
        id: organization?.id,
        payload: values,
      });
      if (response?.success) {
        toast.success(response?.message);
        resetForm();
        navigate(ROUTE_PATH.ORGANISATION_MANAGER);
      }
    } catch (error: any) {
      console.error(error?.response?.data?.message);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <main className="profile-section d-flex bg-white flex-column align-items-stretch w-100">
      <BackBreadcrumb />

      <div className="profile-section-form-container">
        <Container fluid>
          <Row className="justify-content-center align-items-center">
            <Col lg="9" xxl="7">
              <div className="auth-form w-100 m-0">
                <Card className="auth-form-card justify-content-center aligh-items-center">
                  <Card.Body className="d-flex gap-4 flex-column align-items-center justify-content-center">
                    <h1 className="auth-form-heading text-uppercase">
                      Invite New Member
                    </h1>

                    <div className="website-form w-100">
                      <Formik
                        initialValues={InviteNewInitialValues}
                        enableReinitialize
                        validationSchema={InviteNewValidations}
                        onSubmit={handleSubmit}
                      >
                        {({
                          isSubmitting,
                          setFieldValue,
                          values,
                        }: FormikProps<any>) => (
                          <Form
                            className="d-flex flex-column"
                            style={{ gap: "30px" }}
                          >
                            <div className="auth-form-file-input w-auto position-relative align-self-center">
                              <ProfilePicture
                                userPicture={organization?.logo}
                                title={organization?.title || ""}
                                name="logo"
                              />
                            </div>

                            <div className="form-group position-relative">
                              <label className="form-label">Full Name</label>
                              <Field
                                name="full_name"
                                className="form-control"
                                placeholder="Enter New Users Full Name"
                                type="text"
                              />
                              <ErrorMessage
                                component={"span"}
                                name="full_name"
                              />
                              <RiUserLine size={"20px"} color="#70828D" />
                            </div>

                            <div className="form-group position-relative">
                              <div className="d-flex justify-content-between align-items-center">
                                <label className="form-label">Email</label>
                              </div>
                              <Field
                                name="email"
                                className="form-control"
                                placeholder="Enter New Users Email"
                                type="email"
                              />
                              <ErrorMessage component={"span"} name="email" />
                              <RiMailLine size={"20px"} color="#70828D" />
                            </div>

                            <CustomDropdown
                              label="Choose Account Type"
                              title="Account Type"
                              items={USER_ROLE}
                              name="role"
                              onSelect={(value: string) =>
                                setFieldValue("role", value)
                              }
                              value={values.role}
                            />

                            <div
                              className="action-btns d-flex flex-column"
                              style={{ gap: "30px" }}
                            >
                              <Button
                                type="submit"
                                className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                                disabled={isSubmitting}
                              >
                                {isSubmitting ? <Spinner /> : "Invite"}
                              </Button>
                            </div>
                          </Form>
                        )}
                      </Formik>
                    </div>
                  </Card.Body>
                </Card>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </main>
  );
};

export default InviteNew;
