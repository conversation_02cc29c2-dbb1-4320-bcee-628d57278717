import { create } from "zustand";

interface UploadProgressState {
  totalFiles: number;
  uploadedFiles: number;
  isUploading: boolean;
  showWidget: boolean;
  setProgress: (totalFiles: number) => void;
  incrementUploaded: () => void;
  finishUpload: () => void;
  resetProgress: () => void;
  hideWidget: () => void;
  showWidgetFn: () => void;
}

const useUploadProgressStore = create<UploadProgressState>((set) => ({
  totalFiles: 0,
  uploadedFiles: 0,
  isUploading: false,
  showWidget: false,
  setProgress: (totalFiles) => set({ totalFiles, uploadedFiles: 0, isUploading: true, showWidget: true }),
  incrementUploaded: () => set((state) => ({ uploadedFiles: state.uploadedFiles + 1 })),
  finishUpload: () => set({ isUploading: false }),
  resetProgress: () => set({ totalFiles: 0, uploadedFiles: 0, isUploading: false, showWidget: false }),
  hideWidget: () => set({ showWidget: false }),
  showWidgetFn: () => set({ showWidget: true }),
}));

export default useUploadProgressStore; 