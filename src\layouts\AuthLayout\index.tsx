import { IMAGE_PATH } from "globals";
import React from "react";
import { Image } from "react-bootstrap";
import { Link, Outlet } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import "./styles.scss";

const AuthLayout: React.FC = () => {
  return (
    <section className="auth-layout d-flex flex-lg-row flex-column align-items-stretch justify-content-between">
      <div className="auth-background">
        <div className="auth-website-logo">
          <Link to={ROUTE_PATH.LOGIN} className="d-inline-block w-100 h-100">
            <Image
              src={IMAGE_PATH.websiteLogo}
              className="w-100 h-100 object-fit-contain"
              alt="logo"
              loading="eager"
            />
          </Link>
        </div>

        <div className="auth-background-featureImg position-absolute translate-middle-y d-lg-block d-none">
          <Image
            src={IMAGE_PATH.authFeatureImg}
            className="w-100 h-100 object-fit-contain"
            alt="feature"
          />
        </div>
      </div>

      <Outlet />
    </section>
  );
};

export default AuthLayout;
