import { RiUploadCloud2Fill } from "@remixicon/react";
import React from "react";
import useUploadProgressStore from "stores/upload";

const widgetStyle: React.CSSProperties = {
  position: "fixed",
  top: 24,
  left: "50%",
  transform: "translateX(-50%)",
  zIndex: 9999,
  background: "#fff",
  border: "1px solid #e0e0e0",
  borderRadius: 8,
  boxShadow: "0 2px 8px rgba(0,0,0,0.12)",
  padding: "16px 24px 16px 16px",
  display: "flex",
  alignItems: "center",
  gap: 12,
};

const reopenButtonStyle: React.CSSProperties = {
  position: "fixed",
  bottom: 24,
  right: 24,
  zIndex: 9999,
  background: "#1976d2",
  color: "#fff",
  border: "none",
  borderRadius: "50%",
  width: 48,
  height: 48,
  fontSize: 24,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  boxShadow: "0 2px 8px rgba(0,0,0,0.12)",
  cursor: "pointer",
};

const GlobalUploadProgress: React.FC = () => {
  const {
    totalFiles,
    uploadedFiles,
    isUploading,
    showWidget,
    // hideWidget,
    showWidgetFn,
  } = useUploadProgressStore();

  if (!showWidget && (isUploading || totalFiles > 0)) {
    return (
      <button
        style={reopenButtonStyle}
        onClick={showWidgetFn}
        title="Show upload progress"
      >
        <RiUploadCloud2Fill />
      </button>
    );
  }

  if (!showWidget) return null;

  return (
    <div style={widgetStyle}>
      <RiUploadCloud2Fill />
      <div style={{ flex: 1 }}>
        <strong>Uploading files:</strong>
        <div style={{ marginTop: 4 }}>
          {uploadedFiles} of {totalFiles} uploaded
        </div>
      </div>
      {/* <button onClick={hideWidget} title="Close">
        ×
      </button> */}
    </div>
  );
};

export default GlobalUploadProgress;
