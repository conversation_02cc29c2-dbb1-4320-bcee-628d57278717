import React, { FC, useMemo } from "react";

export interface PaymentItemInterface {
  description: string;
  quantity: number;
  amount: number;
  currency: string;
  currency_sign?: string;
}

interface DiscountInfo {
  amount: number;
  code: string;
  percent_off: number;
  amount_off: number;
}

export interface PlanInfo {
  sub_total?: number;
  applied_balance?: number;
  amount?: number;
  start_date?: string;
  expire_date?: string;
  discount?: DiscountInfo;
  total?: number;
  monthly_cost?: PaymentItemInterface[];
  amount_due?: string;
}

interface TableRowProps {
  description: string;
  quantity?: number | string;
  amount?: number | string;
  additionalClasses?: string;
}

interface DetailRowProps {
  label: string;
  value?: number | string;
  additionalClasses?: string;
}

interface SectionProps {
  title: string;
  centerTitle?: boolean;
  children: React.ReactNode;
}

const TableHeader: FC = () => (
  <div className="row fw-bold border-bottom pb-2 mb-2">
    <div className="col-6">Description</div>
    <div className="col-3 text-end">Quantity</div>
    <div className="col-3 text-end">Amount</div>
  </div>
);

const TableRow: FC<TableRowProps> = ({
  description,
  quantity = "-",
  amount = "-",
  additionalClasses = "",
}) => (
  <div className={`row py-2 border-bottom ${additionalClasses}`}>
    <div className="col-6">{description}</div>
    <div className="col-3 text-end">{quantity}</div>
    <div className="col-3 text-end">{amount}</div>
  </div>
);

const DetailRow: FC<DetailRowProps> = ({
  label,
  value = "-",
  additionalClasses = "",
}) => (
  <div className={`row fw-bold pt-3 ${additionalClasses}`}>
    <div className="col-6">{label}</div>
    <div className="col-6 text-end">{value}</div>
  </div>
);

const Section: FC<SectionProps> = ({
  title,
  centerTitle = false,
  children,
}) => (
  <div className="mb-4">
    <h5 className={`mb-3 fw-bold ${centerTitle ? "text-center" : ""}`}>
      {title}
    </h5>
    <div className="payment-table">{children}</div>
  </div>
);

export const MonthlyCostTable: FC<{ monthlyCost: PaymentItemInterface[] }> = ({
  monthlyCost,
}) => {
  return (
    <Section title="Monthly Cost">
      <TableHeader />
      {monthlyCost.map((item, index) => (
        <TableRow
          key={index}
          description={item.description}
          quantity={item.quantity}
          amount={item.amount}
        />
      ))}
    </Section>
  );
};

export const PaymentBreakdownTable: FC<{
  paymentItems: PaymentItemInterface[];
  planInfo?: PlanInfo;
  formatDate: (date: string) => string;
}> = ({ paymentItems, planInfo, formatDate }) => {
  const filteredItems = useMemo(
    () =>
      paymentItems.filter(
        (item) => !item.description?.startsWith("Remaining time"),
      ),
    [paymentItems],
  );

  const renderDiscountRow = () => {
    if (!planInfo?.discount?.code) return null;
    const { code, percent_off, amount_off, amount } = planInfo.discount;
    let label = code;
    if (percent_off) {
      label = `${code} (${percent_off}% off)`;
    } else if (amount_off) {
      label = `${code} (${amount_off} off)`;
    }
    return <DetailRow label={label} value={amount} />;
  };

  return (
    <Section title="Due Today">
      <TableHeader />
      {filteredItems.map((item, index) => (
        <TableRow
          key={index}
          description={item.description}
          quantity={item.quantity}
          amount={item.amount}
        />
      ))}
      <DetailRow label="Subtotal" value={planInfo?.sub_total} />
      {renderDiscountRow()}
      <DetailRow label="Total" value={planInfo?.total} />
      {planInfo?.applied_balance && (
        <DetailRow label="Applied Balance" value={planInfo.applied_balance} />
      )}
      <DetailRow label="Amount Due" value={planInfo?.amount_due} />
      {planInfo?.start_date && planInfo?.expire_date && (
        <DetailRow
          label="Billing Period"
          value={`${formatDate(planInfo.start_date)} - ${formatDate(planInfo.expire_date)}`}
        />
      )}
    </Section>
  );
};
