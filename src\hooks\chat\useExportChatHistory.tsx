import { RiShare2Fill } from "@remixicon/react";
import { useExportChatMutation } from "api";
import toast from "react-hot-toast";
import { setConfirmModalConfig } from "stores";

const REDACTION_CONFIRM_CONTENT = {
  heading: "Export Redaction File?",
  description:
    "We will generate a file for you, demonstrating the redacted values. You can submit this request once a week. We will send the file to your notifications.",
};

const CHAT_CONFIRM_CONTENT = {
  heading: "Export Chat File?",
  description:
    "We will generate a file for you. We will send the file to your notifications.",
};

const useExportChatHistory = ({ itemInfo }: any) => {
  const { mutateAsync: exportChat } = useExportChatMutation();

  const handleExport = async (id: string, type: string) => {
    const payload = {
      chat_id: id,
      redaction: type === "redaction",
    };
    try {
      const result: any = await exportChat(payload);
      if (result?.success) {
        toast.success(result?.message, {
          duration: 10000,
        });
      }
    } catch (err) {
      console.error(err);
    }
  };

  const onClickExport = (evt: React.MouseEvent, type: string) => {
    evt.stopPropagation();
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleExport(itemInfo?.chat_id, type),
        icon: RiShare2Fill,
        iconColor: "#ad986f",
        content:
          type === "chat" ? CHAT_CONFIRM_CONTENT : REDACTION_CONFIRM_CONTENT,
        showCloseIcon: true,
        buttonText: "Send Request",
        customStyling: {
          heading: {
            fontSize: "30px",
          },
        },
      },
    });
  };

  return { onClickExport };
};

export default useExportChatHistory;
