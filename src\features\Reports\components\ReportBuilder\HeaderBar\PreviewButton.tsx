import { RiEye2Fill } from "@remixicon/react";
import ActionButton from "features/Reports/components/ActionButton";
import { useState } from "react";
import ReportPreview from "../ReportPreview";

const PreviewButton = () => {
  const [showPreview, setShowPreview] = useState(false);

  const handleShowPreview = () => {
    setShowPreview(true);
  };
  return (
    <>
      <ActionButton
        icon={RiEye2Fill}
        onClick={handleShowPreview}
        title="Preview Report"
      />
      {showPreview && (
        <ReportPreview
          show={showPreview}
          onClose={() => setShowPreview(false)}
        />
      )}
    </>
  );
};

export default PreviewButton;
