import { useEffect } from "react";

const useAIResponseStatus = ({
  currentStatusIdx,
  setCurrentStatusIdx,
  setIsProcessing,
  isProcessing,
  idx,
  reid_text,
  loadingStatus,
}: any) => {
  useEffect(() => {
    if (!reid_text) {
      setIsProcessing({ [idx]: true });
    }
  }, [reid_text]);

  useEffect(() => {
    if (isProcessing[idx]) {
      const processStep = () => {
        if (reid_text) {
          setCurrentStatusIdx(() => loadingStatus.length - 1);
          setTimeout(
            () => {
              setIsProcessing({});
              setCurrentStatusIdx(0);
            },
            loadingStatus[loadingStatus.length - 1].seconds * 1000,
          );
        } else {
          setCurrentStatusIdx((prevIdx: any) => {
            return prevIdx === loadingStatus.length - 2 ? prevIdx : prevIdx + 1;
          });
        }
      };

      const stepDuration = loadingStatus[currentStatusIdx]?.seconds * 1000;

      const timeout = setTimeout(processStep, stepDuration);

      return () => clearTimeout(timeout);
    }
  }, [reid_text, isProcessing, currentStatusIdx]);

  const currentStatus = loadingStatus[currentStatusIdx]?.label;

  return {
    currentStatus,
    isProcessing: isProcessing[idx],
  };
};

export default useAIResponseStatus;
