.toolbar {
  &-wrapper {
    &-left {
      .toolbar-icon {
        width: 40px;
        height: 40px;
      }

      .title {
        font-size: 20px;
        font-weight: 700;
      }

      .description {
        font-size: 18px;
      }
    }

    &-right {
      .form-control {
        width: 350px;
        height: 45px;
        padding: 15px 15px 15px 40px;
        border-radius: 12px;
        border: 1px solid rgba(0, 0, 0, 0.2);
        background: #f9f9f9;
        font-size: 15px;

        @media only screen and (min-width: 1400px) and (max-width: 1599px) {
          width: 250px;
        }

        @media only screen and (max-width: 1399px) {
          width: 280px;
        }

        @media only screen and (max-width: 576px) {
          width: 100%;
        }
      }

      .search-floating-icon {
        left: 10px;
      }

      .dropdown-menu {
        top: 18px !important;
        position: relative;

        &::after {
          content: " ";
          width: 15px;
          height: 15px;
          position: absolute;
          left: 50%;
          transform: translateX(-50%) rotate(45deg) skew(20deg, 20deg);
          background-color: #0d3149;
          top: -7px;
        }
      }
    }

    @media only screen and (max-width: 576px) {
      &.branding-toolbar {
        .toolbar-wrapper-right {
          button {
            width: 100%;
          }
        }
      }
    }

    .camera-icon {
      bottom: 0;
      right: 0;
      transform: translate(20%, 20%);
    }
  }

  &-btn-blue {
    border-radius: 8px;
    box-shadow:
      0px 65px 47px 0px rgba(26, 26, 26, 0.02),
      0px 100px 80px 0px rgba(26, 26, 26, 0.05) !important;
    height: 45px;
    padding: 0px 17px;
    font-size: 14px;
  }

  &-btn-light {
    height: 45px;
    padding: 0px 17px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
    background: #e6e6e6 !important;
    box-shadow:
      0px 65px 47px 0px rgba(26, 26, 26, 0.02),
      0px 100px 80px 0px rgba(26, 26, 26, 0.05);
    color: #0d3149 !important;
    font-size: 14px;

    &.with-icon {
      height: 60px;
      padding: 0 20px;
      border-radius: 12px;
      background: #f9f9f9 !important;
      border: none !important;
      font-size: 16px;

      img {
        width: 35px;
        height: 35px;
        margin-right: 10px;
      }
    }

    @media only screen and (max-width: 575px) {
      width: 100%;
    }

    &.dropdown-toggle {
      &::after {
        margin-left: 7px;
        vertical-align: middle;
        border-width: 0.5em 0.5em 0 0.5em;
        border-radius: 10px;
      }
    }
  }
}
