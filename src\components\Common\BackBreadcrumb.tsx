import { RiArrowLeftLine } from "@remixicon/react";
import { Button } from "react-bootstrap";
import { useNavigate } from "react-router-dom";

const BackBreadcrumb = () => {
  const navigate = useNavigate();

  const handleBack = () => navigate(-1);
  return (
    <div className="breadcrumb-wrapper d-flex align-items-center justify-content-lg-between">
      <Button
        variant="link"
        onClick={handleBack}
        className="d-inline-block w-auto h-auto text-decoration-none fs-5 font-primary fw-medium"
      >
        <RiArrowLeftLine
          size={"24px"}
          className="me-2"
          style={{ verticalAlign: "sub" }}
        />
        Back
      </Button>
    </div>
  );
};

export default BackBreadcrumb;
