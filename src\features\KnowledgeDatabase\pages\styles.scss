@use "/src/styles/mixins/mixins.scss";

.knowledge-base-wrapper {
  overflow: hidden;
  position: relative;
  height: calc(100vh - 195px);
  border-radius: 12px;

  @media only screen and (max-width: 991px) {
    height: auto;
  }

  @include mixins.slim-scrollbar;

  .table-responsive {
    max-height: calc(100vh - 520px) !important;
  }

  .custom-table {
    thead {
      th {
        min-width: 200px;

        @media only screen and (max-width: 991px) {
          min-width: 200px;

          &:nth-child(2) {
            min-width: 400px;
          }
        }

        &:last-child {
          text-align: center;
        }
      }
    }
    @include mixins.table-td-text-overflow;

    @media only screen and (min-width: 992px) {
      @include mixins.table-body-fix-height;
    }
  }

  /* styles.scss */
  .knowledge-base-breadcrumbs {
    font-size: 14px;
    margin-bottom: 1rem;

    .breadcrumb-item {
      a {
        text-decoration: none;
      }
      &.active {
        color: #6c757d; /* <PERSON><PERSON>p's text-muted */
      }
      &:not(:last-child)::after {
        content: "/";
        margin: 0 0.5rem;
        color: #6c757d;
      }
    }
  }

  .upload-dropdown {
    .dropdown-toggle {
      background-color: #0d3149;
      border-color: #0d3149;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      height: 45px;
      font-size: 14px;
      font-weight: bold;

      &::after {
        content: "";
        display: none;
      }
    }

    .dropdown-menu {
      z-index: 9999;
      min-width: 180px;
      top: 5px !important;

      @media only screen and (min-width: 992px) {
        inset: 0px auto auto 0px !important;
        transform: translate(-90px, 45px) !important;
      }

      &::after {
        content: "";
        display: none;
      }
    }

    @media (max-width: 576px) {
      .dropdown-toggle {
        min-width: 85px;
      }
    }
  }

  .action-btns {
    .submit-btn {
      @include mixins.submit-btn;

      &.bg-transparent {
        @include mixins.submit-btn-transparent;
        @include mixins.button-layer-hover;
      }
    }
  }
}
