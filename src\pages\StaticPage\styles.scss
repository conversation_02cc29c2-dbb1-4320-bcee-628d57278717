.static-page {
  &-logo {
    background-image: url("assets/images/authBg.webp"),
      linear-gradient(180deg, #0d3149 -6.33%, #3c7981 99.98%);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    padding: 30px 0px;
    position: sticky;
    top: 0;
    z-index: 3;

    img {
      width: 500px;

      @media only screen and (max-width: 767px) {
        width: 300px;
      }
    }
  }

  &-content {
    h1 {
      font-size: 40px;
      margin: 0px;
      padding: 25px 0px;
      top: 143px;
      position: sticky;
      background-color: #f8f9fa;
      z-index: 1;

      @media only screen and (max-width: 767px) {
        top: 110px;
        font-size: 30px;
      }

      @media only screen and (max-width: 575px) {
        padding-bottom: 55px;
      }
    }

    h2,
    h3,
    h4,
    h6,
    h5 {
      font-size: 22px;
      font-weight: 600;

      @media only screen and (max-width: 575px) {
        font-size: 20px;
      }
    }

    p {
      font-size: 18px;
      text-wrap: initial;

      @media only screen and (max-width: 575px) {
        font-size: 15px;
      }
    }
  }

  .back-button {
    top: 180px;
    left: 90px;
    font-size: 18px;

    @media only screen and (max-width: 767px) {
      top: 143px;
      left: 20px;
    }

    @media only screen and (max-width: 575px) {
      left: 50%;
      transform: translateX(-50%);
      top: 185px;
    }
  }
}
