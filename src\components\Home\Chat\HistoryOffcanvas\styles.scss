@use "/src/styles/mixins/mixins.scss" as mixins;

.chat-history-offcanvas {
  border: none;

  @media only screen and (max-width: 991px) {
    max-width: 80%;
  }

  &-header {
    gap: 10px;

    hr {
      margin-bottom: 10px;
    }

    .btn-close {
      @include mixins.btn-close-style;
    }
  }

  &-body {
    overflow: hidden;

    .history-footer {
      gap: 15px;
      padding: 10px 10px;
      z-index: 9;

      &-profile {
        svg {
          border: 1px solid #0d3149;
          padding: 5px;
          margin-top: 5px;
        }
      }

      &-plans {
        gap: 10px;

        img {
          background-color: #ad986f;
          width: 35px;
          height: 35px;
        }
      }
    }
  }
}
