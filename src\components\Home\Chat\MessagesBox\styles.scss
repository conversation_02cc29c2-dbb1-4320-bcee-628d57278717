@use "/src/styles/mixins/mixins.scss" as mixins;

.message-box {
  flex: 1;
  border-radius: 12px;
  padding: 25px 25px 10px 25px;

  @media only screen and (min-width: 992px) {
    max-width: calc(100vw - 500px);
  }

  @media only screen and (max-width: 576px) {
    padding: 15px;
  }

  &-chat-wrapper {
    gap: 20px;

    &-filter {
      button {
        width: 50px;
        height: 50px;
        box-shadow: none !important;
        line-height: 1;

        @media only screen and (max-width: 576px) {
          width: 40px;
          min-width: 40px;
          height: 40px;
          padding: 0px;

          svg {
            width: 20px;
            height: 20px;
          }
        }
      }

      .private-ai-alert {
        display: flex;
        align-items: center;
        padding: 0.8rem;
        border-radius: 6px;
        color: white;
        margin-left: auto;
        margin-right: auto;

        .private-ai-alert-message {
          margin: 0;
          font-size: 16px;

          @media only screen and (max-width: 767px) {
            font-size: 14px;
            line-height: 20px;
          }

          strong {
            color: #ffe082;
          }
        }

        &.private-ai-alert-gradient {
          background: linear-gradient(90deg, #ff6b6b, #f94d6b);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .messages {
      gap: 50px;
      overflow-x: hidden;
      overflow-y: auto;
      padding-right: 20px;
      padding-bottom: 30px;

      @include mixins.slim-scrollbar;

      @media only screen and (max-width: 576px) {
        gap: 30px;
      }
    }

    &-input {
      bottom: 0;
      right: 0;
      left: 0;
      width: 100%;

      .form {
        &-switch {
          display: flex;
          justify-content: end;
          flex-direction: row-reverse;
          align-items: center;
          gap: 15px;
          padding: 15px 0px 0px 0px;

          input {
            margin: 0;
            width: 45px;
            height: 20px;
            border: 2px solid #0d3149;
            background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9Jy00IC00IDggOCc+PGNpcmNsZSByPSczJyBmaWxsPSdyZ2JhKDE3MywgMTUyLCAxMTEpJy8+PC9zdmc+DQoNCg==");

            &:checked {
              background-color: #0d3149;
            }

            &:focus {
              box-shadow: 0 0 0 0.25rem rgba(13, 49, 73, 0.25);
            }
          }

          label {
            font-size: 18px;
            font-weight: 500;
          }
        }
      }
    }
  }
}
