import { RiChat1Fill } from "@remixicon/react";
import TableSearch from "components/Common/Toolbar/TableSearch";
import FAQPromptModal from "features/KnowledgeDatabase/components/FAQPromptModal";
import { FilterInterface } from "features/KnowledgeDatabase/pages/KnowledgeBase";
import useFAQPromptActions from "features/KnowledgeDatabase/hooks/useFAQPromptActions";
import { useState } from "react";
import { Button, Form } from "react-bootstrap";
import "./styles.scss";

interface FAQPromptsToolbarProps {
  setFilters: (value: FilterInterface) => void;
  filters: FilterInterface;
}

export default function FAQPromptsToolbarProps({
  setFilters,
  filters,
}: FAQPromptsToolbarProps) {
  const [showFAQModal, setShowFAQModal] = useState(false);
  const { handleAddFAQPrompt } = useFAQPromptActions();

  const handleFAQPromptSave = async (data: any) => {
    const result = await handleAddFAQPrompt(data);
    if (result?.success) {
      setShowFAQModal(false);
    }
  };

  return (
    <div className="knowledge-base-toolbar">
      <div className="toolbar-wrapper d-flex flex-sm-row flex-column justify-content-sm-between justify-content-center align-items-stretch mb-3">
        <div className="toolbar-wrapper-left">
          <div className="icon d-inline-block lh-1 align-middle">
            <RiChat1Fill className="toolbar-icon object-fit-contain" />
          </div>

          <p className="title mb-0 ms-2 d-inline-block lh-1 align-middle">
            FAQ Prompts
          </p>

          <p className="mb-0 mt-1 description">Manage your FAQ prompts</p>
        </div>

        <div className="toolbar-wrapper-right mt-sm-0 mt-2 d-flex flex-lg-row flex-column justify-content-sm-end align-items-sm-center align-items-stretch gap-3">
          <Form
            action="#!"
            className="d-flex flex-lg-row flex-column justify-content-sm-end align-items-sm-center align-items-stretch"
            style={{ gap: "10px" }}
          >
            <TableSearch
              placeholder="Search by Title"
              setFilters={setFilters}
              filters={filters}
            />
          </Form>
          <Button
            className="lh-1 toolbar-btn-blue bg-blue border-blue text-center ms-sm-auto fw-bold"
            onClick={() => setShowFAQModal(true)}
          >
            Create FAQ Prompt
          </Button>
        </div>
      </div>

      {showFAQModal && (
        <FAQPromptModal
          show={showFAQModal}
          onClose={() => setShowFAQModal(false)}
          onSave={handleFAQPromptSave}
        />
      )}
    </div>
  );
}
