import { Button } from "react-bootstrap";

interface ActionButtonProps {
  icon: any;
  onClick?: () => void;
  title?: string;
  className?: string;
  iconProps?: any;
}

const ActionButton = ({
  icon,
  onClick = () => {},
  title,
  className = "",
  iconProps,
}: ActionButtonProps) => {
  const Icon = icon;
  return (
    <Button
      onClick={onClick}
      variant=""
      title={title}
      className={`w-100 py-2 bg-blue font-light border-blue text-decoration-none fw-bold d-flex justify-content-center align-items-center ${className}`}
    >
      <Icon {...iconProps} />
    </Button>
  );
};

export default ActionButton;
