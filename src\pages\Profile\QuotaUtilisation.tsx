import { RiArticleFill } from "@remixicon/react";
import { useGetChatQuota } from "api";
import { BackBreadcrumb } from "components";
import { getQuotaCardsConfig, IMAGE_PATH } from "globals";
import { useUserStatus } from "hooks";
import { Container, Row } from "react-bootstrap";
import SettingCard from "./SettingCard";
import "./styles.scss";

export default function QuotaUtilisation() {
  const { data: quotaData = {}, isLoading } = useGetChatQuota();
  const { isOrgAdmin, isOfflineAccount } = useUserStatus();

  const QUOTA_CARDS_CONFIG = getQuotaCardsConfig({
    quotaData,
    IMAGE_PATH,
    isLoading,
    isOrgAdmin,
    isOfflineAccount,
  });

  return (
    <main className="profile-section d-flex bg-white flex-column align-items-stretch w-100">
      <BackBreadcrumb />
      <div className="breadcrumb-wrapper d-flex flex-lg-row align-items-center justify-content-between">
        <div className="page-details d-flex align-items-lg-center justify-content-start">
          <RiArticleFill size={40} className="object-fit-contain" />

          <div className="page-details-page-name">
            <h3 className="mb-lg-2 mb-1 fw-bold">Quota Utilisation</h3>
            <p className="mb-0">View your current quota usage</p>
          </div>
        </div>
      </div>

      <hr className="m-0 border-0" />

      <div className="profile-section-action-card-list">
        <Container fluid>
          <Row className="gy-4">
            {QUOTA_CARDS_CONFIG.map((cardItem: any, idx: number) => {
              return (
                <SettingCard key={`setting-card-${idx}`} cardItem={cardItem} />
              );
            })}
          </Row>
        </Container>
      </div>
    </main>
  );
}
