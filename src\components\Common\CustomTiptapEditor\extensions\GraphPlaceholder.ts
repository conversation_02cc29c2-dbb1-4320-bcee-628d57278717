import { Node, mergeAttributes } from "@tiptap/core";

export interface GraphPlaceholderOptions {
  HTMLAttributes: Record<string, any>;
}

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    graphPlaceholder: {
      /**
       * Insert a graph placeholder
       */
      insertGraphPlaceholder: () => ReturnType;
    };
  }
}

export const GraphPlaceholder = Node.create<GraphPlaceholderOptions>({
  name: "graphPlaceholder",

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  group: "block",

  atom: false,

  selectable: false,
  isolating: true,

  content: "text*",

  addAttributes() {
    return {
      id: {
        default: null,
      },
      title: {
        default: "",
      },
      pastedContent: {
        default: "",
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="graph-placeholder"]',
        contentElement: 'div.graph-placeholder-paste-area',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "div",
      mergeAttributes(
        {
          "data-type": "graph-placeholder",
          class: "graph-placeholder",
        },
        this.options.HTMLAttributes,
        HTMLAttributes
      ),
      [
        "div",
        { class: "graph-placeholder-content" },
        ["div", { class: "graph-placeholder-icon" }, "📊"],
        ["div", { class: "graph-placeholder-text" }, HTMLAttributes.title || ""],
        ["div", { class: "graph-placeholder-subtitle" }, ""],
        [
          "div",
          { class: "graph-placeholder-instruction" },
          "Paste content here",
        ],
        [
          "div",
          {
            class: "graph-placeholder-paste-area",
            "data-placeholder": HTMLAttributes.pastedContent ? "" : "",
          },
          HTMLAttributes.pastedContent || "",
        ],
      ],
    ];
  },

  addCommands() {
    return {
      insertGraphPlaceholder:
        () =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: {
              id: `graph-${Date.now()}`,
              title: "",
              pastedContent: "",
            },
          });
        },
    };
  },

  addNodeView() {
    return ({ node, getPos, editor }) => {
      const dom = document.createElement("div");
      dom.classList.add("graph-placeholder");
      dom.dataset.type = "graph-placeholder";

      const content = document.createElement("div");
      content.classList.add("graph-placeholder-content");

      const icon = document.createElement("div");
      icon.className = "graph-placeholder-icon";
      icon.textContent = "📊";

      const title = document.createElement("div");
      title.className = "graph-placeholder-text";

      const subtitle = document.createElement("div");
      subtitle.className = "graph-placeholder-subtitle";

      const instruction = document.createElement("div");
      instruction.className = "graph-placeholder-instruction fw-bold";
      instruction.textContent = "Paste content here";

      const pasteArea = document.createElement("div");
      pasteArea.className = "graph-placeholder-paste-area";
      pasteArea.contentEditable = "false"; // Disable manual typing
      pasteArea.dataset.pasteOnly = "true";
      pasteArea.textContent = node.attrs.pastedContent || "";
      pasteArea.dataset.placeholder = node.attrs.pastedContent ? "" : "";

      // Update placeholder visibility
      const updatePlaceholder = () => {
        pasteArea.dataset.placeholder = pasteArea.textContent?.trim() ? "" : "";
      };

      // Prevent typing manually (redundant with contentEditable="false", but kept for robustness)
      pasteArea.addEventListener("beforeinput", (e) => {
        if (e.inputType !== "insertFromPaste") {
          e.preventDefault();
        }
      });

      // Handle paste event
      pasteArea.addEventListener("paste", (e) => {
        e.preventDefault();
        e.stopPropagation();
        const pastedText = e.clipboardData?.getData("text/plain") || "";

        if (pastedText) {
          // Get the position of the current node
          const pos = getPos();

          // Create a transaction to replace the GraphPlaceholder node with pasted content
          const transaction = editor.state.tr.replaceWith(
            pos,
            pos + 1, // Replace the entire node
            editor.state.schema.nodes.paragraph.create(
              {},
              editor.state.schema.text(pastedText)
            )
          );

          // Dispatch the transaction to update the editor
          editor.view.dispatch(transaction);

          // Optionally, focus the editor at the end of the inserted content
          editor.commands.focus(pos + pastedText.length);
        }
      });

      // Focus styling
      pasteArea.addEventListener("click", () => {
        pasteArea.classList.add("is-focused");
        editor.commands.focus(getPos());
      });

      pasteArea.addEventListener("blur", () => {
        pasteArea.classList.remove("is-focused");
        updatePlaceholder();
      });

      // Keep placeholder updated on editor changes
      editor.on("update", updatePlaceholder);

      content.append(icon, instruction, title, subtitle, pasteArea);
      dom.append(content);

      return {
        dom,
        contentDOM: pasteArea,
        update: (updatedNode) => {
          if (updatedNode.type !== node.type) return false;
          pasteArea.textContent = updatedNode.attrs.pastedContent || "";
          updatePlaceholder();
          return true;
        },
        destroy: () => {
          editor.off("update", updatePlaceholder);
        },
      };
    };
  },
});

export default GraphPlaceholder;