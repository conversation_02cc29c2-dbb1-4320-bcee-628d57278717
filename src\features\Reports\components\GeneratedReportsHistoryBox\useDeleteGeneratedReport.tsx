import { useDeleteGeneratedReportMutation } from "features/Reports/api";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import { setConfirmModalConfig } from "stores";

const useDeleteGeneratedReport = ({
  itemInfo,
  setHistoryList,
  setTotalCount,
}: any) => {
  const { id: paramsChatId } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { mutateAsync: deleteHistory } = useDeleteGeneratedReportMutation();

  const handleDelete = async (id: string) => {
    try {
      const result: any = await deleteHistory(id);
      if (result?.success) {
        toast.success(result?.message || "Report Deleted Successfully!");
        setHistoryList((prev: any) =>
          prev.filter((item: any) => item.id !== id)
        );
        setTotalCount((prev: any) => prev - 1);
        if (paramsChatId === id) {
          navigate(REPORTS_ROUTE_PATH.GENERATE_REPORT);
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const onClickDelete = (evt: React.MouseEvent) => {
    evt.stopPropagation();
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleDelete(itemInfo.id),
      },
    });
  };

  return { onClickDelete };
};

export default useDeleteGeneratedReport;
