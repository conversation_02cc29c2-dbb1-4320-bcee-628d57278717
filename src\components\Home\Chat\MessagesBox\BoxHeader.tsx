import { RiEqualizerFill } from "@remixicon/react";
import WarningBar from "components/Common/WarningBar";
import { Button } from "react-bootstrap";
import useUtilStore from "stores/util";

const BoxHeader = ({ filterRef, handleShow }: any) => {
  const { enable_privacy } = useUtilStore((state) => state.entityConfiguration);
  return (
    <div
      ref={filterRef}
      className="message-box-chat-wrapper-filter d-flex align-items-center justify-content-end justify-content-start gap-3"
    >
      {!enable_privacy && (
        <WarningBar message="The Privacy Filter is disabled, potentially exposing your sensitive information to external entities." />
      )}
      <Button
        variant="primary"
        className="bg-blue border-blue p-lg-0"
        onClick={handleShow}
      >
        <RiEqualizerFill size={"24px"} color="#F9F9F9" />
      </Button>
    </div>
  );
};

export default BoxHeader;
