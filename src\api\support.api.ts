import { useMutation } from "@tanstack/react-query";
import { apiClient } from "./apiClient";
import { API_ENDPOINTS } from "globals";

export const useSuggestFeatureMutation = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      const response = await apiClient.post(
        API_ENDPOINTS.SUGGEST_FEATURE,
        payload,
      );
      return response;
    },
  });

export const useReportBugMutation = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      const response = await apiClient.post(API_ENDPOINTS.REPORT_BUG, payload);
      return response;
    },
  });
