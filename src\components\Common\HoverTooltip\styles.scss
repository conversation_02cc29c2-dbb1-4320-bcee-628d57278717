.tooltip {
  opacity: 1 !important;

  &-inner {
    background-color: #ffffff;
    text-align: left;
    border: 1px solid #f7f5f1;
    font-size: 14px;
    color: #0d3149;

    @media only screen and (max-width: 576px) {
      font-size: 16px;
    }
  }

  &-custom-width {
    .tooltip-inner {
      background-color: #ffffff;
      width: 400px;
      max-width: inherit;
      text-align: left;
      padding: 10px 20px 20px 20px;
      font-weight: bold;
      font-size: 16px;

      @media only screen and (max-width: 576px) {
        width: 330px;
        font-size: 16px;
        padding: 10px 10px;
      }

      ul {
        margin-bottom: 0;
        font-weight: 400;
        margin-top: 20px;
        font-size: 16px;
        padding-left: 20px;

        @media only screen and (max-width: 576px) {
          font-size: 14px;
        }

        li:not(:last-child) {
          margin-bottom: 10px;
        }
      }
    }
  }

  &.bs-tooltip-bottom .tooltip-arrow::before {
    top: 1px;
    width: 10px;
    height: 10px;
    border: none;
    border-left: 1px solid #f7f5f1;
    border-top: 1px solid #f7f5f1;
    background-color: #ffffff;
    transform: rotate(45deg);
  }
}
