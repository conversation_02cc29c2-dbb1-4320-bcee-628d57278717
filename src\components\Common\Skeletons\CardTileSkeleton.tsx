import { Placeholder } from "react-bootstrap";

const CardTileSkeleton = () => {
  return (
    <li className="d-flex flex-lg-row flex-column justify-content-between align-items-lg-center align-items-start position-relative p-3 border rounded">
      <div className="card-data w-100 d-flex justify-content-start align-items-center flex-grow-1 gap-4">
        <Placeholder as="div" animation="glow">
          <Placeholder
            className="rounded-2 d-flex align-items-center justify-content-center"
            style={{ width: "60px", height: "60px" }}
          >
            <Placeholder xs={6} />
          </Placeholder>
        </Placeholder>

        <div className="card-data-details flex-grow-1">
          <Placeholder
            as="small"
            animation="glow"
            className="font-gray fw-medium d-block"
          >
            <Placeholder xs={8} />
          </Placeholder>
          <Placeholder as="p" animation="glow" className="mb-1">
            <Placeholder xs={6} />
          </Placeholder>
        </div>
      </div>
    </li>
  );
};

export default CardTileSkeleton;
