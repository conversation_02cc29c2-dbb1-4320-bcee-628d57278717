import { RiFile2Line, RiFolder2Line } from "@remixicon/react";
import { useRef } from "react";
import { CustomDropdown } from "components";
import useFolderUpload from "features/KnowledgeDatabase/hooks/useFolderUpload";

interface UploadDropdownProps {
  className?: string;
  owner_type: string;
}

export default function UploadDropdown({
  className,
  owner_type,
}: UploadDropdownProps) {
  const folderInputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { handleFolderUpload } = useFolderUpload();

  const handleUploadOption = (value: string) => {
    if (value === "folder") {
      folderInputRef.current?.click();
    } else if (value === "file") {
      fileInputRef.current?.click();
    }
  };

  const onFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFolderUpload(e, owner_type);
    e.target.value = "";
  };

  return (
    <>
      <input
        ref={folderInputRef}
        type="file"
        onChange={onFileChange}
        style={{ display: "none" }}
        multiple
        {...({
          webkitdirectory: "true",
          directory: "true",
          mozdirectory: "true",
        } as any)}
      />

      <input
        ref={fileInputRef}
        type="file"
        onChange={onFileChange}
        style={{ display: "none" }}
        multiple
      />

      <CustomDropdown
        title="Upload"
        items={[
          {
            label: (
              <span className="d-inline-flex align-items-center gap-2">
                <RiFolder2Line size={18} />
                Upload Folder
              </span>
            ),
            value: "folder",
          },
          {
            label: (
              <span className="d-inline-flex align-items-center gap-2">
                <RiFile2Line size={18} />
                Upload Files
              </span>
            ),
            value: "file",
          },
        ]}
        onSelect={handleUploadOption}
        preserveTitle
        className={className}
      />
    </>
  );
}
