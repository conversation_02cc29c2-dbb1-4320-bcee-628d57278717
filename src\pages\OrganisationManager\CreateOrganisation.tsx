import { Container } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";
import { ROUTE_PATH } from "routes";

const CreateOrganisation = () => {
  return (
    <Container fluid className="h-75 d-flex">
      <div className="p-5 text-center w-100 d-flex justify-content-center align-items-center flex-column gap-3">
        <h1 className="auth-form-heading text-uppercase">
          Get Started with Your Organisation
        </h1>
        <p className="auth-form-description">
          Set up your organisation’s profile to collaborate and manage your team
          efficiently.
        </p>
        <div className="action-btns">
          <Link
            to={ROUTE_PATH.ORGANISATIONAL_PROFILE}
            className="btn submit-btn bg-brown border-brown text-uppercase font-light px-5 d-flex justify-content-center align-items-center"
          >
            Set Up Profile
          </Link>
        </div>
      </div>
    </Container>
  );
};

export default CreateOrganisation;
