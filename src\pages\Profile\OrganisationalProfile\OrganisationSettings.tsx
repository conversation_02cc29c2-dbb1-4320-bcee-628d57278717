import { Form } from "react-bootstrap";

export default function OrganisationSettings({
  formik,
  handleSwitchChange,
  ORGANISATION_SETTINGS,
}: any) {
  return (
    <div className="form-group position-relative">
      <div className="d-flex justify-content-between align-items-center">
        <label className="form-label fw-bold">Settings</label>
      </div>
      <div className="ms-3">
        {formik.values.settings &&
          Object.entries(formik.values.settings).map(([key, value]: any) => (
            <div
              key={key}
              className="d-flex justify-content-between align-items-center"
            >
              <label className="form-label">
                {ORGANISATION_SETTINGS[key].label}
              </label>
              <Form.Switch
                checked={value}
                onChange={(e) =>
                  handleSwitchChange({
                    key,
                    checked: e.target.checked,
                    formik,
                  })
                }
              />
            </div>
          ))}
      </div>
    </div>
  );
}
