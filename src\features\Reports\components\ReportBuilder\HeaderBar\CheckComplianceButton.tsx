import { RiSurveyLine } from "@remixicon/react";
import ActionButton from "features/Reports/components/ActionButton";
import useReportStore from "features/Reports/store/report";
import { useState } from "react";
import ComplianceModal from "../ComplianceModal";
import { useTiptapConverter } from "../hooks/useTiptapConverter";

const CheckComplianceButton = () => {
  const [showComplianceModal, setShowComplianceModal] = useState(false);
  const [frozenPayload, setFrozenPayload] = useState<any>(null);

  const reportInfo = useReportStore((state) => state.reportInfo);
  const { convertSections } = useTiptapConverter();

  const onComplianceClick = () => {
    const payload = {
      title: reportInfo.title,
      sections: convertSections(reportInfo.sections, "html"),
    };
    setFrozenPayload(payload);
    setShowComplianceModal(true);
  };

  const onClose = () => {
    setShowComplianceModal(false);
    setFrozenPayload(null);
  };

  return (
    <>
      <ActionButton
        icon={RiSurveyLine}
        onClick={onComplianceClick}
        title="Check Suitability Compliance"
      />
      {showComplianceModal && frozenPayload && (
        <ComplianceModal
          show={showComplianceModal}
          onClose={onClose}
          payload={frozenPayload}
        />
      )}
    </>
  );
};

export default CheckComplianceButton;
