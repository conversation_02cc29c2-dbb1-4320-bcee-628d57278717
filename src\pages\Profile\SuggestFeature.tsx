import { useSuggestFeatureMutation } from "api";
import { BackBreadcrumb } from "components";
import { SuggestFeatureInitialValues } from "formSchema/initialValues";
import { SuggestFeatureValidations } from "formSchema/schemaValidations";
import { ErrorMessage, Field, Form, Formik, FormikProps } from "formik";
import { <PERSON><PERSON>, Card, Col, Container, Row, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";
import "./styles.scss";

const SuggestFeature = () => {
  const { mutateAsync: suggestFeature } = useSuggestFeatureMutation();

  const handleSubmit = async (
    values: any,
    { setSubmitting, resetForm }: any,
  ) => {
    try {
      const response: any = await suggestFeature(values);
      if (response?.success) {
        toast.success(response?.message);
        resetForm();
      }
    } catch (error: any) {
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <main className="profile-section d-flex bg-white flex-column align-items-stretch w-100">
      <BackBreadcrumb />
      <div className="profile-section-form-container">
        <Container fluid>
          <Row className="justify-content-center align-items-center">
            <Col lg="9" xxl="7">
              <div className="auth-form w-100 m-0">
                <Card className="auth-form-card justify-content-center aligh-items-center">
                  <Card.Body className="d-flex gap-4 flex-column align-items-center justify-content-center">
                    <div className="d-flex flex-column" style={{ gap: "10px" }}>
                      <h1 className="mb-0 text-center auth-form-heading text-uppercase fw-bold">
                        suggest a feature
                      </h1>
                    </div>

                    <div className="website-form w-100">
                      <Formik
                        initialValues={SuggestFeatureInitialValues}
                        validationSchema={SuggestFeatureValidations}
                        onSubmit={handleSubmit}
                      >
                        {({ isSubmitting }: FormikProps<any>) => (
                          <Form
                            className="d-flex flex-column"
                            style={{ gap: "30px" }}
                          >
                            <div className="form-group position-relative">
                              <label className="form-label fw-bold">
                                Title
                              </label>
                              <Field
                                name="title"
                                className="form-control"
                                placeholder="Enter title"
                                type="text"
                              />
                              <ErrorMessage component={"span"} name="title" />
                            </div>

                            <div className="form-group position-relative">
                              <label className="form-label fw-bold">
                                Description
                              </label>
                              <Field
                                name="description"
                                className="form-control pt-2"
                                placeholder="Enter description"
                                type="text"
                                as="textarea"
                              />
                              <ErrorMessage
                                component={"span"}
                                name="description"
                              />
                            </div>

                            <div
                              className="action-btns d-flex flex-column"
                              style={{ gap: "30px" }}
                            >
                              <Button
                                type="submit"
                                className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                                disabled={isSubmitting}
                              >
                                {isSubmitting ? <Spinner /> : "Submit"}
                              </Button>
                            </div>
                          </Form>
                        )}
                      </Formik>
                    </div>
                  </Card.Body>
                </Card>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </main>
  );
};

export default SuggestFeature;
