import { RiDeleteBin5Fill } from "@remixicon/react";
import { DOC_TYPE_INFO } from "globals";
import { useDeleteDoc } from "hooks";
import { Button, Image } from "react-bootstrap";

const DocumentCard = ({ metadata, msgId }: any) => {
  const { onClickDelete } = useDeleteDoc({
    metadata,
    msgId,
  });

  return (
    <div className="user-message-file d-flex justify-content-start align-items-center ms-auto position-relative">
      {metadata?.doc_id && (
        <Button
          variant="link"
          className="remove-btn p-0"
          onClick={onClickDelete}
        >
          <RiDeleteBin5Fill size={"20px"} color="red" />
        </Button>
      )}
      <Image
        src={DOC_TYPE_INFO?.[metadata?.type]?.icon}
        className="object-fit-contain"
      />

      <hr className="my-1 p-0 align-self-stretch opacity-100" />

      <p className="mb-0 fw-bold text-start text-truncate">
        {metadata?.name}
        <hr className="border-0 my-1" />
        <small className="fw-medium text-uppercase">
          {DOC_TYPE_INFO?.[metadata?.type]?.type}
        </small>
      </p>
    </div>
  );
};

export default DocumentCard;
