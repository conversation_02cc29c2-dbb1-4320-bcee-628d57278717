import { RiInboxArchiveFill, RiInboxUnarchiveFill } from "@remixicon/react";
import { useArchiveReportMutation } from "features/Reports/api";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import { setConfirmModalConfig } from "stores";

const useArchiveReport = ({ itemInfo, setHistoryList, setTotalCount }: any) => {
  const { mutateAsync: archiveHistory } = useArchiveReportMutation();
  const navigate = useNavigate();
  const { id: paramsChatId } = useParams<{ id: string }>();

  const handleArchive = async (id: string) => {
    try {
      const result: any = await archiveHistory(id);
      if (result?.success) {
        toast.success(result?.message || "Report Archived Successfully!");
        setHistoryList((prev: any) =>
          prev.filter((item: any) => item.id !== id),
        );
        setTotalCount((prev: any) => prev - 1);
        if (paramsChatId === id) {
          navigate(REPORTS_ROUTE_PATH.BUILD_REPORTS);
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const onClickArchive = (evt: React.MouseEvent) => {
    evt.stopPropagation();
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleArchive(itemInfo.id),
        content: {
          heading: itemInfo?.is_archived ? "Unarchive" : "Archive",
          description: `Are you sure you want to ${
            itemInfo?.is_archived ? "unarchive" : "archive"
          } this report?`,
        },
        icon: itemInfo?.is_archived ? RiInboxUnarchiveFill : RiInboxArchiveFill,
        iconColor: "#ad986f",
      },
    });
  };

  return { onClickArchive };
};

export default useArchiveReport;
