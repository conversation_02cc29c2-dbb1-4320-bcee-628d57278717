import { PRESET_OPTIONS } from "globals";
import { useMemo, useState } from "react";

interface UseEntityItemActionsParams {
  activePreset: string;
  entityItem: any;
  localConfiguration: any;
  setLocalConfiguration: (config: any) => void;
}

const useEntityItemActions = ({
  activePreset,
  entityItem,
  localConfiguration,
  setLocalConfiguration,
}: UseEntityItemActionsParams) => {
  const [expanded, setExpanded] = useState<string | null>(null);

  const { preset = [], enable_privacy: isPrivacyEnabled = false } =
    localConfiguration ?? {};

  const { availableEntities, comingSoonEntities } = useMemo(() => {
    return entityItem?.entities?.reduce(
      (acc: any, entity: any) => {
        if (entity.upcoming) {
          acc.comingSoonEntities.push(entity);
        } else {
          acc.availableEntities.push(entity);
        }
        return acc;
      },
      { availableEntities: [], comingSoonEntities: [] },
    );
  }, [entityItem?.entities]);

  const privateEntities: any[] = useMemo(
    () =>
      preset?.find((item: any) => item.type === activePreset)?.entity_values ||
      [],
    [preset, activePreset],
  );

  const isAllEntitiesComingSoon = useMemo(
    () => entityItem?.entities?.every((entity: any) => entity?.upcoming),
    [entityItem],
  );

  const isFieldDisabled =
    !activePreset ||
    !isPrivacyEnabled ||
    localConfiguration?.preset?.some((item: any) =>
      [PRESET_OPTIONS.basic.key, PRESET_OPTIONS.full.key].includes(item.type),
    );

  const checkCategorySelected = () => {
    if (!availableEntities || availableEntities.length === 0) {
      return false;
    }

    return availableEntities.every((entity: any) =>
      privateEntities?.includes(entity?.value),
    );
  };

  const handleToggle = (currentKey: string) => {
    setExpanded(expanded === currentKey ? null : currentKey);
  };

  const updatePreset = (updatedEntities: any[]) => {
    const newPreset = preset.map((item: any) =>
      item.type === activePreset
        ? { ...item, entity_values: updatedEntities }
        : item,
    );
    setLocalConfiguration({ ...localConfiguration, preset: newPreset });
  };

  const onChangeCategory = (evt: any, entityItem: any) => {
    let updatedEntities = [...privateEntities];
    if (evt.target.checked) {
      entityItem.entities.forEach((entity: any) => {
        if (!updatedEntities.includes(entity.value) && !entity.upcoming) {
          updatedEntities.push(entity.value);
        }
      });
    } else {
      updatedEntities = updatedEntities.filter(
        (entity) =>
          !entityItem.entities.some((item: any) => item.value === entity),
      );
    }

    updatePreset(updatedEntities);
  };

  const onChangeEntity = (evt: any) => {
    const updatedEntities = evt.target.checked
      ? [...privateEntities, evt.target.value]
      : privateEntities.filter((item) => item !== evt.target.value);

    updatePreset(updatedEntities);
  };

  return {
    availableEntities,
    checkCategorySelected,
    comingSoonEntities,
    expanded,
    isAllEntitiesComingSoon,
    isFieldDisabled,
    handleToggle,
    onChangeEntity,
    onChangeCategory,
    privateEntities,
  };
};

export default useEntityItemActions;
